/**
 * RunRewards.js
 * System for managing temporary cards, upgrades, and loot during dungeon runs
 */

export class RunRewards {
    constructor(cardManager, playerDataManager) {
        this.cardManager = cardManager;
        this.playerDataManager = playerDataManager;
        
        // Temporary card pools for runs
        this.temporaryCardPools = {
            common: [
                {
                    name: "Dungeon Strike",
                    type: "attack",
                    cost: 2,
                    damage: 12,
                    description: "A powerful strike learned in the depths"
                },
                {
                    name: "Stone Shield",
                    type: "defense",
                    cost: 1,
                    block: 8,
                    description: "Protective barrier of dungeon stone"
                },
                {
                    name: "Quick Heal",
                    type: "utility",
                    cost: 1,
                    heal: 6,
                    description: "Rapid healing from dungeon herbs"
                },
                {
                    name: "Energy Surge",
                    type: "utility",
                    cost: 0,
                    energy: 2,
                    description: "Burst of magical energy"
                }
            ],
            uncommon: [
                {
                    name: "Shadow Blade",
                    type: "attack",
                    cost: 3,
                    damage: 18,
                    effect: "Ignores armor",
                    description: "Ethereal weapon that cuts through defenses"
                },
                {
                    name: "Mystic Barrier",
                    type: "defense",
                    cost: 2,
                    block: 12,
                    effect: "Reflects 25% damage",
                    description: "Magical shield that turns attacks back"
                },
                {
                    name: "Vampiric Strike",
                    type: "attack",
                    cost: 3,
                    damage: 15,
                    effect: "Heal for 50% damage dealt",
                    description: "Life-draining attack"
                },
                {
                    name: "Time Warp",
                    type: "utility",
                    cost: 2,
                    effect: "Take an extra turn",
                    description: "Manipulate time to act again"
                }
            ],
            rare: [
                {
                    name: "Void Blast",
                    type: "attack",
                    cost: 4,
                    damage: 25,
                    effect: "Deals true damage",
                    description: "Pure destructive force from the void"
                },
                {
                    name: "Phoenix Rebirth",
                    type: "utility",
                    cost: 3,
                    effect: "Revive with full health when defeated",
                    description: "Rise from the ashes of defeat"
                },
                {
                    name: "Reality Tear",
                    type: "utility",
                    cost: 5,
                    effect: "Remove all enemy buffs and deal 20 damage",
                    description: "Rip through the fabric of reality"
                },
                {
                    name: "Divine Intervention",
                    type: "defense",
                    cost: 2,
                    effect: "Become immune to damage this turn",
                    description: "Call upon divine protection"
                }
            ],
            legendary: [
                {
                    name: "Omnislash",
                    type: "attack",
                    cost: 6,
                    damage: 40,
                    effect: "Hits all enemies",
                    description: "Ultimate technique that strikes all foes"
                },
                {
                    name: "Temporal Mastery",
                    type: "utility",
                    cost: 4,
                    effect: "Take 3 extra turns",
                    description: "Complete control over time itself"
                },
                {
                    name: "Soul Storm",
                    type: "attack",
                    cost: 5,
                    damage: 30,
                    effect: "Damage increases with each enemy defeated",
                    description: "Harness the power of fallen souls"
                }
            ]
        };
        
        // Card upgrade types
        this.upgradeTypes = {
            damage: {
                name: "Damage Boost",
                description: "Increase damage by 50%",
                apply: (card) => {
                    if (card.damage) card.damage = Math.floor(card.damage * 1.5);
                }
            },
            cost: {
                name: "Cost Reduction",
                description: "Reduce energy cost by 1 (minimum 0)",
                apply: (card) => {
                    card.cost = Math.max(0, card.cost - 1);
                }
            },
            effect: {
                name: "Enhanced Effect",
                description: "Improve card's special effects",
                apply: (card) => {
                    if (card.heal) card.heal = Math.floor(card.heal * 1.5);
                    if (card.block) card.block = Math.floor(card.block * 1.5);
                    if (card.energy) card.energy += 1;
                }
            },
            duplicate: {
                name: "Duplicate",
                description: "Add a copy of this card to your deck",
                apply: (card, runData) => {
                    const copy = { ...card, id: `${card.id}_copy_${Date.now()}` };
                    runData.temporaryCards.push(copy);
                }
            }
        };
        
        // Artifacts that provide passive benefits
        this.artifactPool = [
            {
                name: "Ring of Might",
                description: "All attack cards deal +3 damage",
                effect: "damage_boost",
                value: 3,
                rarity: "common"
            },
            {
                name: "Amulet of Warding",
                description: "All defense cards block +2 damage",
                effect: "block_boost",
                value: 2,
                rarity: "common"
            },
            {
                name: "Crystal of Energy",
                description: "Start each combat with +1 energy",
                effect: "energy_boost",
                value: 1,
                rarity: "uncommon"
            },
            {
                name: "Boots of Swiftness",
                description: "Draw +1 card at start of turn",
                effect: "card_draw",
                value: 1,
                rarity: "uncommon"
            },
            {
                name: "Crown of Mastery",
                description: "All cards cost 1 less energy (minimum 0)",
                effect: "cost_reduction",
                value: 1,
                rarity: "rare"
            },
            {
                name: "Heart of the Phoenix",
                description: "Heal 5 HP at the start of each combat",
                effect: "combat_heal",
                value: 5,
                rarity: "rare"
            },
            {
                name: "Void Shard",
                description: "Critical hits deal double damage",
                effect: "critical_boost",
                value: 2,
                rarity: "legendary"
            },
            {
                name: "Time Crystal",
                description: "25% chance to not consume energy when playing cards",
                effect: "energy_refund",
                value: 0.25,
                rarity: "legendary"
            }
        ];
        
        console.log("🎁 RunRewards system initialized");
    }
    
    /**
     * Generate rewards for completing a floor
     */
    generateFloorRewards(floorType, difficulty, runData) {
        const rewards = {
            gold: 0,
            cards: [],
            upgrades: [],
            artifacts: [],
            choices: []
        };
        
        // Base gold reward
        const baseGold = {
            combat: 15,
            elite: 30,
            boss: 60,
            event: 10,
            shop: 0
        };
        
        rewards.gold = Math.floor((baseGold[floorType] || 15) * (1 + difficulty * 0.1));
        
        // Card rewards based on floor type
        switch (floorType) {
            case "combat":
                if (Math.random() < 0.7) {
                    rewards.cards.push(this.generateRandomCard("common", runData));
                }
                break;
                
            case "elite":
                rewards.cards.push(this.generateRandomCard("uncommon", runData));
                if (Math.random() < 0.4) {
                    rewards.upgrades.push(this.generateUpgradeChoice(runData));
                }
                break;
                
            case "boss":
                rewards.cards.push(this.generateRandomCard("rare", runData));
                rewards.artifacts.push(this.generateRandomArtifact("rare"));
                rewards.choices.push(this.generateRewardChoice(runData));
                break;
        }
        
        return rewards;
    }
    
    /**
     * Generate a random temporary card
     */
    generateRandomCard(rarity, runData) {
        const pool = this.temporaryCardPools[rarity] || this.temporaryCardPools.common;
        const template = pool[Math.floor(Math.random() * pool.length)];
        
        // Create unique card instance
        const card = {
            ...template,
            id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            temporary: true,
            rarity: rarity,
            runId: runData.playerId
        };
        
        // Apply class-specific modifications
        this.applyClassModifications(card, runData);
        
        return card;
    }
    
    /**
     * Apply class-specific modifications to cards
     */
    applyClassModifications(card, runData) {
        // This would integrate with the class system
        // For now, just add some variety
        const classBonus = Math.floor(Math.random() * 3) + 1;
        
        if (card.damage) {
            card.damage += classBonus;
        }
        if (card.block) {
            card.block += classBonus;
        }
        if (card.heal) {
            card.heal += classBonus;
        }
    }
    
    /**
     * Generate a random artifact
     */
    generateRandomArtifact(minRarity = "common") {
        const rarityOrder = ["common", "uncommon", "rare", "legendary"];
        const minIndex = rarityOrder.indexOf(minRarity);
        
        const availableArtifacts = this.artifactPool.filter(artifact => {
            const artifactIndex = rarityOrder.indexOf(artifact.rarity);
            return artifactIndex >= minIndex;
        });
        
        const artifact = availableArtifacts[Math.floor(Math.random() * availableArtifacts.length)];
        
        return {
            ...artifact,
            id: `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            acquired: Date.now()
        };
    }
    
    /**
     * Generate upgrade choice for existing cards
     */
    generateUpgradeChoice(runData) {
        const availableCards = [
            ...runData.temporaryCards,
            // Would also include permanent cards from player's deck
        ];
        
        if (availableCards.length === 0) {
            return null;
        }
        
        const cardToUpgrade = availableCards[Math.floor(Math.random() * availableCards.length)];
        const upgradeOptions = Object.keys(this.upgradeTypes);
        const selectedUpgrade = upgradeOptions[Math.floor(Math.random() * upgradeOptions.length)];
        
        return {
            type: "card_upgrade",
            cardId: cardToUpgrade.id,
            upgradeType: selectedUpgrade,
            description: `Upgrade ${cardToUpgrade.name}: ${this.upgradeTypes[selectedUpgrade].description}`
        };
    }
    
    /**
     * Generate a reward choice (pick one of multiple options)
     */
    generateRewardChoice(runData) {
        const choices = [
            {
                type: "card_choice",
                description: "Choose a powerful card",
                options: [
                    this.generateRandomCard("rare", runData),
                    this.generateRandomCard("rare", runData),
                    this.generateRandomCard("uncommon", runData)
                ]
            },
            {
                type: "artifact_choice",
                description: "Choose an artifact",
                options: [
                    this.generateRandomArtifact("uncommon"),
                    this.generateRandomArtifact("uncommon"),
                    this.generateRandomArtifact("common")
                ]
            },
            {
                type: "upgrade_choice",
                description: "Choose an upgrade",
                options: [
                    { type: "health", description: "Increase max health by 20" },
                    { type: "energy", description: "Increase max energy by 10" },
                    { type: "draw", description: "Draw +1 card per turn" }
                ]
            }
        ];
        
        return choices[Math.floor(Math.random() * choices.length)];
    }
    
    /**
     * Apply rewards to a player's run
     */
    applyRewards(player, rewards, runData) {
        if (!runData) return;
        
        player.sendMessage("§6🎁 REWARDS EARNED:");
        
        // Apply gold
        if (rewards.gold > 0) {
            runData.runGold = (runData.runGold || 0) + rewards.gold;
            player.sendMessage(`§e+ ${rewards.gold} Gold (Total: ${runData.runGold})`);
        }
        
        // Apply cards
        rewards.cards.forEach(card => {
            runData.temporaryCards.push(card);
            player.sendMessage(`§b+ ${card.name} (${card.rarity})`);
        });
        
        // Apply artifacts
        rewards.artifacts.forEach(artifact => {
            runData.activeArtifacts.push(artifact);
            this.applyArtifactEffect(artifact, runData);
            player.sendMessage(`§d+ ${artifact.name}`);
            player.sendMessage(`§7  ${artifact.description}`);
        });
        
        // Handle upgrade choices
        rewards.upgrades.forEach(upgrade => {
            if (upgrade) {
                this.presentUpgradeChoice(player, upgrade, runData);
            }
        });
        
        // Handle reward choices
        rewards.choices.forEach(choice => {
            this.presentRewardChoice(player, choice, runData);
        });
    }
    
    /**
     * Apply artifact effects to run data
     */
    applyArtifactEffect(artifact, runData) {
        switch (artifact.effect) {
            case "energy_boost":
                runData.maxRunEnergy += artifact.value;
                runData.runEnergy = runData.maxRunEnergy;
                break;
                
            case "combat_heal":
                runData.combatStartHeal = (runData.combatStartHeal || 0) + artifact.value;
                break;
                
            case "damage_boost":
                runData.globalDamageBonus = (runData.globalDamageBonus || 0) + artifact.value;
                break;
                
            case "block_boost":
                runData.globalBlockBonus = (runData.globalBlockBonus || 0) + artifact.value;
                break;
                
            case "cost_reduction":
                runData.globalCostReduction = (runData.globalCostReduction || 0) + artifact.value;
                break;
                
            case "card_draw":
                runData.bonusCardDraw = (runData.bonusCardDraw || 0) + artifact.value;
                break;
        }
    }
    
    /**
     * Present upgrade choice to player
     */
    presentUpgradeChoice(player, upgrade, runData) {
        player.sendMessage("§6🔧 UPGRADE AVAILABLE:");
        player.sendMessage(`§e${upgrade.description}`);
        player.sendMessage("§7Use /combat upgrade accept to apply this upgrade");
        
        // Store pending upgrade
        runData.pendingUpgrade = upgrade;
    }
    
    /**
     * Present reward choice to player
     */
    presentRewardChoice(player, choice, runData) {
        player.sendMessage(`§6🎯 CHOICE: ${choice.description}`);
        choice.options.forEach((option, index) => {
            if (option.name) {
                player.sendMessage(`§e${index + 1}. ${option.name} - ${option.description}`);
            } else {
                player.sendMessage(`§e${index + 1}. ${option.description}`);
            }
        });
        player.sendMessage("§7Use /combat choose <number> to select");
        
        // Store pending choice
        runData.pendingChoice = choice;
    }
    
    /**
     * Apply card upgrade
     */
    applyCardUpgrade(cardId, upgradeType, runData) {
        const card = runData.temporaryCards.find(c => c.id === cardId);
        if (!card) return false;
        
        const upgrade = this.upgradeTypes[upgradeType];
        if (!upgrade) return false;
        
        upgrade.apply(card, runData);
        return true;
    }
    
    /**
     * Get all active artifacts for a run
     */
    getActiveArtifacts(runData) {
        return runData.activeArtifacts || [];
    }
    
    /**
     * Get all temporary cards for a run
     */
    getTemporaryCards(runData) {
        return runData.temporaryCards || [];
    }
    
    /**
     * Calculate total gold earned in run
     */
    getTotalGold(runData) {
        return runData.runGold || 0;
    }
    
    /**
     * Clean up run-specific rewards when run ends
     */
    cleanupRunRewards(runData) {
        // Temporary cards are automatically cleaned up when run ends
        // Some artifacts might convert to permanent rewards
        const permanentRewards = {
            metaProgress: Math.floor(runData.cardsCollected * 0.5),
            unlocks: [],
            achievements: []
        };
        
        // Check for special achievements
        if (runData.currentFloor >= 10) {
            permanentRewards.achievements.push("Deep Delver");
        }
        
        if (runData.activeArtifacts.length >= 3) {
            permanentRewards.achievements.push("Artifact Collector");
        }
        
        return permanentRewards;
    }
}
