/**
 * CombatAI - AI controller for entities in turn-based combat
 */

export class CombatAI {
    constructor(cardManager, combatManager) {
        this.cardManager = cardManager;
        this.combatManager = combatManager;
        
        // AI difficulty levels
        this.difficultyLevels = {
            NOVICE: "novice",
            EXPERT: "expert", 
            MASTER: "master"
        };
        
        // AI card decks by difficulty
        this.aiDecks = new Map();
        this.initializeAIDecks();
        
        // AI behavior patterns
        this.behaviorPatterns = {
            AGGRESSIVE: "aggressive",
            DEFENSIVE: "defensive",
            BALANCED: "balanced",
            CONTROL: "control"
        };
    }
    
    /**
     * Initialize AI decks for different difficulty levels
     */
    initializeAIDecks() {
        // Novice AI deck - simple, low-cost cards
        this.aiDecks.set(this.difficultyLevels.NOVICE, [
            "basic_strike", "basic_strike", "basic_strike",
            "basic_block", "basic_block",
            "health_potion", "health_potion"
        ]);
        
        // Expert AI deck - balanced with some strategy
        this.aiDecks.set(this.difficultyLevels.EXPERT, [
            "basic_strike", "basic_strike", "power_attack", "power_attack",
            "basic_block", "shield_wall", "shield_wall",
            "heal", "fireball", "strength_potion"
        ]);
        
        // Master AI deck - advanced cards and combos
        this.aiDecks.set(this.difficultyLevels.MASTER, [
            "power_attack", "critical_strike", "devastating_blow",
            "shield_wall", "iron_defense", "iron_defense",
            "fireball", "lightning_bolt", "time_warp",
            "golden_apple", "strength_potion"
        ]);
    }
    
    /**
     * Get AI difficulty based on entity
     */
    getAIDifficulty(entity) {
        if (entity.hasTag("novice_duelist")) {
            return this.difficultyLevels.NOVICE;
        } else if (entity.hasTag("expert_duelist")) {
            return this.difficultyLevels.EXPERT;
        } else if (entity.hasTag("master_duelist")) {
            return this.difficultyLevels.MASTER;
        }
        
        // Default based on entity type
        switch (entity.typeId) {
            case "minecraft:zombie":
            case "minecraft:skeleton":
                return this.difficultyLevels.NOVICE;
            case "minecraft:spider":
            case "minecraft:creeper":
                return this.difficultyLevels.EXPERT;
            case "minecraft:enderman":
            case "minecraft:witch":
                return this.difficultyLevels.MASTER;
            default:
                return this.difficultyLevels.NOVICE;
        }
    }
    
    /**
     * Get AI behavior pattern
     */
    getAIBehaviorPattern(entity, combatSession) {
        const difficulty = this.getAIDifficulty(entity);
        const health = entity.getComponent("health");
        const healthPercentage = health ? health.currentValue / health.defaultValue : 1.0;
        
        // Determine behavior based on health and difficulty
        if (healthPercentage < 0.3) {
            return this.behaviorPatterns.DEFENSIVE; // Low health = defensive
        }
        
        switch (difficulty) {
            case this.difficultyLevels.NOVICE:
                return Math.random() < 0.7 ? this.behaviorPatterns.AGGRESSIVE : this.behaviorPatterns.DEFENSIVE;
            case this.difficultyLevels.EXPERT:
                return this.behaviorPatterns.BALANCED;
            case this.difficultyLevels.MASTER:
                return this.behaviorPatterns.CONTROL;
            default:
                return this.behaviorPatterns.BALANCED;
        }
    }
    
    /**
     * Execute AI turn
     */
    async executeAITurn(entity, combatSession) {
        const difficulty = this.getAIDifficulty(entity);
        const behaviorPattern = this.getAIBehaviorPattern(entity, combatSession);
        
        // Get AI hand (simulated)
        const aiHand = this.getAIHand(entity, difficulty);
        const aiEnergy = this.getAIEnergy(entity, difficulty);
        
        // Choose action based on behavior pattern
        const action = this.chooseAction(entity, combatSession, aiHand, aiEnergy, behaviorPattern);
        
        if (action) {
            await this.executeAction(entity, combatSession, action);
        } else {
            // No valid action, end turn
            this.notifyAIAction(entity, "passes their turn");
        }
    }
    
    /**
     * Get AI hand (simulated deck)
     */
    getAIHand(entity, difficulty) {
        const deck = this.aiDecks.get(difficulty) || this.aiDecks.get(this.difficultyLevels.NOVICE);
        
        // Simulate drawing cards
        const handSize = 5;
        const hand = [];
        
        for (let i = 0; i < handSize; i++) {
            const randomCard = deck[Math.floor(Math.random() * deck.length)];
            hand.push(randomCard);
        }
        
        return hand;
    }
    
    /**
     * Get AI energy
     */
    getAIEnergy(entity, difficulty) {
        const baseEnergy = {
            [this.difficultyLevels.NOVICE]: 2,
            [this.difficultyLevels.EXPERT]: 3,
            [this.difficultyLevels.MASTER]: 4
        };
        
        return baseEnergy[difficulty] || 2;
    }
    
    /**
     * Choose best action for AI
     */
    chooseAction(entity, combatSession, hand, energy, behaviorPattern) {
        const playableCards = hand.filter(cardId => {
            const card = this.cardManager.getCard(cardId);
            return card && card.cost <= energy;
        });
        
        if (playableCards.length === 0) {
            return null; // No playable cards
        }
        
        // Score cards based on behavior pattern
        const scoredCards = playableCards.map(cardId => ({
            cardId,
            score: this.scoreCard(cardId, entity, combatSession, behaviorPattern)
        }));
        
        // Sort by score and pick the best
        scoredCards.sort((a, b) => b.score - a.score);
        
        return {
            type: "play_card",
            cardId: scoredCards[0].cardId,
            target: this.selectTarget(scoredCards[0].cardId, entity, combatSession)
        };
    }
    
    /**
     * Score a card for AI decision making
     */
    scoreCard(cardId, entity, combatSession, behaviorPattern) {
        const card = this.cardManager.getCard(cardId);
        if (!card) return 0;
        
        let score = 0;
        const health = entity.getComponent("health");
        const healthPercentage = health ? health.currentValue / health.defaultValue : 1.0;
        
        // Base scoring by card type
        switch (card.type) {
            case this.cardManager.cardTypes.ATTACK:
                score += card.damage || 0;
                if (behaviorPattern === this.behaviorPatterns.AGGRESSIVE) {
                    score *= 1.5;
                }
                break;
                
            case this.cardManager.cardTypes.DEFENSE:
                score += (card.block || 0) * 0.8;
                if (behaviorPattern === this.behaviorPatterns.DEFENSIVE) {
                    score *= 1.5;
                }
                if (healthPercentage < 0.5) {
                    score *= 1.3; // Prioritize defense when low health
                }
                break;
                
            case this.cardManager.cardTypes.SPELL:
                if (card.damage) {
                    score += card.damage;
                }
                if (card.healing) {
                    score += card.healing * (1.0 - healthPercentage); // More valuable when injured
                }
                break;
                
            case this.cardManager.cardTypes.ITEM:
                if (card.healing) {
                    score += card.healing * (1.0 - healthPercentage);
                }
                break;
        }
        
        // Bonus for special effects
        if (card.effects && card.effects.length > 0) {
            score += card.effects.length * 2;
            
            // Special scoring for specific effects
            if (card.effects.includes("draw_card")) {
                score += 3;
            }
            if (card.effects.includes("extra_turn")) {
                score += 10;
            }
        }
        
        // Cost efficiency
        if (card.cost > 0) {
            score = score / card.cost; // Prefer cost-efficient cards
        }
        
        // Randomization for unpredictability
        score += Math.random() * 2;
        
        return score;
    }
    
    /**
     * Select target for AI action
     */
    selectTarget(cardId, entity, combatSession) {
        const card = this.cardManager.getCard(cardId);
        if (!card) return null;
        
        // Only attack and some spell cards need targets
        if (card.type !== this.cardManager.cardTypes.ATTACK && 
            !(card.type === this.cardManager.cardTypes.SPELL && card.damage)) {
            return null;
        }
        
        // Find valid targets (enemies)
        const enemies = combatSession.participants.filter(p => 
            p !== entity && p.isValid() && p.typeId === "minecraft:player"
        );
        
        if (enemies.length === 0) return null;
        
        // Simple targeting: lowest health enemy
        let target = enemies[0];
        let lowestHealth = Infinity;
        
        for (const enemy of enemies) {
            const health = enemy.getComponent("health");
            if (health && health.currentValue < lowestHealth) {
                lowestHealth = health.currentValue;
                target = enemy;
            }
        }
        
        return target;
    }
    
    /**
     * Execute AI action
     */
    async executeAction(entity, combatSession, action) {
        switch (action.type) {
            case "play_card":
                await this.playAICard(entity, combatSession, action.cardId, action.target);
                break;
            default:
                console.warn("Unknown AI action type:", action.type);
        }
    }
    
    /**
     * Play AI card
     */
    async playAICard(entity, combatSession, cardId, target) {
        const card = this.cardManager.getCard(cardId);
        if (!card) return;
        
        // Notify players of AI action
        this.notifyAIAction(entity, `plays ${card.name}!`);
        
        // Apply card effects
        await this.applyAICardEffects(entity, combatSession, card, target);
        
        // Visual effects
        this.showAICardEffects(entity, card);
    }
    
    /**
     * Apply AI card effects
     */
    async applyAICardEffects(entity, combatSession, card, target) {
        switch (card.type) {
            case this.cardManager.cardTypes.ATTACK:
                if (target && card.damage) {
                    this.dealDamage(target, card.damage, entity);
                    this.notifyAIAction(entity, `deals ${card.damage} damage to ${target.nameTag || target.typeId}!`);
                }
                break;
                
            case this.cardManager.cardTypes.DEFENSE:
                if (card.block) {
                    // AI doesn't have persistent block, so just show the action
                    this.notifyAIAction(entity, `gains ${card.block} block!`);
                }
                break;
                
            case this.cardManager.cardTypes.SPELL:
                if (card.damage && target) {
                    this.dealDamage(target, card.damage, entity);
                    this.notifyAIAction(entity, `casts ${card.name} for ${card.damage} damage!`);
                }
                if (card.healing) {
                    this.healEntity(entity, card.healing);
                    this.notifyAIAction(entity, `heals for ${card.healing} health!`);
                }
                break;
                
            case this.cardManager.cardTypes.ITEM:
                if (card.healing) {
                    this.healEntity(entity, card.healing);
                    this.notifyAIAction(entity, `uses ${card.name} and heals for ${card.healing}!`);
                }
                break;
        }
        
        // Apply special effects
        for (const effect of card.effects || []) {
            await this.applyAISpecialEffect(entity, combatSession, effect);
        }
    }
    
    /**
     * Apply AI special effects
     */
    async applyAISpecialEffect(entity, combatSession, effect) {
        switch (effect) {
            case "extra_turn":
                combatSession.extraTurn = entity;
                this.notifyAIAction(entity, "gets an extra turn!");
                break;
            case "draw_card":
                this.notifyAIAction(entity, "draws a card!");
                break;
            // Add more special effects as needed
        }
    }
    
    /**
     * Show visual effects for AI card
     */
    showAICardEffects(entity, card) {
        // Spawn particles based on card type
        const location = entity.location;
        
        switch (card.type) {
            case this.cardManager.cardTypes.ATTACK:
                entity.dimension.spawnParticle("minecraft:critical_hit_emitter", location);
                break;
            case this.cardManager.cardTypes.DEFENSE:
                entity.dimension.spawnParticle("minecraft:villager_happy", location);
                break;
            case this.cardManager.cardTypes.SPELL:
                entity.dimension.spawnParticle("minecraft:enchanting_table_particle", location);
                break;
            case this.cardManager.cardTypes.ITEM:
                entity.dimension.spawnParticle("minecraft:heart_particle", location);
                break;
        }
    }
    
    /**
     * Utility methods
     */
    
    dealDamage(target, damage, source) {
        const health = target.getComponent("health");
        if (health) {
            health.setCurrentValue(Math.max(0, health.currentValue - damage));
        }
        
        // Visual effects
        target.dimension.spawnParticle("minecraft:critical_hit_emitter", target.location);
    }
    
    healEntity(entity, amount) {
        const health = entity.getComponent("health");
        if (health) {
            const newHealth = Math.min(health.defaultValue, health.currentValue + amount);
            health.setCurrentValue(newHealth);
        }
        
        // Visual effects
        entity.dimension.spawnParticle("minecraft:heart_particle", entity.location);
    }
    
    notifyAIAction(entity, message) {
        const entityName = entity.nameTag || entity.typeId;
        const formattedMessage = `§c${entityName} §r${message}`;
        
        // Send message to nearby players
        for (const player of entity.dimension.getPlayers()) {
            const distance = this.getDistance(player.location, entity.location);
            if (distance <= 20) {
                player.sendMessage(formattedMessage);
            }
        }
    }
    
    getDistance(loc1, loc2) {
        const dx = loc1.x - loc2.x;
        const dy = loc1.y - loc2.y;
        const dz = loc1.z - loc2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
}
