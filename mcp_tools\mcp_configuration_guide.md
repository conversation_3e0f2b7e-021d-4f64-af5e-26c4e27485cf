# MCP Tools Configuration Guide

This guide explains how to configure and use the installed MCP (Model Context Protocol) tools for audio and texture generation with your Minecraft addon project.

## Installed MCP Tools

### 1. ElevenLabs MCP Server (Audio Generation)
- **Purpose**: High-quality text-to-speech, voice cloning, and audio processing
- **Installation**: ✅ Installed via pip
- **Type**: Python-based MCP server

### 2. MiniMax MCP JS (Multi-modal Generation)
- **Purpose**: Image generation, video generation, text-to-speech, voice cloning
- **Installation**: ✅ Installed via npm
- **Type**: Node.js-based MCP server

### 3. Figma MCP Server (Design & UI Assets)
- **Purpose**: Access Figma designs, extract assets, collaborate on UI elements
- **Installation**: ✅ Installed via npm
- **Type**: Node.js-based MCP server

## Configuration Steps

### Step 1: Get API Keys

#### ElevenLabs API Key
1. Visit [ElevenLabs](https://elevenlabs.io/)
2. Sign up/login to your account
3. Go to Profile → API Keys
4. Generate a new API key
5. Copy the key for configuration

#### MiniMax API Key
1. Visit [MiniMax Platform](https://www.minimax.io/platform/user-center/basic-information/interface-key)
2. Sign up/login to your account
3. Navigate to Interface Key section
4. Generate API key
5. Note: Different regions use different hosts:
   - Global: `https://api.minimaxi.chat` (note the extra "i")
   - Mainland China: `https://api.minimax.chat`

#### Figma API Key (Optional)
1. Visit [Figma](https://figma.com)
2. Click your name (top left) → Settings → Security
3. Generate Personal Access Token
4. Grant `File content` and `Comments` scopes

### Step 2: Configure Claude Desktop

Create or edit your Claude Desktop configuration file:

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "elevenlabs-mcp": {
      "command": "python",
      "args": ["-m", "elevenlabs_mcp"],
      "env": {
        "ELEVENLABS_API_KEY": "your-elevenlabs-api-key-here"
      }
    },
    "minimax-mcp-js": {
      "command": "npx",
      "args": ["-y", "minimax-mcp-js"],
      "env": {
        "MINIMAX_API_HOST": "https://api.minimaxi.chat",
        "MINIMAX_API_KEY": "your-minimax-api-key-here",
        "MINIMAX_MCP_BASE_PATH": "C:\\Users\\<USER>\\Desktop\\coding stuff\\mcc_addon\\generated_assets",
        "MINIMAX_RESOURCE_MODE": "local"
      }
    },
    "figma-mcp": {
      "command": "npx",
      "args": ["figma-mcp"],
      "env": {
        "FIGMA_API_KEY": "your-figma-api-key-here"
      }
    }
  }
}
```

### Step 3: Create Asset Directories

```bash
mkdir generated_assets
mkdir generated_assets/audio
mkdir generated_assets/textures
mkdir generated_assets/ui
```

## Usage Examples

### Audio Generation with ElevenLabs
```
Generate a sword swing sound effect for my Minecraft addon
```

### Audio/Image Generation with MiniMax
```
Create a magical spell casting sound effect
Generate a fantasy card texture for my combat system
```

### UI Asset Creation with Figma
```
Extract button designs from this Figma file: [URL]
Get UI components for my card game interface
```

## Integration with Minecraft Addon

### Audio Assets
Generated audio files should be placed in:
- `resource_pack/sounds/` directory
- Update `sound_definitions.json` with new audio references
- Use AudioManager.js to play sounds in-game

### Texture Assets
Generated textures should be placed in:
- `resource_pack/textures/` directory
- Update item definitions to reference new textures
- Follow Minecraft's texture naming conventions

### UI Assets
Generated UI elements can be used for:
- HUD overlays
- Menu backgrounds
- Card designs
- Button graphics

## Troubleshooting

### Common Issues

1. **"No tools found" error**
   - Restart Claude Desktop after configuration
   - Check API keys are correctly set
   - Verify file paths in configuration

2. **Python module not found**
   - Ensure Python is in PATH
   - Try using full Python path in configuration

3. **Node.js package errors**
   - Verify Node.js and npm are installed
   - Check network connectivity for npm packages

### Testing MCP Tools

Use the MCP Inspector for testing:
```bash
npx @modelcontextprotocol/inspector npx minimax-mcp-js
```

## Next Steps

1. Configure API keys in Claude Desktop
2. Restart Claude Desktop
3. Test each MCP tool with simple requests
4. Generate missing audio assets for your addon
5. Create texture assets for cards and UI elements
6. Integrate generated assets into your Minecraft addon

## Asset Generation Workflow

1. **Identify Missing Assets**: Review `sound_definitions.json` for required audio
2. **Generate Audio**: Use ElevenLabs or MiniMax for sound effects and music
3. **Create Textures**: Use MiniMax for card artwork and UI elements
4. **Design UI**: Use Figma MCP for interface components
5. **Integrate**: Place assets in appropriate addon directories
6. **Test**: Verify assets work correctly in Minecraft

Remember to backup your addon before adding new assets and test thoroughly in Minecraft Bedrock Edition.
