/**
 * Integration Test for Ultimate Abilities System
 * This file can be used to test the system integration
 */

import { world, system } from "@minecraft/server";

// Test function to verify ultimate system integration
export function testUltimateSystemIntegration() {
    console.log("🧪 Testing Ultimate Abilities System Integration...");
    
    const testResults = {
        passed: 0,
        failed: 0,
        errors: []
    };
    
    try {
        // Test 1: Check if UltimateManager is properly imported
        console.log("Test 1: Checking UltimateManager import...");
        const { UltimateManager } = await import("./behavior_pack/scripts/abilities/UltimateManager.js");
        if (UltimateManager) {
            console.log("✅ UltimateManager imported successfully");
            testResults.passed++;
        } else {
            throw new Error("UltimateManager not found");
        }
        
        // Test 2: Check if UltimateCards is properly imported
        console.log("Test 2: Checking UltimateCards import...");
        const { UltimateCards } = await import("./behavior_pack/scripts/cards/UltimateCards.js");
        if (UltimateCards) {
            console.log("✅ UltimateCards imported successfully");
            testResults.passed++;
        } else {
            throw new Error("UltimateCards not found");
        }
        
        // Test 3: Check ultimate cards data structure
        console.log("Test 3: Checking ultimate cards data...");
        const ultimateCards = UltimateCards.getUltimateCards();
        const expectedCards = [
            "berserker_wrath", "avatar_of_war",
            "arcane_ascendance", "archmage_transcendence", 
            "shadow_clone", "master_assassin",
            "divine_intervention", "divine_avatar",
            "army_of_the_dead", "lich_lord"
        ];
        
        let allCardsFound = true;
        for (const cardId of expectedCards) {
            if (!ultimateCards[cardId]) {
                testResults.errors.push(`Missing ultimate card: ${cardId}`);
                allCardsFound = false;
            }
        }
        
        if (allCardsFound) {
            console.log("✅ All ultimate cards found");
            testResults.passed++;
        } else {
            testResults.failed++;
        }
        
        // Test 4: Check class-specific ultimate cards
        console.log("Test 4: Checking class-specific ultimate cards...");
        const classes = ["warrior", "mage", "rogue", "paladin", "necromancer"];
        let classCardsValid = true;
        
        for (const className of classes) {
            const classCards = UltimateCards.getClassUltimateCards(className);
            if (Object.keys(classCards).length !== 2) {
                testResults.errors.push(`Class ${className} should have exactly 2 ultimate cards`);
                classCardsValid = false;
            }
        }
        
        if (classCardsValid) {
            console.log("✅ Class-specific ultimate cards valid");
            testResults.passed++;
        } else {
            testResults.failed++;
        }
        
        // Test 5: Check level-based card availability
        console.log("Test 5: Checking level-based card availability...");
        const level10Cards = UltimateCards.getAvailableUltimateCards("warrior", 10);
        const level20Cards = UltimateCards.getAvailableUltimateCards("warrior", 20);
        
        if (Object.keys(level10Cards).length === 1 && Object.keys(level20Cards).length === 2) {
            console.log("✅ Level-based card availability working");
            testResults.passed++;
        } else {
            testResults.errors.push("Level-based card availability not working correctly");
            testResults.failed++;
        }
        
    } catch (error) {
        console.error("❌ Integration test failed:", error);
        testResults.errors.push(error.message);
        testResults.failed++;
    }
    
    // Print test results
    console.log("\n🧪 Ultimate System Integration Test Results:");
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    
    if (testResults.errors.length > 0) {
        console.log("\n🚨 Errors:");
        testResults.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (testResults.failed === 0) {
        console.log("\n🎉 All integration tests passed! Ultimate system is ready!");
    } else {
        console.log("\n⚠️ Some tests failed. Please check the errors above.");
    }
    
    return testResults;
}

// Test function for in-game testing
export function runInGameTests() {
    console.log("🎮 Running in-game ultimate system tests...");
    
    // Test with all online players
    const players = world.getAllPlayers();
    if (players.length === 0) {
        console.log("⚠️ No players online for testing");
        return;
    }
    
    const testPlayer = players[0];
    console.log(`🧪 Testing with player: ${testPlayer.name}`);
    
    // Test commands
    testPlayer.sendMessage("§6🧪 Testing Ultimate Abilities System...");
    
    // Test class selection
    system.runTimeout(() => {
        testPlayer.runCommand("class select warrior");
    }, 20);
    
    // Test abilities command
    system.runTimeout(() => {
        testPlayer.runCommand("class abilities");
    }, 40);
    
    // Test ultimate command
    system.runTimeout(() => {
        testPlayer.runCommand("class ultimate");
    }, 60);
    
    testPlayer.sendMessage("§a✅ Ultimate system test commands executed!");
    testPlayer.sendMessage("§7Check console for detailed results.");
}

// Export test functions for use in main.js or testing
export const UltimateSystemTests = {
    testIntegration: testUltimateSystemIntegration,
    runInGame: runInGameTests
};

// Auto-run integration test when imported
console.log("🔧 Ultimate Abilities System integration test loaded");
console.log("🧪 Run UltimateSystemTests.testIntegration() to test integration");
console.log("🎮 Run UltimateSystemTests.runInGame() to test in-game functionality");
