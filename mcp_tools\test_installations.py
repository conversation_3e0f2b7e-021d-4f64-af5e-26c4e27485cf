#!/usr/bin/env python3
"""
Test script to verify MCP tool installations
"""

import subprocess
import sys
import os

def test_python_package(package_name):
    """Test if a Python package is installed"""
    try:
        __import__(package_name.replace('-', '_'))
        print(f"✅ {package_name} - Python package installed")
        return True
    except ImportError:
        print(f"❌ {package_name} - Python package NOT installed")
        return False

def test_npm_package(package_name):
    """Test if an npm package is available"""
    try:
        result = subprocess.run(['npm', 'list', package_name], 
                              capture_output=True, text=True, cwd='mcp_tools')
        if package_name in result.stdout:
            print(f"✅ {package_name} - npm package installed")
            return True
        else:
            print(f"❌ {package_name} - npm package NOT installed")
            return False
    except Exception as e:
        print(f"❌ {package_name} - Error checking npm package: {e}")
        return False

def test_command_available(command):
    """Test if a command is available in PATH"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {command} - Command available")
            return True
        else:
            print(f"❌ {command} - Command NOT available")
            return False
    except Exception:
        print(f"❌ {command} - Command NOT available")
        return False

def main():
    print("🔍 Testing MCP Tool Installations\n")
    
    # Test system requirements
    print("📋 System Requirements:")
    python_ok = test_command_available('python')
    node_ok = test_command_available('node')
    npm_ok = test_command_available('npm')
    
    print("\n📦 Python Packages:")
    elevenlabs_ok = test_python_package('elevenlabs-mcp')
    
    print("\n📦 Node.js Packages:")
    minimax_ok = test_npm_package('minimax-mcp-js')
    figma_ok = test_npm_package('figma-mcp')
    
    print("\n📁 Directory Structure:")
    directories = [
        'generated_assets',
        'generated_assets/audio',
        'generated_assets/textures', 
        'generated_assets/ui'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory} - Directory exists")
        else:
            print(f"❌ {directory} - Directory missing")
    
    print("\n📄 Configuration Files:")
    config_files = [
        'mcp_tools/claude_desktop_config_sample.json',
        'mcp_tools/mcp_configuration_guide.md'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} - File exists")
        else:
            print(f"❌ {config_file} - File missing")
    
    print("\n🎯 Summary:")
    all_good = all([python_ok, node_ok, npm_ok, elevenlabs_ok, minimax_ok, figma_ok])
    
    if all_good:
        print("✅ All MCP tools are properly installed!")
        print("\n📋 Next Steps:")
        print("1. Get API keys from ElevenLabs, MiniMax, and Figma")
        print("2. Configure Claude Desktop with your API keys")
        print("3. Restart Claude Desktop")
        print("4. Test the tools with simple requests")
    else:
        print("❌ Some installations are incomplete")
        print("Please check the failed items above")

if __name__ == "__main__":
    main()
