/**
 * GameConfig - Configuration settings for the card combat system
 */

export const GameConfig = {
    // Combat Settings
    combat: {
        // Turn settings
        maxTurnTime: 30, // seconds
        maxRounds: 20,
        combatRange: 8, // blocks
        combatCooldown: 3000, // milliseconds
        
        // Hand and energy settings
        defaultHandSize: 5,
        maxHandSize: 10,
        defaultEnergy: 3,
        maxEnergy: 10,
        
        // Turn order algorithm
        turnOrderAlgorithm: "initiative", // simple, initiative, speed_based, card_based
        
        // Combat detection
        autoStartCombat: true,
        requireMutualAggression: false,
        
        // AI settings
        aiDifficulty: "adaptive", // easy, normal, hard, adaptive
        aiThinkTime: 2000, // milliseconds
        
        // Visual effects
        showDamageNumbers: true,
        showStatusEffects: true,
        combatAnimations: true
    },
    
    // Card System Settings
    cards: {
        // Deck constraints
        minDeckSize: 10,
        maxDeckSize: 30,
        maxCopiesPerCard: 3,
        maxLegendaryCards: 2,
        
        // Card generation
        rarityWeights: {
            common: 60,
            uncommon: 25,
            rare: 12,
            legendary: 3
        },
        
        // Card packs
        basicPackSize: 5,
        advancedPackSize: 4,
        legendaryPackSize: 3,
        
        // Crafting costs
        craftingCosts: {
            common: 5,
            uncommon: 15,
            rare: 50,
            legendary: 200
        }
    },
    
    // Progression Settings
    progression: {
        // Experience settings
        baseExperiencePerLevel: 100,
        experienceScaling: 1.5,
        maxLevel: 50,
        
        // Experience rewards
        combatVictoryExp: 50,
        combatDefeatExp: 10,
        flawlessVictoryBonus: 25,
        speedVictoryBonus: 15,
        
        // Level rewards
        energyIncreaseInterval: 5, // every 5 levels
        deckSlotIncreaseInterval: 3, // every 3 levels
        cardPackRewardInterval: 3, // every 3 levels
        
        // Achievement settings
        enableAchievements: true,
        showAchievementNotifications: true,
        
        // Titles
        titles: {
            1: "Novice",
            4: "Apprentice", 
            7: "Adept",
            11: "Expert",
            16: "Veteran",
            21: "Master",
            26: "Grandmaster",
            36: "Legend",
            46: "Mythic"
        }
    },
    
    // UI Settings
    ui: {
        // Interface preferences
        showCardTooltips: true,
        autoEndTurn: false,
        confirmCardPlay: false,
        showTurnOrder: true,
        
        // Animation settings
        cardPlayAnimation: true,
        uiAnimationSpeed: 1.0,
        
        // Color scheme
        colors: {
            common: "§f",
            uncommon: "§a", 
            rare: "§9",
            legendary: "§6",
            energy: "§b",
            health: "§c",
            block: "§9"
        },
        
        // Sound settings
        playSounds: true,
        soundVolume: 0.8
    },
    
    // Balance Settings
    balance: {
        // Damage scaling
        damageScaling: 1.0,
        healingScaling: 1.0,
        blockScaling: 1.0,
        
        // Status effect durations
        defaultStatusDuration: 3,
        maxStatusStacks: 5,
        
        // Type effectiveness multipliers
        typeEffectiveness: {
            weakness: 1.5,
            resistance: 0.5,
            immunity: 0.0
        },
        
        // AI scaling
        aiDamageMultiplier: 1.0,
        aiHealthMultiplier: 1.0,
        aiSpeedMultiplier: 1.0
    },
    
    // Debug Settings
    debug: {
        // Logging
        enableLogging: true,
        logLevel: "info", // debug, info, warn, error
        logCombatEvents: false,
        logCardPlays: false,
        
        // Testing
        enableTestCommands: true,
        enableCheatCommands: false,
        
        // Performance monitoring
        enablePerformanceMonitoring: false,
        performanceLogInterval: 60000, // milliseconds
        
        // Memory management
        enableMemoryCleanup: true,
        memoryCleanupInterval: 300000 // 5 minutes
    },
    
    // Feature Flags
    features: {
        // Core features
        turnBasedCombat: true,
        cardSystem: true,
        deckBuilder: true,
        progression: true,
        achievements: true,
        
        // Advanced features
        statusEffects: true,
        damageTypes: true,
        cardCrafting: true,
        multiplayerCombat: true,
        
        // Experimental features
        customCards: false,
        cardTrading: false,
        tournaments: false,
        seasonalEvents: false
    },
    
    // Localization
    localization: {
        defaultLanguage: "en_US",
        supportedLanguages: ["en_US"],
        
        // Text keys would go here in a full implementation
        text: {
            combatStart: "Combat begins!",
            combatEnd: "Combat ended!",
            turnStart: "Your turn!",
            cardPlayed: "Card played!",
            levelUp: "Level up!",
            achievementUnlocked: "Achievement unlocked!"
        }
    }
};

/**
 * Get configuration value with fallback
 */
export function getConfig(path, defaultValue = null) {
    const keys = path.split('.');
    let current = GameConfig;
    
    for (const key of keys) {
        if (current && typeof current === 'object' && key in current) {
            current = current[key];
        } else {
            return defaultValue;
        }
    }
    
    return current;
}

/**
 * Set configuration value
 */
export function setConfig(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = GameConfig;
    
    for (const key of keys) {
        if (!(key in current)) {
            current[key] = {};
        }
        current = current[key];
    }
    
    current[lastKey] = value;
}

/**
 * Validate configuration
 */
export function validateConfig() {
    const errors = [];
    
    // Validate combat settings
    if (GameConfig.combat.maxTurnTime <= 0) {
        errors.push("Combat max turn time must be positive");
    }
    
    if (GameConfig.combat.defaultHandSize > GameConfig.combat.maxHandSize) {
        errors.push("Default hand size cannot exceed max hand size");
    }
    
    // Validate card settings
    if (GameConfig.cards.minDeckSize > GameConfig.cards.maxDeckSize) {
        errors.push("Min deck size cannot exceed max deck size");
    }
    
    // Validate progression settings
    if (GameConfig.progression.maxLevel <= 0) {
        errors.push("Max level must be positive");
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

/**
 * Load configuration from world storage (placeholder)
 */
export function loadConfig() {
    // In a full implementation, this would load from world storage
    // For now, we use the default configuration
    return GameConfig;
}

/**
 * Save configuration to world storage (placeholder)
 */
export function saveConfig() {
    // In a full implementation, this would save to world storage
    console.log("Configuration saved (placeholder)");
}
