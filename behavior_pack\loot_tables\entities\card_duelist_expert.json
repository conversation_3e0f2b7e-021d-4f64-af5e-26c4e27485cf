{"pools": [{"rolls": 1, "entries": [{"type": "item", "name": "card_combat:advanced_card_pack", "weight": 50, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "card_combat:basic_card_pack", "weight": 30, "functions": [{"function": "set_count", "count": {"min": 2, "max": 3}}]}, {"type": "item", "name": "emerald", "weight": 20, "functions": [{"function": "set_count", "count": {"min": 3, "max": 6}}]}]}, {"rolls": 1, "entries": [{"type": "item", "name": "card_combat:card_fragment", "weight": 60, "functions": [{"function": "set_count", "count": {"min": 2, "max": 4}}]}, {"type": "item", "name": "experience_bottle", "weight": 40, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}], "conditions": [{"condition": "killed_by_player"}]}]}