/**
 * ClassCommands - Handles class-related slash commands
 */

import { world, system } from "@minecraft/server";

export class ClassCommands {
    constructor(classManager, playerDataManager) {
        this.classManager = classManager;
        this.playerDataManager = playerDataManager;
        this.registerCommands();
    }
    
    registerCommands() {
        // Register class selection command
        world.beforeEvents.chatSend.subscribe((eventData) => {
            const { sender: player, message } = eventData;
            
            if (message.startsWith("/class")) {
                eventData.cancel = true;
                system.run(() => {
                    this.handleClassCommand(player, message);
                });
            }
        });
    }
    
    handleClassCommand(player, message) {
        const args = message.split(" ");
        const subCommand = args[1]?.toLowerCase();
        
        switch (subCommand) {
            case "select":
            case "choose":
                this.handleClassSelect(player, args[2]);
                break;
            case "info":
                this.handleClassInfo(player, args[2]);
                break;
            case "list":
                this.handleClassList(player);
                break;
            case "stats":
            case "status":
                this.handleClassStats(player);
                break;
            case "reset":
                this.handleClassReset(player);
                break;
            case "confirm":
                if (args[2]) {
                    this.handleClassConfirm(player, args[2].toLowerCase());
                } else {
                    player.sendMessage("§c❌ Please specify class to confirm!");
                }
                break;
            case "confirm_reset":
                this.handleClassConfirmReset(player);
                break;
            default:
                this.showClassHelp(player);
                break;
        }
    }
    
    handleClassSelect(player, className) {
        if (!className) {
            player.sendMessage("§c❌ Please specify a class name!");
            player.sendMessage("§7Use: §e/class select <warrior|mage|rogue|paladin|necromancer>");
            return;
        }
        
        className = className.toLowerCase();
        const availableClasses = this.classManager.getAllClasses();
        
        if (!availableClasses.includes(className)) {
            player.sendMessage(`§c❌ Invalid class: ${className}`);
            player.sendMessage(`§7Available classes: §e${availableClasses.join(", ")}`);
            return;
        }
        
        try {
            const currentClass = this.classManager.getPlayerClass(player);
            
            if (currentClass === className) {
                player.sendMessage(`§e⚠ You are already a ${this.classManager.getClassInfo(className).name}!`);
                return;
            }
            
            // Confirm class change if player already has a class
            if (currentClass) {
                player.sendMessage(`§c⚠ WARNING: Changing class will reset your level and remove class-specific cards!`);
                player.sendMessage(`§7Type §e/class confirm ${className}§7 to confirm the change.`);
                
                // Store pending class change
                const data = this.playerDataManager.getPlayerData(player);
                data.pendingClassChange = className;
                this.playerDataManager.savePlayerData(player);
                return;
            }
            
            // Set new class
            this.classManager.setPlayerClass(player, className);
            this.showClassWelcome(player, className);
            
        } catch (error) {
            player.sendMessage(`§c❌ Error selecting class: ${error.message}`);
        }
    }
    
    handleClassConfirm(player, className) {
        const data = this.playerDataManager.getPlayerData(player);
        
        if (data.pendingClassChange !== className) {
            player.sendMessage("§c❌ No pending class change for this class!");
            return;
        }
        
        try {
            this.classManager.setPlayerClass(player, className);
            delete data.pendingClassChange;
            this.playerDataManager.savePlayerData(player);
            this.showClassWelcome(player, className);
        } catch (error) {
            player.sendMessage(`§c❌ Error confirming class: ${error.message}`);
        }
    }
    
    handleClassInfo(player, className) {
        if (!className) {
            // Show current class info
            const currentClass = this.classManager.getPlayerClass(player);
            if (!currentClass) {
                player.sendMessage("§c❌ You don't have a class yet! Use §e/class select <class>");
                return;
            }
            className = currentClass;
        }
        
        className = className.toLowerCase();
        const classInfo = this.classManager.getClassInfo(className);
        
        if (!classInfo) {
            player.sendMessage(`§c❌ Invalid class: ${className}`);
            return;
        }
        
        player.sendMessage(`§6=== ${classInfo.name} ===`);
        player.sendMessage(`§7${classInfo.description}`);
        player.sendMessage(`§aBase Health: §f${classInfo.baseHealth} §7(+${classInfo.healthPerLevel}/level)`);
        player.sendMessage(`§9Base Energy: §f${classInfo.baseEnergy} §7(+${classInfo.energyPerLevel}/level)`);
        player.sendMessage(`§dSpecialties: §f${classInfo.specialties.join(", ")}`);
        
        // Show starting cards
        player.sendMessage(`§eStarting Cards: §f${classInfo.startingCards.length} cards`);
        
        // Show class cards
        player.sendMessage(`§dClass Cards: §f${classInfo.classCards.length} unique cards`);
    }
    
    handleClassList(player) {
        player.sendMessage("§6=== Available Classes ===");
        
        const classes = this.classManager.getAllClasses();
        for (const className of classes) {
            const classInfo = this.classManager.getClassInfo(className);
            player.sendMessage(`§e${classInfo.name} §7- ${classInfo.description}`);
        }
        
        player.sendMessage("§7Use §e/class info <class>§7 for detailed information");
        player.sendMessage("§7Use §e/class select <class>§7 to choose a class");
    }
    
    handleClassStats(player) {
        const currentClass = this.classManager.getPlayerClass(player);
        
        if (!currentClass) {
            player.sendMessage("§c❌ You don't have a class yet! Use §e/class select <class>");
            return;
        }
        
        const data = this.playerDataManager.getPlayerData(player);
        const classInfo = this.classManager.getClassInfo(currentClass);
        const level = data.classLevel || 1;
        const experience = data.classExperience || 0;
        const nextLevelXP = this.classManager.getExperienceForNextLevel(level);
        
        player.sendMessage(`§6=== ${classInfo.name} Stats ===`);
        player.sendMessage(`§aLevel: §f${level}/${this.classManager.maxLevel}`);
        player.sendMessage(`§bExperience: §f${experience}/${nextLevelXP}`);
        player.sendMessage(`§cHealth: §f${data.health || data.maxHealth}/${data.maxHealth}`);
        player.sendMessage(`§9Energy: §f${data.energy || data.maxEnergy}/${data.maxEnergy}`);
        
        // Show progress bar
        const progressPercent = Math.floor((experience / nextLevelXP) * 100);
        const progressBar = this.createProgressBar(progressPercent);
        player.sendMessage(`§7Progress: ${progressBar} §f${progressPercent}%`);
        
        // Show next reward
        if (level < this.classManager.maxLevel) {
            if ((level + 1) % 3 === 0) {
                player.sendMessage(`§dNext Level: §fNew class card!`);
            } else {
                player.sendMessage(`§dNext Level: §f+${classInfo.healthPerLevel} Health, +${classInfo.energyPerLevel} Energy`);
            }
        }
    }
    
    handleClassReset(player) {
        const currentClass = this.classManager.getPlayerClass(player);
        
        if (!currentClass) {
            player.sendMessage("§c❌ You don't have a class to reset!");
            return;
        }
        
        player.sendMessage("§c⚠ WARNING: This will reset your class level and experience!");
        player.sendMessage("§7Type §e/class confirm_reset§7 to confirm.");
        
        // Store pending reset
        const data = this.playerDataManager.getPlayerData(player);
        data.pendingReset = true;
        this.playerDataManager.savePlayerData(player);
    }
    
    handleClassConfirmReset(player) {
        const data = this.playerDataManager.getPlayerData(player);
        
        if (!data.pendingReset) {
            player.sendMessage("§c❌ No pending class reset!");
            return;
        }
        
        const currentClass = this.classManager.getPlayerClass(player);
        if (currentClass) {
            data.classLevel = 1;
            data.classExperience = 0;
            this.classManager.updatePlayerStats(player);
            
            delete data.pendingReset;
            this.playerDataManager.savePlayerData(player);
            
            player.sendMessage("§a✓ Class level and experience reset!");
        }
    }
    
    showClassWelcome(player, className) {
        const classInfo = this.classManager.getClassInfo(className);
        
        player.sendMessage("§6" + "=".repeat(30));
        player.sendMessage(`§6🎭 Welcome, ${classInfo.name}! 🎭`);
        player.sendMessage("§6" + "=".repeat(30));
        player.sendMessage(`§7${classInfo.description}`);
        player.sendMessage("");
        player.sendMessage("§eClass Features:");
        player.sendMessage(`§a• Health: §f${classInfo.baseHealth} §7(+${classInfo.healthPerLevel}/level)`);
        player.sendMessage(`§9• Energy: §f${classInfo.baseEnergy} §7(+${classInfo.energyPerLevel}/level)`);
        player.sendMessage(`§d• Specialties: §f${classInfo.specialties.join(", ")}`);
        player.sendMessage("");
        player.sendMessage("§7Use §e/class stats§7 to view your progress");
        player.sendMessage("§7Use §e/combat start§7 to begin your first battle!");
    }
    
    showClassHelp(player) {
        player.sendMessage("§6=== Class System Commands ===");
        player.sendMessage("§e/class list §7- Show all available classes");
        player.sendMessage("§e/class info [class] §7- Show class information");
        player.sendMessage("§e/class select <class> §7- Choose your class");
        player.sendMessage("§e/class stats §7- Show your class stats");
        player.sendMessage("§e/class reset §7- Reset your class level");
        player.sendMessage("");
        player.sendMessage("§7Available classes: §ewarrior, mage, rogue, paladin, necromancer");
    }
    
    createProgressBar(percent) {
        const barLength = 20;
        const filledLength = Math.floor((percent / 100) * barLength);
        const emptyLength = barLength - filledLength;
        
        return "§a" + "█".repeat(filledLength) + "§7" + "░".repeat(emptyLength);
    }
}
