# Card Combat System - Audio Specifications

## Overview

This document provides detailed specifications for all audio assets in the Card Combat System. Each sound effect has been designed to enhance the player experience and provide clear audio feedback for all game actions.

## Audio Technical Standards

### Format Requirements
- **File Format**: OGG Vorbis
- **Sample Rate**: 44.1kHz
- **Bit Depth**: 16-bit
- **Compression**: Variable bitrate, quality level 6
- **File Naming**: Lowercase with underscores (e.g., `card_play1.ogg`)

### Volume Standards
- **UI Sounds**: -15dB to -18dB (subtle feedback)
- **Combat Actions**: -9dB to -12dB (clear feedback)
- **Major Events**: -4dB to -8dB (prominent notifications)
- **Ambient Effects**: -16dB to -20dB (background atmosphere)

## Combat Sounds

### Card Play Sounds
**Files**: `card_play1.ogg`, `card_play2.ogg`, `card_play3.ogg`

#### card_play1.ogg
- **Duration**: 0.4 seconds
- **Volume**: -12dB
- **Pitch**: C4 (261.63 Hz base)
- **Character**: Crisp paper slide with magical shimmer
- **Usage**: Primary card play sound
- **Frequency Profile**:
  - Low (200-500Hz): Paper texture
  - Mid (800-2000Hz): Primary card sound
  - High (3000-8000Hz): Magical shimmer

#### card_play2.ogg
- **Duration**: 0.5 seconds
- **Volume**: -12dB
- **Pitch**: D4 (293.66 Hz base)
- **Character**: Slightly longer with higher pitch
- **Usage**: Variation for card play diversity
- **Differences**: 10% higher pitch, 25% longer duration

#### card_play3.ogg
- **Duration**: 0.35 seconds
- **Volume**: -12dB
- **Pitch**: E4 (329.63 Hz base)
- **Character**: Shortest and most crisp
- **Usage**: Quick card plays, urgent moments
- **Differences**: Highest pitch, fastest response

### Card Draw Sound
**File**: `card_draw.ogg`
- **Duration**: 0.6 seconds
- **Volume**: -15dB
- **Pitch**: A3 (220 Hz base)
- **Character**: Smooth card sliding with gentle acquisition
- **Usage**: Drawing cards from deck to hand
- **Design**: Lower pitch than card play, longer duration

### Combat State Sounds
**Files**: `combat_start.ogg`, `combat_end.ogg`, `turn_start.ogg`

#### combat_start.ogg
- **Duration**: 2.0 seconds
- **Volume**: -8dB
- **Character**: Epic combat initiation with energy buildup
- **Layers**:
  - Deep magical rumble (0.0-0.3s)
  - Energy crackling (0.3-0.8s)
  - Harmonic progression (0.8-1.2s)
  - Sustained resonance (1.2-2.0s)
- **Integration**: Synchronized with camera transition

#### combat_end.ogg
- **Duration**: 1.8 seconds
- **Volume**: -10dB
- **Character**: Resolution chord with magical dissipation
- **Progression**: Victory → Peace → Return to normalcy
- **Emotional Tone**: Positive regardless of outcome

#### turn_start.ogg
- **Duration**: 0.8 seconds
- **Volume**: -14dB
- **Character**: Gentle notification with energy pulse
- **Usage**: Current turn participant focus
- **Design**: Non-intrusive but clearly audible

## Action Sounds

### Attack Sounds
**Files**: `attack_hit1.ogg`, `attack_hit2.ogg`, `attack_hit3.ogg`

#### attack_hit1.ogg
- **Duration**: 0.5 seconds
- **Volume**: -10dB
- **Character**: Satisfying physical impact with magical enhancement
- **Layers**: Physical impact + metallic resonance + magical sparkles
- **Usage**: Standard attack feedback

#### attack_hit2.ogg
- **Duration**: 0.45 seconds
- **Volume**: -10dB
- **Character**: Sharper and more focused than variant 1
- **Differences**: 10% shorter, higher pitch, less reverb
- **Usage**: Medium damage attacks

#### attack_hit3.ogg
- **Duration**: 0.6 seconds
- **Volume**: -9dB
- **Character**: Heaviest and most resonant
- **Usage**: High damage, critical hits, legendary effects
- **Design**: Deepest pitch, longest duration, richest harmonics

### Defensive Sound
**File**: `block_activate.ogg`
- **Duration**: 0.7 seconds
- **Volume**: -11dB
- **Character**: Protective barrier with crystalline qualities
- **Emotional Tone**: Protective and reassuring
- **Usage**: Defense cards, shield effects

### Spell Sounds
**Files**: `spell_cast1.ogg`, `spell_cast2.ogg`

#### spell_cast1.ogg
- **Duration**: 1.2 seconds
- **Volume**: -9dB
- **Character**: Arcane spellcasting with energy buildup
- **Progression**: Gathering → Buildup → Release → Dissipation
- **Usage**: Primary magical effects

#### spell_cast2.ogg
- **Duration**: 1.0 seconds
- **Volume**: -9dB
- **Character**: More direct and focused magic
- **Usage**: Offensive spells, quick magical effects
- **Differences**: Sharper attack, more forceful

### Healing Sound
**File**: `heal.ogg`
- **Duration**: 1.5 seconds
- **Volume**: -12dB
- **Character**: Restorative and peaceful
- **Emotional Impact**: Relief, comfort, hope
- **Usage**: Healing cards, beneficial effects

## UI Sounds

### Pack Opening Sounds
**Files**: `pack_open.ogg`, `rare_card.ogg`, `legendary_card.ogg`

#### pack_open.ogg
- **Duration**: 1.8 seconds
- **Volume**: -8dB
- **Character**: Exciting revelation with anticipation buildup
- **Progression**: Unsealing → Energy release → Revelation → Settling
- **Usage**: Standard card pack opening

#### rare_card.ogg
- **Duration**: 2.2 seconds
- **Volume**: -6dB
- **Character**: More elaborate and prestigious
- **Usage**: Rare card reveals
- **Enhancement**: Richer harmonics, orchestral elements

#### legendary_card.ogg
- **Duration**: 3.0 seconds
- **Volume**: -4dB
- **Character**: Epic and ultimate achievement
- **Usage**: Legendary card reveals
- **Design**: Maximum complexity, deepest bass, highest sparkles

### Interface Sounds
**Files**: `ui_hover.ogg`, `ui_click.ogg`

#### ui_hover.ogg
- **Duration**: 0.2 seconds
- **Volume**: -18dB
- **Character**: Subtle and light
- **Usage**: Button hovers, card highlights
- **Design**: Non-intrusive, suitable for repetition

#### ui_click.ogg
- **Duration**: 0.3 seconds
- **Volume**: -15dB
- **Character**: Satisfying confirmation
- **Usage**: Button clicks, selections
- **Design**: Immediate response, clear confirmation

### System Sounds
**Files**: `deck_shuffle.ogg`, `energy_gain.ogg`, `status_effect.ogg`

#### deck_shuffle.ogg
- **Duration**: 1.5 seconds
- **Volume**: -13dB
- **Character**: Realistic card shuffling with magical enhancement
- **Usage**: Deck building, turn preparation
- **Elements**: Paper texture, rhythmic movement, magical completion

#### energy_gain.ogg
- **Duration**: 0.8 seconds
- **Volume**: -11dB
- **Character**: Energizing and empowering
- **Usage**: Energy point gains, power-ups
- **Design**: Rising pitch progression, positive reinforcement

#### status_effect.ogg
- **Duration**: 1.0 seconds
- **Volume**: -12dB
- **Character**: Mystical and ethereal
- **Usage**: Buff/debuff application
- **Design**: Neutral tone, swirling energy patterns

## Integration with Combat Camera System

### Spatial Audio Considerations
- **Top-Down View**: Sounds designed to work well with overhead perspective
- **Distance Scaling**: Volume adjustments for camera height changes
- **Focus Tracking**: Audio emphasis follows camera focus
- **Transition Sync**: Combat sounds synchronized with camera transitions

### Camera-Specific Adaptations
- **Combat Start**: Timed with camera transition to top-down view
- **Turn Changes**: Audio cues complement camera focus changes
- **Combat End**: Synchronized with camera return to normal view
- **Emergency Reset**: Audio feedback for camera reset actions

## Production Guidelines

### Audio Creation Process
1. **Base Sound Design**: Create core sound elements
2. **Magical Enhancement**: Add fantasy/magical elements
3. **Frequency Balancing**: Ensure proper frequency distribution
4. **Volume Normalization**: Match specified dB levels
5. **Format Conversion**: Convert to OGG Vorbis
6. **Quality Testing**: Verify in-game audio quality

### Quality Assurance
- **Frequency Analysis**: Verify frequency content matches specifications
- **Volume Consistency**: Ensure consistent volume levels within categories
- **Loop Testing**: Test sounds that may play repeatedly
- **Compression Quality**: Verify OGG compression maintains quality
- **Platform Testing**: Test on various devices and speakers

### Implementation Notes
- **Random Variation**: Use multiple variants to prevent repetition
- **Volume Scaling**: Implement dynamic volume based on game settings
- **Spatial Positioning**: Consider 3D audio positioning for immersion
- **Performance**: Optimize file sizes for mobile platforms

## File Structure
```
resource_pack/sounds/combat/
├── card_play1.ogg          # Primary card play sound
├── card_play2.ogg          # Card play variation 2
├── card_play3.ogg          # Card play variation 3
├── card_draw.ogg           # Card drawing sound
├── combat_start.ogg        # Combat initiation
├── combat_end.ogg          # Combat conclusion
├── turn_start.ogg          # Turn notification
├── attack_hit1.ogg         # Attack impact 1
├── attack_hit2.ogg         # Attack impact 2
├── attack_hit3.ogg         # Attack impact 3
├── block_activate.ogg      # Defense activation
├── spell_cast1.ogg         # Spell casting 1
├── spell_cast2.ogg         # Spell casting 2
├── heal.ogg                # Healing effect
├── pack_open.ogg           # Pack opening
├── rare_card.ogg           # Rare card reveal
├── legendary_card.ogg      # Legendary card reveal
├── ui_hover.ogg            # UI hover feedback
├── ui_click.ogg            # UI click feedback
├── deck_shuffle.ogg        # Deck shuffling
├── energy_gain.ogg         # Energy acquisition
└── status_effect.ogg       # Status effect application
```

---

**Version**: 1.0.0  
**Last Updated**: 2024-12-19  
**Audio Designer**: Card Combat Development Team
