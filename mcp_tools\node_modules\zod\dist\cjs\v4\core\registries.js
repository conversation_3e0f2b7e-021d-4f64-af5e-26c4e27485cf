"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalRegistry = exports.$ZodRegistry = exports.$input = exports.$output = void 0;
exports.registry = registry;
exports.$output = Symbol("ZodOutput");
exports.$input = Symbol("ZodInput");
class $ZodRegistry {
    constructor() {
        this._map = new WeakMap();
        this._idmap = new Map();
    }
    add(schema, ..._meta) {
        const meta = _meta[0];
        this._map.set(schema, meta);
        if (meta && typeof meta === "object" && "id" in meta) {
            if (this._idmap.has(meta.id)) {
                throw new Error(`ID ${meta.id} already exists in the registry`);
            }
            this._idmap.set(meta.id, schema);
        }
        return this;
    }
    remove(schema) {
        this._map.delete(schema);
        return this;
    }
    get(schema) {
        // return this._map.get(schema) as any;
        // inherit metadata
        const p = schema._zod.parent;
        if (p) {
            const pm = { ...(this.get(p) ?? {}) };
            delete pm.id; // do not inherit id
            return { ...pm, ...this._map.get(schema) };
        }
        return this._map.get(schema);
    }
    has(schema) {
        return this._map.has(schema);
    }
}
exports.$ZodRegistry = $ZodRegistry;
// registries
function registry() {
    return new $ZodRegistry();
}
exports.globalRegistry = registry();
