/**
 * RoguelikeManager.js
 * Core system for managing dungeon runs, floor progression, and run state
 */

import { world, system } from "@minecraft/server";

export class RoguelikeManager {
    constructor(combatManager, playerDataManager, classManager) {
        this.combatManager = combatManager;
        this.playerDataManager = playerDataManager;
        this.classManager = classManager;
        
        // Active runs - Map of player ID to run data
        this.activeRuns = new Map();
        
        // Run configurations
        this.runTypes = {
            normal: {
                name: "Normal Run",
                description: "Standard dungeon with balanced difficulty",
                floors: 10,
                difficultyMultiplier: 1.0,
                rewardMultiplier: 1.0,
                unlockRequirement: null
            },
            hard: {
                name: "Hard Run", 
                description: "Increased difficulty with better rewards",
                floors: 12,
                difficultyMultiplier: 1.5,
                rewardMultiplier: 1.5,
                unlockRequirement: "complete_normal_run"
            },
            nightmare: {
                name: "Nightmare Run",
                description: "Extreme challenge for the bravest adventurers",
                floors: 15,
                difficultyMultiplier: 2.0,
                rewardMultiplier: 2.0,
                unlockRequirement: "complete_hard_run"
            },
            endless: {
                name: "Endless Run",
                description: "See how far you can go in an infinite dungeon",
                floors: -1, // Infinite
                difficultyMultiplier: 1.2,
                rewardMultiplier: 1.3,
                unlockRequirement: "complete_nightmare_run"
            }
        };
        
        // Floor types and their properties
        this.floorTypes = {
            combat: { weight: 70, description: "Standard combat encounter" },
            elite: { weight: 15, description: "Powerful elite enemy" },
            event: { weight: 10, description: "Random event or choice" },
            shop: { weight: 3, description: "Merchant with cards and upgrades" },
            boss: { weight: 2, description: "Challenging boss fight" }
        };
        
        console.log("🏰 RoguelikeManager initialized");
    }
    
    /**
     * Start a new dungeon run for a player
     */
    startRun(player, runType = "normal") {
        const playerId = player.id;
        
        // Check if player already has an active run
        if (this.activeRuns.has(playerId)) {
            player.sendMessage("§cYou already have an active dungeon run! Use /combat dungeon abandon to quit your current run.");
            return false;
        }
        
        // Validate run type
        if (!this.runTypes[runType]) {
            player.sendMessage("§cInvalid run type. Available: normal, hard, nightmare, endless");
            return false;
        }
        
        const runConfig = this.runTypes[runType];
        
        // Check unlock requirements
        if (runConfig.unlockRequirement && !this.checkUnlockRequirement(player, runConfig.unlockRequirement)) {
            player.sendMessage(`§cYou haven't unlocked ${runConfig.name} yet!`);
            return false;
        }
        
        // Create run data
        const runData = {
            playerId: playerId,
            runType: runType,
            config: runConfig,
            currentFloor: 1,
            maxFloor: runConfig.floors,
            startTime: Date.now(),
            
            // Run-specific player state
            runHealth: 100,
            maxRunHealth: 100,
            runEnergy: 100,
            maxRunEnergy: 100,
            
            // Run progress
            enemiesDefeated: 0,
            cardsCollected: 0,
            eventsCompleted: 0,
            
            // Temporary run modifications
            temporaryCards: [],
            cardUpgrades: new Map(),
            activeArtifacts: [],
            runModifiers: [],
            
            // Floor history
            floorHistory: [],
            currentEncounter: null,
            
            // Rewards accumulated
            pendingRewards: [],
            metaProgressionEarned: 0
        };
        
        this.activeRuns.set(playerId, runData);
        
        // Notify player
        player.sendMessage("§6🏰 DUNGEON RUN STARTED! 🏰");
        player.sendMessage(`§e${runConfig.name} - Floor 1/${runConfig.floors === -1 ? '∞' : runConfig.floors}`);
        player.sendMessage("§7Prepare for your first encounter...");
        
        // Generate first floor
        system.runTimeout(() => {
            this.generateFloor(player);
        }, 60); // 3 second delay
        
        return true;
    }
    
    /**
     * Get current run data for a player
     */
    getPlayerRun(player) {
        return this.activeRuns.get(player.id);
    }
    
    /**
     * Check if player has an active run
     */
    hasActiveRun(player) {
        return this.activeRuns.has(player.id);
    }
    
    /**
     * Generate the current floor encounter
     */
    generateFloor(player) {
        const runData = this.getPlayerRun(player);
        if (!runData) return;
        
        const floorType = this.determineFloorType(runData);
        const encounter = this.createEncounter(runData, floorType);
        
        runData.currentEncounter = encounter;
        runData.floorHistory.push({
            floor: runData.currentFloor,
            type: floorType,
            timestamp: Date.now()
        });
        
        // Present the encounter to the player
        this.presentEncounter(player, encounter);
    }
    
    /**
     * Determine what type of floor this should be
     */
    determineFloorType(runData) {
        const floor = runData.currentFloor;
        const maxFloor = runData.maxFloor;
        
        // Boss floors
        if (maxFloor > 0 && (floor === maxFloor || floor % 10 === 0)) {
            return "boss";
        }
        
        // Elite floors (every 3rd floor after floor 3)
        if (floor > 3 && floor % 3 === 0) {
            return "elite";
        }
        
        // Shop floors (every 5th floor)
        if (floor % 5 === 0 && floor !== maxFloor) {
            return "shop";
        }
        
        // Random selection for other floors
        const weights = this.floorTypes;
        const totalWeight = Object.values(weights).reduce((sum, type) => sum + type.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const [type, data] of Object.entries(weights)) {
            random -= data.weight;
            if (random <= 0) {
                return type;
            }
        }
        
        return "combat"; // Fallback
    }
    
    /**
     * Create an encounter based on floor type
     */
    createEncounter(runData, floorType) {
        const floor = runData.currentFloor;
        const difficulty = this.calculateDifficulty(runData);
        
        const encounter = {
            type: floorType,
            floor: floor,
            difficulty: difficulty,
            completed: false,
            rewards: []
        };
        
        switch (floorType) {
            case "combat":
                encounter.enemies = this.generateEnemies(difficulty, "normal");
                encounter.rewards = this.generateCombatRewards(difficulty);
                break;
                
            case "elite":
                encounter.enemies = this.generateEnemies(difficulty * 1.5, "elite");
                encounter.rewards = this.generateEliteRewards(difficulty);
                break;
                
            case "boss":
                encounter.enemies = this.generateEnemies(difficulty * 2, "boss");
                encounter.rewards = this.generateBossRewards(difficulty);
                break;
                
            case "event":
                encounter.event = this.generateRandomEvent(difficulty);
                break;
                
            case "shop":
                encounter.shop = this.generateShop(difficulty);
                break;
        }
        
        return encounter;
    }
    
    /**
     * Calculate current difficulty based on floor and run type
     */
    calculateDifficulty(runData) {
        const baseFloor = runData.currentFloor;
        const multiplier = runData.config.difficultyMultiplier;
        
        // Exponential scaling with floor
        return Math.floor(baseFloor * multiplier * (1 + (baseFloor - 1) * 0.1));
    }
    
    /**
     * Present an encounter to the player
     */
    presentEncounter(player, encounter) {
        player.sendMessage(`§6=== FLOOR ${encounter.floor} ===`);
        
        switch (encounter.type) {
            case "combat":
                player.sendMessage("§c⚔ COMBAT ENCOUNTER ⚔");
                player.sendMessage(`§7Enemies: ${encounter.enemies.map(e => e.name).join(", ")}`);
                player.sendMessage("§eAttack any enemy to begin combat!");
                break;
                
            case "elite":
                player.sendMessage("§6👑 ELITE ENCOUNTER 👑");
                player.sendMessage(`§7Elite Enemy: ${encounter.enemies[0].name}`);
                player.sendMessage("§eThis will be a challenging fight!");
                break;
                
            case "boss":
                player.sendMessage("§4💀 BOSS ENCOUNTER 💀");
                player.sendMessage(`§7Boss: ${encounter.enemies[0].name}`);
                player.sendMessage("§cPrepare for the ultimate challenge!");
                break;
                
            case "event":
                player.sendMessage("§d✨ SPECIAL EVENT ✨");
                player.sendMessage(`§7${encounter.event.description}`);
                break;
                
            case "shop":
                player.sendMessage("§a🏪 MERCHANT SHOP 🏪");
                player.sendMessage("§7Browse cards and upgrades for sale!");
                break;
        }
    }
    
    /**
     * Complete the current floor and advance
     */
    completeFloor(player, success = true) {
        const runData = this.getPlayerRun(player);
        if (!runData || !runData.currentEncounter) return;
        
        if (success) {
            runData.currentEncounter.completed = true;
            
            // Award rewards
            this.awardFloorRewards(player, runData.currentEncounter);
            
            // Check if run is complete
            if (runData.currentFloor >= runData.maxFloor && runData.maxFloor > 0) {
                this.completeRun(player, true);
                return;
            }
            
            // Advance to next floor
            runData.currentFloor++;
            player.sendMessage(`§a✅ Floor ${runData.currentFloor - 1} completed!`);
            player.sendMessage(`§eAdvancing to Floor ${runData.currentFloor}...`);
            
            // Generate next floor after delay
            system.runTimeout(() => {
                this.generateFloor(player);
            }, 100); // 5 second delay
            
        } else {
            // Floor failed - end run
            this.completeRun(player, false);
        }
    }
    
    /**
     * Complete a dungeon run
     */
    completeRun(player, success) {
        const runData = this.getPlayerRun(player);
        if (!runData) return;
        
        const duration = Date.now() - runData.startTime;
        const durationMinutes = Math.floor(duration / 60000);
        
        if (success) {
            player.sendMessage("§6🎉 DUNGEON RUN COMPLETED! 🎉");
            player.sendMessage(`§a✅ ${runData.config.name} cleared in ${durationMinutes} minutes!`);
            
            // Award completion rewards
            this.awardCompletionRewards(player, runData);
            
        } else {
            player.sendMessage("§c💀 DUNGEON RUN FAILED 💀");
            player.sendMessage(`§7You reached Floor ${runData.currentFloor} in ${durationMinutes} minutes.`);
            
            // Award partial rewards
            this.awardPartialRewards(player, runData);
        }
        
        // Clean up
        this.activeRuns.delete(player.id);
    }
    
    /**
     * Abandon current run
     */
    abandonRun(player) {
        const runData = this.getPlayerRun(player);
        if (!runData) {
            player.sendMessage("§cYou don't have an active dungeon run.");
            return false;
        }
        
        player.sendMessage("§7🚪 Dungeon run abandoned.");
        player.sendMessage("§eYou can start a new run anytime with /combat dungeon start");
        
        this.activeRuns.delete(player.id);
        return true;
    }
    
    /**
     * Get run status for a player
     */
    getRunStatus(player) {
        const runData = this.getPlayerRun(player);
        if (!runData) {
            return "No active dungeon run";
        }
        
        const status = [
            `§6=== DUNGEON RUN STATUS ===`,
            `§eRun Type: ${runData.config.name}`,
            `§eFloor: ${runData.currentFloor}/${runData.maxFloor === -1 ? '∞' : runData.maxFloor}`,
            `§eHealth: ${runData.runHealth}/${runData.maxRunHealth}`,
            `§eEnergy: ${runData.runEnergy}/${runData.maxRunEnergy}`,
            `§eEnemies Defeated: ${runData.enemiesDefeated}`,
            `§eCards Collected: ${runData.cardsCollected}`,
            `§eEvents Completed: ${runData.eventsCompleted}`
        ];
        
        return status.join("\n");
    }
    
    /**
     * Check if player meets unlock requirement
     */
    checkUnlockRequirement(player, requirement) {
        // This would check meta-progression data
        // For now, return true (will implement with MetaProgression system)
        return true;
    }
    
    /**
     * Generate enemies for an encounter
     */
    generateEnemies(difficulty, type) {
        // Placeholder - will be implemented with DungeonGenerator
        return [{
            name: `Level ${difficulty} ${type} Enemy`,
            health: difficulty * 20,
            damage: difficulty * 5,
            type: type
        }];
    }
    
    /**
     * Generate rewards for different encounter types
     */
    generateCombatRewards(difficulty) {
        return [`${difficulty} Gold`, "Random Card"];
    }
    
    generateEliteRewards(difficulty) {
        return [`${difficulty * 2} Gold`, "Rare Card", "Card Upgrade"];
    }
    
    generateBossRewards(difficulty) {
        return [`${difficulty * 5} Gold`, "Legendary Card", "Artifact", "Meta Progress"];
    }
    
    /**
     * Award rewards to player
     */
    awardFloorRewards(player, encounter) {
        if (encounter.rewards && encounter.rewards.length > 0) {
            player.sendMessage("§6🎁 REWARDS EARNED:");
            encounter.rewards.forEach(reward => {
                player.sendMessage(`§e+ ${reward}`);
            });
        }
    }
    
    awardCompletionRewards(player, runData) {
        player.sendMessage("§6🏆 COMPLETION BONUS:");
        player.sendMessage("§e+ Major Meta Progression");
        player.sendMessage("§e+ Unlock New Content");
    }
    
    awardPartialRewards(player, runData) {
        const floorsCompleted = runData.currentFloor - 1;
        if (floorsCompleted > 0) {
            player.sendMessage("§6📦 PARTIAL REWARDS:");
            player.sendMessage(`§e+ ${floorsCompleted} Meta Progress Points`);
        }
    }
}
