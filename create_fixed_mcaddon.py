#!/usr/bin/env python3
"""
Create a fixed .mcaddon package for Minecraft Bedrock 1.21.90 compatibility
"""

import os
import zipfile
import json
from pathlib import Path

def create_mcaddon():
    """Create the .mcaddon package with 1.21.90 compatibility fixes"""
    
    # Define paths
    behavior_pack_dir = "behavior_pack"
    resource_pack_dir = "resource_pack"
    output_file = "card_combat_1_21_90_with_classes.mcaddon"
    
    # Remove existing file if it exists
    if os.path.exists(output_file):
        os.remove(output_file)
        print(f"Removed existing {output_file}")
    
    # Create the .mcaddon file (which is just a zip file)
    with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as mcaddon:
        
        # Add behavior pack files
        print("Adding behavior pack files...")
        for root, dirs, files in os.walk(behavior_pack_dir):
            for file in files:
                # Skip any _fixed files that might still exist
                if "_fixed" in file:
                    continue
                    
                file_path = os.path.join(root, file)
                # Calculate the archive path (relative to behavior_pack_dir)
                archive_path = os.path.relpath(file_path, ".")
                mcaddon.write(file_path, archive_path)
                print(f"  Added: {archive_path}")
        
        # Add resource pack files
        print("Adding resource pack files...")
        for root, dirs, files in os.walk(resource_pack_dir):
            for file in files:
                file_path = os.path.join(root, file)
                # Calculate the archive path (relative to resource_pack_dir)
                archive_path = os.path.relpath(file_path, ".")
                mcaddon.write(file_path, archive_path)
                print(f"  Added: {archive_path}")
    
    # Get file size
    file_size = os.path.getsize(output_file)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"\n✅ Successfully created {output_file}")
    print(f"📦 Package size: {file_size_mb:.2f} MB ({file_size:,} bytes)")
    
    # Count files in the package
    with zipfile.ZipFile(output_file, 'r') as mcaddon:
        file_count = len(mcaddon.namelist())
        print(f"📁 Total files: {file_count}")
    
    print(f"\n🎮 Ready for Minecraft Bedrock 1.21.90!")
    print(f"📋 Installation: Import {output_file} into Minecraft")

if __name__ == "__main__":
    create_mcaddon()
