/**
 * StatusEffectManager - Comprehensive status effect system
 */

import { world, system } from "@minecraft/server";

export class StatusEffectManager {
    constructor(playerDataManager, damageCalculator) {
        this.playerDataManager = playerDataManager;
        this.damageCalculator = damageCalculator;
        
        // Status effect definitions
        this.statusEffects = new Map();
        this.initializeStatusEffects();
        
        // Effect processing intervals
        this.processingIntervals = new Map();
    }
    
    /**
     * Initialize all status effect definitions
     */
    initializeStatusEffects() {
        // Damage over time effects
        this.registerStatusEffect("burn", {
            name: "Burn",
            type: "damage_over_time",
            color: "§c",
            icon: "🔥",
            description: "Takes fire damage each turn",
            onApply: this.onBurnApply.bind(this),
            onTick: this.onBurnTick.bind(this),
            onRemove: this.onBurnRemove.bind(this)
        });
        
        this.registerStatusEffect("poison", {
            name: "Poison",
            type: "damage_over_time",
            color: "§2",
            icon: "☠",
            description: "Takes poison damage each turn",
            onApply: this.onPoisonApply.bind(this),
            onTick: this.onPoisonTick.bind(this),
            onRemove: this.onPoisonRemove.bind(this)
        });
        
        this.registerStatusEffect("bleed", {
            name: "Bleeding",
            type: "damage_over_time",
            color: "§4",
            icon: "🩸",
            description: "Loses health each turn",
            onTick: this.onBleedTick.bind(this)
        });
        
        // Healing over time effects
        this.registerStatusEffect("regeneration", {
            name: "Regeneration",
            type: "healing_over_time",
            color: "§a",
            icon: "💚",
            description: "Restores health each turn",
            onTick: this.onRegenerationTick.bind(this)
        });
        
        // Buff effects
        this.registerStatusEffect("strength", {
            name: "Strength",
            type: "buff",
            color: "§c",
            icon: "💪",
            description: "Increases damage dealt",
            onApply: this.onStrengthApply.bind(this),
            onRemove: this.onStrengthRemove.bind(this)
        });
        
        this.registerStatusEffect("defense", {
            name: "Defense",
            type: "buff",
            color: "§9",
            icon: "🛡",
            description: "Reduces damage taken",
            onApply: this.onDefenseApply.bind(this)
        });
        
        this.registerStatusEffect("speed", {
            name: "Speed",
            type: "buff",
            color: "§b",
            icon: "💨",
            description: "Increases turn priority",
            onApply: this.onSpeedApply.bind(this)
        });
        
        this.registerStatusEffect("focus", {
            name: "Focus",
            type: "buff",
            color: "§e",
            icon: "🎯",
            description: "Next card costs 1 less energy",
            onApply: this.onFocusApply.bind(this)
        });
        
        // Debuff effects
        this.registerStatusEffect("weakness", {
            name: "Weakness",
            type: "debuff",
            color: "§8",
            icon: "😵",
            description: "Reduces damage dealt",
            onApply: this.onWeaknessApply.bind(this)
        });
        
        this.registerStatusEffect("vulnerability", {
            name: "Vulnerability",
            type: "debuff",
            color: "§c",
            icon: "💔",
            description: "Increases damage taken",
            onApply: this.onVulnerabilityApply.bind(this)
        });
        
        this.registerStatusEffect("slow", {
            name: "Slow",
            type: "debuff",
            color: "§7",
            icon: "🐌",
            description: "Decreases turn priority",
            onApply: this.onSlowApply.bind(this)
        });
        
        this.registerStatusEffect("silence", {
            name: "Silence",
            type: "debuff",
            color: "§5",
            icon: "🤐",
            description: "Cannot play spell cards",
            onApply: this.onSilenceApply.bind(this)
        });
        
        this.registerStatusEffect("stun", {
            name: "Stun",
            type: "debuff",
            color: "§6",
            icon: "⭐",
            description: "Cannot take actions",
            onApply: this.onStunApply.bind(this)
        });
        
        // Special effects
        this.registerStatusEffect("frozen", {
            name: "Frozen",
            type: "special",
            color: "§b",
            icon: "❄",
            description: "Cannot act, vulnerable to fire",
            onApply: this.onFrozenApply.bind(this),
            onRemove: this.onFrozenRemove.bind(this)
        });
        
        this.registerStatusEffect("invisible", {
            name: "Invisible",
            type: "special",
            color: "§7",
            icon: "👻",
            description: "Cannot be targeted by attacks",
            onApply: this.onInvisibleApply.bind(this)
        });
        
        this.registerStatusEffect("reflect", {
            name: "Reflect",
            type: "special",
            color: "§d",
            icon: "🪞",
            description: "Reflects damage back to attacker",
            onApply: this.onReflectApply.bind(this)
        });
    }
    
    /**
     * Register a status effect
     */
    registerStatusEffect(id, definition) {
        this.statusEffects.set(id, definition);
    }
    
    /**
     * Apply status effect to entity
     */
    applyStatusEffect(entity, effectId, data = {}) {
        const effectDef = this.statusEffects.get(effectId);
        if (!effectDef) {
            console.warn(`Unknown status effect: ${effectId}`);
            return false;
        }
        
        const entityData = this.playerDataManager.getPlayerData(entity);
        if (!entityData) return false;
        
        // Check if effect already exists
        const existingEffect = entityData.statusEffects.get(effectId);
        if (existingEffect) {
            // Stack or refresh effect
            this.stackStatusEffect(entity, effectId, existingEffect, data);
        } else {
            // Apply new effect
            const effectData = {
                duration: data.duration || 3,
                amount: data.amount || 1,
                damage: data.damage || 0,
                healing: data.healing || 0,
                appliedAt: Date.now(),
                ...data
            };
            
            entityData.statusEffects.set(effectId, effectData);
            
            // Call onApply handler
            if (effectDef.onApply) {
                effectDef.onApply(entity, effectData);
            }
            
            // Notify entity
            this.notifyStatusEffect(entity, effectDef, "applied");
            
            // Visual effects
            this.showStatusEffectVisual(entity, effectDef, "apply");
        }
        
        this.playerDataManager.updatePlayerData(entity, entityData);
        return true;
    }
    
    /**
     * Stack or refresh existing status effect
     */
    stackStatusEffect(entity, effectId, existingEffect, newData) {
        const effectDef = this.statusEffects.get(effectId);
        
        // Different stacking behaviors
        switch (effectDef.type) {
            case "damage_over_time":
                // Refresh duration, stack damage
                existingEffect.duration = Math.max(existingEffect.duration, newData.duration || 3);
                existingEffect.damage = Math.max(existingEffect.damage, newData.damage || existingEffect.damage);
                break;
                
            case "buff":
            case "debuff":
                // Refresh duration, stack amount up to limit
                existingEffect.duration = Math.max(existingEffect.duration, newData.duration || 3);
                existingEffect.amount = Math.min(existingEffect.amount + (newData.amount || 1), 5);
                break;
                
            case "special":
                // Just refresh duration
                existingEffect.duration = Math.max(existingEffect.duration, newData.duration || 3);
                break;
                
            default:
                // Default: refresh duration
                existingEffect.duration = newData.duration || existingEffect.duration;
        }
        
        this.notifyStatusEffect(entity, effectDef, "refreshed");
    }
    
    /**
     * Remove status effect from entity
     */
    removeStatusEffect(entity, effectId) {
        const effectDef = this.statusEffects.get(effectId);
        const entityData = this.playerDataManager.getPlayerData(entity);
        
        if (!effectDef || !entityData) return false;
        
        const effectData = entityData.statusEffects.get(effectId);
        if (!effectData) return false;
        
        // Call onRemove handler
        if (effectDef.onRemove) {
            effectDef.onRemove(entity, effectData);
        }
        
        // Remove effect
        entityData.statusEffects.delete(effectId);
        
        // Notify entity
        this.notifyStatusEffect(entity, effectDef, "removed");
        
        // Visual effects
        this.showStatusEffectVisual(entity, effectDef, "remove");
        
        this.playerDataManager.updatePlayerData(entity, entityData);
        return true;
    }
    
    /**
     * Process all status effects for an entity
     */
    processStatusEffects(entity, timing = "turn_start") {
        const entityData = this.playerDataManager.getPlayerData(entity);
        if (!entityData) return;
        
        const effectsToRemove = [];
        
        for (const [effectId, effectData] of entityData.statusEffects) {
            const effectDef = this.statusEffects.get(effectId);
            if (!effectDef) continue;
            
            // Call onTick handler
            if (effectDef.onTick && timing === "turn_start") {
                effectDef.onTick(entity, effectData);
            }
            
            // Reduce duration
            if (timing === "turn_end") {
                effectData.duration--;
                if (effectData.duration <= 0) {
                    effectsToRemove.push(effectId);
                }
            }
        }
        
        // Remove expired effects
        for (const effectId of effectsToRemove) {
            this.removeStatusEffect(entity, effectId);
        }
    }
    
    /**
     * Get all active status effects for display
     */
    getActiveStatusEffects(entity) {
        const entityData = this.playerDataManager.getPlayerData(entity);
        if (!entityData) return [];
        
        const activeEffects = [];
        
        for (const [effectId, effectData] of entityData.statusEffects) {
            const effectDef = this.statusEffects.get(effectId);
            if (effectDef) {
                activeEffects.push({
                    id: effectId,
                    name: effectDef.name,
                    icon: effectDef.icon,
                    color: effectDef.color,
                    description: effectDef.description,
                    duration: effectData.duration,
                    amount: effectData.amount || 1
                });
            }
        }
        
        return activeEffects;
    }
    
    /**
     * Check if entity has specific status effect
     */
    hasStatusEffect(entity, effectId) {
        const entityData = this.playerDataManager.getPlayerData(entity);
        return entityData ? entityData.statusEffects.has(effectId) : false;
    }
    
    /**
     * Get status effect data
     */
    getStatusEffectData(entity, effectId) {
        const entityData = this.playerDataManager.getPlayerData(entity);
        return entityData ? entityData.statusEffects.get(effectId) : null;
    }
    
    /**
     * Status Effect Handlers
     */
    
    // Burn effect handlers
    onBurnApply(entity, effectData) {
        entity.dimension.spawnParticle("minecraft:lava_particle", entity.location);
    }
    
    onBurnTick(entity, effectData) {
        if (effectData.damage > 0) {
            this.damageCalculator.applyCalculatedDamage(
                null, entity, effectData.damage, "fire", ["burn_damage"]
            );
        }
        entity.dimension.spawnParticle("minecraft:lava_particle", entity.location);
    }
    
    onBurnRemove(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§7The burning sensation fades away.");
        }
    }
    
    // Poison effect handlers
    onPoisonApply(entity, effectData) {
        entity.dimension.spawnParticle("minecraft:villager_angry", entity.location);
    }
    
    onPoisonTick(entity, effectData) {
        if (effectData.damage > 0) {
            this.damageCalculator.applyCalculatedDamage(
                null, entity, effectData.damage, "poison", ["poison_damage"]
            );
        }
        entity.dimension.spawnParticle("minecraft:villager_angry", entity.location);
    }
    
    // Regeneration effect handlers
    onRegenerationTick(entity, effectData) {
        if (effectData.healing > 0) {
            const health = entity.getComponent("health");
            if (health) {
                const newHealth = Math.min(health.defaultValue, health.currentValue + effectData.healing);
                health.setCurrentValue(newHealth);
                entity.dimension.spawnParticle("minecraft:heart_particle", entity.location);
            }
        }
    }
    
    // Buff effect handlers
    onStrengthApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage(`§cYou feel stronger! (+${effectData.amount} damage)`);
        }
    }
    
    onStrengthRemove(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§7Your strength returns to normal.");
        }
    }
    
    onDefenseApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage(`§9You feel more resilient! (-${effectData.amount} damage taken)`);
        }
    }
    
    onSpeedApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§bYou feel faster!");
        }
    }
    
    onFocusApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§eYou feel focused! Next card costs 1 less energy.");
        }
    }
    
    // Debuff effect handlers
    onWeaknessApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage(`§8You feel weaker... (-${effectData.amount} damage)`);
        }
    }
    
    onVulnerabilityApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage(`§cYou feel vulnerable! (+${effectData.amount} damage taken)`);
        }
    }
    
    onSlowApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§7You feel sluggish...");
        }
    }
    
    onSilenceApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§5You cannot speak! Spell cards are disabled.");
        }
    }
    
    onStunApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§6You are stunned! Cannot act this turn.");
        }
    }
    
    // Special effect handlers
    onFrozenApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§bYou are frozen solid!");
        }
        entity.dimension.spawnParticle("minecraft:snowflake_particle", entity.location);
    }
    
    onFrozenRemove(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§7The ice melts away.");
        }
    }
    
    onInvisibleApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§7You fade from view...");
        }
    }
    
    onReflectApply(entity, effectData) {
        if (entity.typeId === "minecraft:player") {
            entity.sendMessage("§dA magical barrier surrounds you!");
        }
        entity.dimension.spawnParticle("minecraft:enchanting_table_particle", entity.location);
    }
    
    /**
     * Utility methods
     */
    
    notifyStatusEffect(entity, effectDef, action) {
        if (entity.typeId === "minecraft:player") {
            const message = `${effectDef.color}${effectDef.icon} ${effectDef.name} ${action}!§r`;
            entity.sendMessage(message);
        }
    }
    
    showStatusEffectVisual(entity, effectDef, action) {
        const location = entity.location;
        
        switch (effectDef.type) {
            case "damage_over_time":
                entity.dimension.spawnParticle("minecraft:critical_hit_emitter", location);
                break;
            case "healing_over_time":
                entity.dimension.spawnParticle("minecraft:heart_particle", location);
                break;
            case "buff":
                entity.dimension.spawnParticle("minecraft:villager_happy", location);
                break;
            case "debuff":
                entity.dimension.spawnParticle("minecraft:villager_angry", location);
                break;
            case "special":
                entity.dimension.spawnParticle("minecraft:enchanting_table_particle", location);
                break;
        }
    }
    
    onBleedTick(entity, effectData) {
        if (effectData.damage > 0) {
            const health = entity.getComponent("health");
            if (health) {
                health.setCurrentValue(Math.max(1, health.currentValue - effectData.damage));
                entity.dimension.spawnParticle("minecraft:redstone_dust_particle", entity.location);
            }
        }
    }
}
