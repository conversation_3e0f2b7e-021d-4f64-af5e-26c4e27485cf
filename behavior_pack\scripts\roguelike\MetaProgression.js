/**
 * MetaProgression.js
 * System for persistent progression, unlocks, and improvements between dungeon runs
 */

export class MetaProgression {
    constructor(playerDataManager) {
        this.playerDataManager = playerDataManager;
        
        // Meta progression unlocks and their requirements
        this.unlocks = {
            // Run Types
            hard_run: {
                name: "Hard Difficulty",
                description: "Unlock challenging dungeon runs with better rewards",
                requirement: { type: "complete_runs", value: 1, difficulty: "normal" },
                category: "run_types"
            },
            nightmare_run: {
                name: "Nightmare Difficulty", 
                description: "Unlock extreme difficulty with legendary rewards",
                requirement: { type: "complete_runs", value: 3, difficulty: "hard" },
                category: "run_types"
            },
            endless_run: {
                name: "Endless Mode",
                description: "Unlock infinite dungeon runs",
                requirement: { type: "complete_runs", value: 1, difficulty: "nightmare" },
                category: "run_types"
            },
            
            // Starting Deck Upgrades
            improved_starter: {
                name: "Improved Starter Deck",
                description: "Start runs with better basic cards",
                requirement: { type: "meta_points", value: 50 },
                category: "starter_upgrades"
            },
            advanced_starter: {
                name: "Advanced Starter Deck",
                description: "Start runs with uncommon cards",
                requirement: { type: "meta_points", value: 150 },
                category: "starter_upgrades"
            },
            master_starter: {
                name: "Master Starter Deck",
                description: "Start runs with rare cards",
                requirement: { type: "meta_points", value: 300 },
                category: "starter_upgrades"
            },
            
            // Permanent Artifacts
            health_boost: {
                name: "Vitality Enhancement",
                description: "Start all runs with +20 max health",
                requirement: { type: "meta_points", value: 100 },
                category: "permanent_artifacts"
            },
            energy_boost: {
                name: "Energy Mastery",
                description: "Start all runs with +10 max energy",
                requirement: { type: "meta_points", value: 120 },
                category: "permanent_artifacts"
            },
            card_draw_boost: {
                name: "Mental Acuity",
                description: "Draw +1 card at start of each turn",
                requirement: { type: "meta_points", value: 200 },
                category: "permanent_artifacts"
            },
            
            // Class Enhancements
            warrior_mastery: {
                name: "Warrior Mastery",
                description: "Warrior ultimate charges 25% faster",
                requirement: { type: "class_runs", value: 10, class: "warrior" },
                category: "class_mastery"
            },
            mage_mastery: {
                name: "Mage Mastery", 
                description: "Mage ultimate charges 25% faster",
                requirement: { type: "class_runs", value: 10, class: "mage" },
                category: "class_mastery"
            },
            rogue_mastery: {
                name: "Rogue Mastery",
                description: "Rogue ultimate charges 25% faster", 
                requirement: { type: "class_runs", value: 10, class: "rogue" },
                category: "class_mastery"
            },
            
            // Special Unlocks
            artifact_shop: {
                name: "Artifact Merchant",
                description: "Unlock special shops that sell artifacts",
                requirement: { type: "artifacts_collected", value: 25 },
                category: "special"
            },
            card_forge: {
                name: "Card Forge",
                description: "Unlock ability to craft custom cards",
                requirement: { type: "cards_collected", value: 100 },
                category: "special"
            },
            time_rift: {
                name: "Time Rift Runs",
                description: "Unlock time-limited challenge runs",
                requirement: { type: "floors_cleared", value: 500 },
                category: "special"
            }
        };
        
        // Achievement system
        this.achievements = {
            first_victory: {
                name: "First Victory",
                description: "Complete your first dungeon run",
                points: 10,
                requirement: { type: "complete_runs", value: 1 }
            },
            speed_runner: {
                name: "Speed Runner",
                description: "Complete a run in under 10 minutes",
                points: 25,
                requirement: { type: "run_time", value: 600000 } // 10 minutes in ms
            },
            perfectionist: {
                name: "Perfectionist",
                description: "Complete a run without taking damage",
                points: 50,
                requirement: { type: "no_damage_run", value: 1 }
            },
            collector: {
                name: "Card Collector",
                description: "Collect 50 different temporary cards",
                points: 30,
                requirement: { type: "unique_cards", value: 50 }
            },
            artifact_hunter: {
                name: "Artifact Hunter",
                description: "Collect 20 different artifacts",
                points: 40,
                requirement: { type: "unique_artifacts", value: 20 }
            },
            floor_master: {
                name: "Floor Master",
                description: "Reach floor 50 in endless mode",
                points: 100,
                requirement: { type: "max_floor", value: 50 }
            },
            class_master: {
                name: "Class Master",
                description: "Complete runs with all classes",
                points: 75,
                requirement: { type: "all_classes", value: 3 }
            }
        };
        
        console.log("🏆 MetaProgression system initialized");
    }
    
    /**
     * Get player's meta progression data
     */
    getPlayerMetaData(player) {
        const playerData = this.playerDataManager.getPlayerData(player);
        
        if (!playerData.metaProgression) {
            playerData.metaProgression = {
                metaPoints: 0,
                unlockedFeatures: [],
                achievements: [],
                statistics: {
                    runsCompleted: 0,
                    runsAttempted: 0,
                    totalFloorsCleared: 0,
                    totalEnemiesDefeated: 0,
                    totalCardsCollected: 0,
                    totalArtifactsCollected: 0,
                    bestRunTime: null,
                    maxFloorReached: 0,
                    classRuns: {
                        warrior: 0,
                        mage: 0,
                        rogue: 0
                    },
                    difficultyCompletions: {
                        normal: 0,
                        hard: 0,
                        nightmare: 0,
                        endless: 0
                    }
                }
            };
        }
        
        return playerData.metaProgression;
    }
    
    /**
     * Award meta progression points
     */
    awardMetaPoints(player, points, reason = "") {
        const metaData = this.getPlayerMetaData(player);
        metaData.metaPoints += points;
        
        player.sendMessage(`§6+${points} Meta Points earned! ${reason}`);
        player.sendMessage(`§eTotal Meta Points: ${metaData.metaPoints}`);
        
        // Check for new unlocks
        this.checkForUnlocks(player);
        
        this.playerDataManager.savePlayerData(player);
    }
    
    /**
     * Record run completion and award meta progression
     */
    recordRunCompletion(player, runData, success) {
        const metaData = this.getPlayerMetaData(player);
        const stats = metaData.statistics;
        
        // Update basic statistics
        stats.runsAttempted++;
        if (success) {
            stats.runsCompleted++;
            stats.difficultyCompletions[runData.runType]++;
        }
        
        stats.totalFloorsCleared += runData.currentFloor - 1;
        stats.totalEnemiesDefeated += runData.enemiesDefeated || 0;
        stats.totalCardsCollected += runData.cardsCollected || 0;
        stats.totalArtifactsCollected += (runData.activeArtifacts || []).length;
        stats.maxFloorReached = Math.max(stats.maxFloorReached, runData.currentFloor);
        
        // Update class statistics
        const playerClass = this.getPlayerClass(player);
        if (playerClass && stats.classRuns[playerClass] !== undefined) {
            stats.classRuns[playerClass]++;
        }
        
        // Update best run time
        if (success) {
            const runTime = Date.now() - runData.startTime;
            if (!stats.bestRunTime || runTime < stats.bestRunTime) {
                stats.bestRunTime = runTime;
            }
        }
        
        // Award meta points based on performance
        let metaPoints = 0;
        
        // Base points for attempting
        metaPoints += 5;
        
        // Bonus for completion
        if (success) {
            metaPoints += 15;
            
            // Difficulty multiplier
            const difficultyMultiplier = {
                normal: 1,
                hard: 1.5,
                nightmare: 2,
                endless: 2.5
            };
            metaPoints = Math.floor(metaPoints * (difficultyMultiplier[runData.runType] || 1));
        }
        
        // Floor bonus
        metaPoints += Math.floor((runData.currentFloor - 1) * 2);
        
        // First time bonuses
        if (success && stats.difficultyCompletions[runData.runType] === 1) {
            metaPoints += 25; // First completion bonus
        }
        
        this.awardMetaPoints(player, metaPoints, `(Run ${success ? 'completed' : 'attempted'})`);
        
        // Check achievements
        this.checkAchievements(player, runData, success);
        
        this.playerDataManager.savePlayerData(player);
    }
    
    /**
     * Check for new unlocks
     */
    checkForUnlocks(player) {
        const metaData = this.getPlayerMetaData(player);
        const stats = metaData.statistics;
        
        for (const [unlockId, unlock] of Object.entries(this.unlocks)) {
            // Skip if already unlocked
            if (metaData.unlockedFeatures.includes(unlockId)) continue;
            
            // Check requirement
            if (this.checkRequirement(unlock.requirement, stats, metaData)) {
                metaData.unlockedFeatures.push(unlockId);
                
                player.sendMessage("§6🔓 NEW UNLOCK!");
                player.sendMessage(`§e${unlock.name}`);
                player.sendMessage(`§7${unlock.description}`);
            }
        }
    }
    
    /**
     * Check achievement progress
     */
    checkAchievements(player, runData, success) {
        const metaData = this.getPlayerMetaData(player);
        const stats = metaData.statistics;
        
        for (const [achievementId, achievement] of Object.entries(this.achievements)) {
            // Skip if already earned
            if (metaData.achievements.includes(achievementId)) continue;
            
            let earned = false;
            
            switch (achievement.requirement.type) {
                case "complete_runs":
                    earned = stats.runsCompleted >= achievement.requirement.value;
                    break;
                    
                case "run_time":
                    if (success && stats.bestRunTime) {
                        earned = stats.bestRunTime <= achievement.requirement.value;
                    }
                    break;
                    
                case "no_damage_run":
                    if (success && runData.runHealth === runData.maxRunHealth) {
                        earned = true;
                    }
                    break;
                    
                case "unique_cards":
                    earned = stats.totalCardsCollected >= achievement.requirement.value;
                    break;
                    
                case "unique_artifacts":
                    earned = stats.totalArtifactsCollected >= achievement.requirement.value;
                    break;
                    
                case "max_floor":
                    earned = stats.maxFloorReached >= achievement.requirement.value;
                    break;
                    
                case "all_classes":
                    const classesCompleted = Object.values(stats.classRuns).filter(count => count > 0).length;
                    earned = classesCompleted >= achievement.requirement.value;
                    break;
            }
            
            if (earned) {
                metaData.achievements.push(achievementId);
                this.awardMetaPoints(player, achievement.points, `(Achievement: ${achievement.name})`);
                
                player.sendMessage("§6🏆 ACHIEVEMENT UNLOCKED!");
                player.sendMessage(`§e${achievement.name}`);
                player.sendMessage(`§7${achievement.description}`);
            }
        }
    }
    
    /**
     * Check if a requirement is met
     */
    checkRequirement(requirement, stats, metaData) {
        switch (requirement.type) {
            case "meta_points":
                return metaData.metaPoints >= requirement.value;
                
            case "complete_runs":
                if (requirement.difficulty) {
                    return stats.difficultyCompletions[requirement.difficulty] >= requirement.value;
                }
                return stats.runsCompleted >= requirement.value;
                
            case "class_runs":
                return stats.classRuns[requirement.class] >= requirement.value;
                
            case "artifacts_collected":
                return stats.totalArtifactsCollected >= requirement.value;
                
            case "cards_collected":
                return stats.totalCardsCollected >= requirement.value;
                
            case "floors_cleared":
                return stats.totalFloorsCleared >= requirement.value;
                
            default:
                return false;
        }
    }
    
    /**
     * Get available unlocks for a player
     */
    getAvailableUnlocks(player) {
        const metaData = this.getPlayerMetaData(player);
        const stats = metaData.statistics;
        
        const available = [];
        const locked = [];
        
        for (const [unlockId, unlock] of Object.entries(this.unlocks)) {
            if (metaData.unlockedFeatures.includes(unlockId)) {
                available.push({ ...unlock, id: unlockId, status: "unlocked" });
            } else if (this.checkRequirement(unlock.requirement, stats, metaData)) {
                available.push({ ...unlock, id: unlockId, status: "available" });
            } else {
                locked.push({ ...unlock, id: unlockId, status: "locked" });
            }
        }
        
        return { available, locked };
    }
    
    /**
     * Get player achievements
     */
    getPlayerAchievements(player) {
        const metaData = this.getPlayerMetaData(player);
        
        const earned = [];
        const available = [];
        
        for (const [achievementId, achievement] of Object.entries(this.achievements)) {
            if (metaData.achievements.includes(achievementId)) {
                earned.push({ ...achievement, id: achievementId, status: "earned" });
            } else {
                available.push({ ...achievement, id: achievementId, status: "available" });
            }
        }
        
        return { earned, available };
    }
    
    /**
     * Get starter deck modifications based on unlocks
     */
    getStarterDeckModifications(player) {
        const metaData = this.getPlayerMetaData(player);
        const modifications = {
            extraCards: [],
            statBoosts: {},
            specialAbilities: []
        };
        
        // Check for starter deck upgrades
        if (metaData.unlockedFeatures.includes("improved_starter")) {
            modifications.extraCards.push("Improved Strike", "Improved Block");
        }
        
        if (metaData.unlockedFeatures.includes("advanced_starter")) {
            modifications.extraCards.push("Power Strike", "Shield Wall");
        }
        
        if (metaData.unlockedFeatures.includes("master_starter")) {
            modifications.extraCards.push("Master's Technique", "Perfect Defense");
        }
        
        // Check for permanent artifacts
        if (metaData.unlockedFeatures.includes("health_boost")) {
            modifications.statBoosts.maxHealth = 20;
        }
        
        if (metaData.unlockedFeatures.includes("energy_boost")) {
            modifications.statBoosts.maxEnergy = 10;
        }
        
        if (metaData.unlockedFeatures.includes("card_draw_boost")) {
            modifications.statBoosts.cardDraw = 1;
        }
        
        return modifications;
    }
    
    /**
     * Check if a feature is unlocked
     */
    isFeatureUnlocked(player, featureId) {
        const metaData = this.getPlayerMetaData(player);
        return metaData.unlockedFeatures.includes(featureId);
    }
    
    /**
     * Get player class (would integrate with class system)
     */
    getPlayerClass(player) {
        // This would integrate with the existing class system
        // For now, return a default
        return "warrior";
    }
    
    /**
     * Display meta progression status
     */
    displayMetaStatus(player) {
        const metaData = this.getPlayerMetaData(player);
        const stats = metaData.statistics;
        
        const status = [
            "§6=== META PROGRESSION ===",
            `§eMeta Points: ${metaData.metaPoints}`,
            `§eUnlocked Features: ${metaData.unlockedFeatures.length}`,
            `§eAchievements: ${metaData.achievements.length}`,
            "",
            "§6=== STATISTICS ===",
            `§eRuns Completed: ${stats.runsCompleted}/${stats.runsAttempted}`,
            `§eMax Floor Reached: ${stats.maxFloorReached}`,
            `§eTotal Floors Cleared: ${stats.totalFloorsCleared}`,
            `§eEnemies Defeated: ${stats.totalEnemiesDefeated}`,
            `§eCards Collected: ${stats.totalCardsCollected}`,
            `§eArtifacts Found: ${stats.totalArtifactsCollected}`,
            "",
            "§6=== DIFFICULTY COMPLETIONS ===",
            `§eNormal: ${stats.difficultyCompletions.normal}`,
            `§eHard: ${stats.difficultyCompletions.hard}`,
            `§eNightmare: ${stats.difficultyCompletions.nightmare}`,
            `§eEndless: ${stats.difficultyCompletions.endless}`
        ];
        
        return status.join("\n");
    }
}
