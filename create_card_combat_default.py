#!/usr/bin/env python3
"""
Create Card Combat Addon with Card Combat as Default Fighting System
"""

import zipfile
import os
import json

def create_mcaddon():
    """Create the .mcaddon file with card combat as default"""
    print("📦 Creating Card Combat Addon - DEFAULT FIGHTING SYSTEM...")
    
    # Create the .mcaddon file
    mcaddon_path = "card_combat_default_with_starter_deck.mcaddon"
    
    with zipfile.ZipFile(mcaddon_path, 'w', zipfile.ZIP_DEFLATED) as mcaddon:
        # Add behavior pack files
        bp_count = 0
        for root, dirs, files in os.walk("behavior_pack"):
            for file in files:
                file_path = os.path.join(root, file)
                archive_path = file_path.replace("\\", "/")
                mcaddon.write(file_path, archive_path)
                bp_count += 1
        
        # Add resource pack files
        rp_count = 0
        for root, dirs, files in os.walk("resource_pack"):
            for file in files:
                file_path = os.path.join(root, file)
                archive_path = file_path.replace("\\", "/")
                mcaddon.write(file_path, archive_path)
                rp_count += 1
    
    # Get file info
    file_size = os.path.getsize(mcaddon_path)
    file_size_mb = file_size / (1024 * 1024)
    total_files = bp_count + rp_count
    
    print(f"\n✅ CARD COMBAT WITH STARTER DECK SYSTEM CREATED!")
    print(f"📁 File: {mcaddon_path}")
    print(f"📊 Size: {file_size_mb:.2f} MB ({file_size:,} bytes)")
    print(f"📦 Files: {total_files} total ({bp_count} BP + {rp_count} RP)")
    print(f"🎮 Ready for installation!")

    print(f"\n🔥 CARD COMBAT IS NOW DEFAULT FIGHTING!")
    print(f"  ✅ ALL combat is intercepted and made card-based")
    print(f"  ✅ Players get FREE starter deck on spawn/join")
    print(f"  ✅ Mobs have their own card decks")
    print(f"  ✅ No more button mashing - pure strategy!")
    print(f"  ✅ Works with ALL combat-capable mobs")
    print(f"  ✅ Card Duelist entities are now visible")
    print(f"  ✅ Balanced starter deck for new players")
    
    return mcaddon_path

def create_installation_guide():
    """Create installation guide for the default fighting system"""
    guide_content = """
# CARD COMBAT - DEFAULT FIGHTING SYSTEM
## Installation & Usage Guide

### 🎮 WHAT'S NEW:
- **ALL COMBAT IS NOW CARD-BASED!**
- **FREE STARTER DECK for all new players!**
- No more traditional Minecraft combat
- Every attack triggers turn-based card combat
- Mobs have their own card decks and strategies

### 📦 INSTALLATION:
1. Double-click `card_combat_default_with_starter_deck.mcaddon`
2. Minecraft will import both behavior and resource packs
3. Create a new world or edit existing world
4. Enable BOTH packs in world settings:
   - Behavior Pack: "Card Combat System"
   - Resource Pack: "Card Combat System"
5. Start the world!

### ⚔ HOW IT WORKS:
- Attack ANY mob to start card combat
- Traditional damage is completely replaced
- Turn-based strategy using cards
- Mobs play cards from their own decks
- Energy system limits card plays per turn

### 🎯 COMBAT-READY MOBS:
- Zombies, Skeletons, Spiders
- Creepers, Endermen, Witches  
- Pillagers, Vindicators, Evokers
- Blazes, Ghasts, Piglins
- Hoglins, Wither Skeletons
- Card Duelists (special NPCs)

### 🎮 COMMANDS:
- `!combat info` - System information
- `!combat starter` - Get starter deck (new players)
- `!combat deck` - Open deck builder
- `!combat collection` - View cards
- `!combat help` - All commands

### 🔧 FEATURES:
- Strategic turn-based combat
- **FREE starter deck with 14 balanced cards**
- Mob-specific card decks
- Energy management system
- Camera system for combat view
- Audio effects and feedback
- Progression and achievements

### 🎲 MOB CARD EXAMPLES:
- **Zombies**: Basic strikes and power attacks
- **Skeletons**: Ranged attacks and critical strikes
- **Creepers**: Explosive devastating blows
- **Witches**: Healing potions and spells
- **Evokers**: Powerful magic spells

### 🎴 STARTER DECK INCLUDES:
- 4x Basic Strike (reliable attack)
- 3x Basic Block (essential defense)
- 2x Heal (recovery cards)
- 2x Power Attack (stronger damage)
- 1x Health Potion (emergency heal)
- 1x Critical Strike (special attack)
- 1x Shield Wall (strong defense)

### 💡 TIPS:
- Your starter deck is perfectly balanced for beginners
- Build better decks to win harder fights
- Manage your energy carefully
- Different mobs have different strategies
- Use defensive cards against aggressive mobs
- Collect cards by winning battles

### 🚨 IMPORTANT:
This completely replaces Minecraft's combat system.
All damage is now card-based and turn-based.
Perfect for players who want strategic combat!

Enjoy the new card-based fighting experience! ⚔️🎴
"""
    
    with open("CARD_COMBAT_DEFAULT_FIGHTING_GUIDE.txt", "w") as f:
        f.write(guide_content)
    
    print(f"📖 Installation guide created: CARD_COMBAT_DEFAULT_FIGHTING_GUIDE.txt")

def main():
    """Create the card combat default fighting system"""
    print("🎮 CARD COMBAT - DEFAULT FIGHTING SYSTEM CREATOR")
    print("=" * 50)
    
    mcaddon_path = create_mcaddon()
    create_installation_guide()
    
    print(f"\n🎯 READY TO TRANSFORM MINECRAFT COMBAT!")
    print(f"📁 Install: {mcaddon_path}")
    print(f"📖 Guide: CARD_COMBAT_DEFAULT_FIGHTING_GUIDE.txt")
    print(f"\n⚔️ ALL COMBAT IS NOW STRATEGIC AND CARD-BASED! ⚔️")

if __name__ == "__main__":
    main()
