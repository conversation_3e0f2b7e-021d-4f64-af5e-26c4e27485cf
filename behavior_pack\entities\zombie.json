{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "minecraft:zombie", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:zombie_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:movement": {"value": 0.35}, "minecraft:attack": {"damage": 2}}, "minecraft:zombie_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/zombie.json"}, "minecraft:attack": {"damage": 3}}, "minecraft:in_combat": {"minecraft:movement": {"value": 0.0}, "minecraft:behavior.float": {"priority": 0}}, "minecraft:combat_ai": {"minecraft:behavior.nearest_attackable_target": {"priority": 1, "must_see": false, "reselect_targets": false, "within_radius": 0.0, "entity_types": []}, "minecraft:behavior.melee_attack": {"priority": 2, "speed_multiplier": 0.0, "reach_multiplier": 0.0}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["zombie", "undead", "monster", "mob"]}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:movement": {"value": 0.23}, "minecraft:navigation.walk": {"is_amphibious": true, "avoid_sun": true, "avoid_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0, "breathes_air": true, "breathes_water": false}, "minecraft:nameable": {}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.hurt_by_target": {"priority": 1}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 25.0, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "snowgolem"}, {"test": "is_family", "subject": "other", "value": "irongolem"}]}, "max_dist": 35}, {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}, "max_dist": 35, "must_see": false}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "baby_turtle"}, {"test": "in_water", "subject": "other", "operator": "!=", "value": true}]}, "max_dist": 35}]}, "minecraft:behavior.melee_attack": {"priority": 3}, "minecraft:behavior.break_door": {"priority": 4}, "minecraft:behavior.move_towards_restriction": {"priority": 5, "speed_multiplier": 1.0}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 8.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:attack": {"damage": 3}, "minecraft:equipment": {"table": "loot_tables/entities/zombie_equipment.json"}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:burns_in_daylight": {}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.93, 0.0]}}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 95, "trigger": "minecraft:as_adult"}, {"weight": 5, "trigger": "minecraft:as_baby"}]}, "minecraft:as_adult": {"add": {"component_groups": ["minecraft:zombie_adult"]}}, "minecraft:as_baby": {"add": {"component_groups": ["minecraft:zombie_baby"]}}, "minecraft:start_combat": {"add": {"component_groups": ["minecraft:in_combat", "minecraft:combat_ai"]}, "remove": {"component_groups": []}}, "minecraft:end_combat": {"remove": {"component_groups": ["minecraft:in_combat", "minecraft:combat_ai"]}}, "minecraft:turn_start": {"run_command": {"command": ["tellraw @a {\"text\":\"Zombie's turn!\",\"color\":\"red\"}"]}}, "minecraft:turn_end": {"run_command": {"command": ["tellraw @a {\"text\":\"Zombie ends turn\",\"color\":\"gray\"}"]}}}}}