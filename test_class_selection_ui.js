/**
 * Test script for Class Selection UI System
 * This file can be used to test the class selection UI functionality
 */

import { world, system } from "@minecraft/server";

/**
 * Test the class selection UI system
 */
export function testClassSelectionUI() {
    console.log("🧪 Testing Class Selection UI System...");
    
    const testResults = {
        passed: 0,
        failed: 0,
        errors: []
    };
    
    try {
        // Test 1: Check if ClassSelectionUI is properly imported
        console.log("Test 1: Checking ClassSelectionUI import...");
        const { ClassSelectionUI } = await import("./behavior_pack/scripts/ui/ClassSelectionUI.js");
        if (ClassSelectionUI) {
            console.log("✅ ClassSelectionUI imported successfully");
            testResults.passed++;
        } else {
            throw new Error("ClassSelectionUI not found");
        }
        
        // Test 2: Check if UIManager has class selection integration
        console.log("Test 2: Checking UIManager integration...");
        const { UIManager } = await import("./behavior_pack/scripts/ui/UIManager.js");
        const uiManager = new UIManager();
        
        if (typeof uiManager.setClassSelectionUI === 'function' && 
            typeof uiManager.showClassSelectionForNewPlayer === 'function') {
            console.log("✅ UIManager class selection methods found");
            testResults.passed++;
        } else {
            throw new Error("UIManager missing class selection methods");
        }
        
        // Test 3: Check class information structure
        console.log("Test 3: Checking class information structure...");
        const { ClassManager } = await import("./behavior_pack/scripts/classes/ClassManager.js");
        const classManager = new ClassManager(null);
        const classSelectionUI = new ClassSelectionUI(classManager, null);
        
        const classInfo = classSelectionUI.classInfo;
        const expectedClasses = ["warrior", "mage", "rogue", "paladin", "necromancer"];
        
        let allClassesFound = true;
        for (const className of expectedClasses) {
            if (!classInfo[className]) {
                testResults.errors.push(`Missing class info: ${className}`);
                allClassesFound = false;
            } else {
                // Check required fields
                const requiredFields = ["name", "icon", "description", "playstyle", "strengths", "abilities"];
                for (const field of requiredFields) {
                    if (!classInfo[className][field]) {
                        testResults.errors.push(`Missing field ${field} for class ${className}`);
                        allClassesFound = false;
                    }
                }
            }
        }
        
        if (allClassesFound) {
            console.log("✅ All class information structure valid");
            testResults.passed++;
        } else {
            testResults.failed++;
        }
        
        // Test 4: Check ability information completeness
        console.log("Test 4: Checking ability information...");
        let abilitiesValid = true;
        
        for (const className of expectedClasses) {
            const abilities = classInfo[className].abilities;
            const requiredAbilities = ["passive", "ultimate", "advanced", "master"];
            
            for (const abilityType of requiredAbilities) {
                if (!abilities[abilityType] || abilities[abilityType].length < 10) {
                    testResults.errors.push(`Invalid ${abilityType} ability for ${className}`);
                    abilitiesValid = false;
                }
            }
        }
        
        if (abilitiesValid) {
            console.log("✅ All ability information valid");
            testResults.passed++;
        } else {
            testResults.failed++;
        }
        
        // Test 5: Check UI method availability
        console.log("Test 5: Checking UI methods...");
        const requiredMethods = [
            "showWelcomeScreen",
            "showClassSelection", 
            "showClassDetails",
            "confirmClassSelection",
            "needsClassSelection"
        ];
        
        let methodsValid = true;
        for (const method of requiredMethods) {
            if (typeof classSelectionUI[method] !== 'function') {
                testResults.errors.push(`Missing method: ${method}`);
                methodsValid = false;
            }
        }
        
        if (methodsValid) {
            console.log("✅ All UI methods available");
            testResults.passed++;
        } else {
            testResults.failed++;
        }
        
    } catch (error) {
        console.error("❌ Class Selection UI test failed:", error);
        testResults.errors.push(error.message);
        testResults.failed++;
    }
    
    // Print test results
    console.log("\n🧪 Class Selection UI Test Results:");
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    
    if (testResults.errors.length > 0) {
        console.log("\n🚨 Errors:");
        testResults.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (testResults.failed === 0) {
        console.log("\n🎉 All Class Selection UI tests passed! System is ready!");
    } else {
        console.log("\n⚠️ Some tests failed. Please check the errors above.");
    }
    
    return testResults;
}

/**
 * Test in-game class selection UI functionality
 */
export function testInGameClassSelectionUI() {
    console.log("🎮 Testing in-game class selection UI...");
    
    const players = world.getAllPlayers();
    if (players.length === 0) {
        console.log("⚠️ No players online for testing");
        return;
    }
    
    const testPlayer = players[0];
    console.log(`🧪 Testing with player: ${testPlayer.name}`);
    
    // Test commands
    testPlayer.sendMessage("§6🧪 Testing Class Selection UI System...");
    
    // Test manual class selection command
    system.runTimeout(() => {
        testPlayer.runCommand("combat selectclass");
    }, 20);
    
    // Test class commands
    system.runTimeout(() => {
        testPlayer.runCommand("class help");
    }, 60);
    
    testPlayer.sendMessage("§a✅ Class Selection UI test commands executed!");
    testPlayer.sendMessage("§7Check if the class selection UI appears correctly.");
}

/**
 * Generate test scenarios for class selection
 */
export function generateClassSelectionTestScenarios() {
    const scenarios = [
        {
            name: "New Player First Spawn",
            description: "Test class selection UI appears for new players on first spawn",
            steps: [
                "Create new player or reset existing player data",
                "Player joins server",
                "Verify class selection UI appears automatically",
                "Test class selection flow",
                "Verify class is set correctly"
            ]
        },
        {
            name: "Existing Player Without Class",
            description: "Test class selection for existing players who haven't chosen a class",
            steps: [
                "Have player with no class selected",
                "Player spawns in world",
                "Verify class selection prompt appears",
                "Test manual /combat selectclass command",
                "Complete class selection"
            ]
        },
        {
            name: "Class Information Display",
            description: "Test detailed class information display",
            steps: [
                "Open class selection UI",
                "Click on each class to view details",
                "Verify all class information is displayed correctly",
                "Check ability descriptions and progression info",
                "Test back navigation"
            ]
        },
        {
            name: "Class Selection Confirmation",
            description: "Test class selection confirmation flow",
            steps: [
                "Select a class from the UI",
                "Verify confirmation dialog appears",
                "Test cancellation (should return to selection)",
                "Test confirmation (should set class)",
                "Verify class is properly set in system"
            ]
        },
        {
            name: "UI Error Handling",
            description: "Test UI error handling and edge cases",
            steps: [
                "Test UI with invalid class data",
                "Test canceling at various stages",
                "Test rapid clicking/interaction",
                "Verify error messages are appropriate",
                "Test fallback to command-based selection"
            ]
        }
    ];
    
    console.log("📋 Class Selection UI Test Scenarios Generated:");
    scenarios.forEach((scenario, index) => {
        console.log(`\n${index + 1}. ${scenario.name}`);
        console.log(`   ${scenario.description}`);
        console.log("   Steps:");
        scenario.steps.forEach((step, stepIndex) => {
            console.log(`   ${stepIndex + 1}. ${step}`);
        });
    });
    
    return scenarios;
}

// Export test functions
export const ClassSelectionUITests = {
    testIntegration: testClassSelectionUI,
    testInGame: testInGameClassSelectionUI,
    generateScenarios: generateClassSelectionTestScenarios
};

// Auto-run integration test when imported
console.log("🔧 Class Selection UI test loaded");
console.log("🧪 Run ClassSelectionUITests.testIntegration() to test integration");
console.log("🎮 Run ClassSelectionUITests.testInGame() to test in-game functionality");
console.log("📋 Run ClassSelectionUITests.generateScenarios() to see test scenarios");
