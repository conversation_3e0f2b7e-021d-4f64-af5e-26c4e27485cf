/**
 * RunEvents.js
 * System for random events, shops, shrines, and special encounters during runs
 */

export class RunEvents {
    constructor(runRewards, metaProgression) {
        this.runRewards = runRewards;
        this.metaProgression = metaProgression;
        
        // Event types and their data
        this.eventTypes = {
            // Beneficial events
            shrine: {
                name: "Ancient Shrine",
                description: "A mystical shrine radiates with power",
                weight: 25,
                outcomes: [
                    {
                        name: "Pray for Strength",
                        description: "Gain +10 max health",
                        effect: { type: "health", value: 10 },
                        cost: null
                    },
                    {
                        name: "Pray for Energy",
                        description: "Gain +5 max energy",
                        effect: { type: "energy", value: 5 },
                        cost: null
                    },
                    {
                        name: "Pray for Wisdom",
                        description: "Upgrade a random card",
                        effect: { type: "upgrade_random" },
                        cost: null
                    }
                ]
            },
            
            treasure: {
                name: "Hidden Treasure",
                description: "You discover a hidden cache of valuables",
                weight: 20,
                outcomes: [
                    {
                        name: "Take the Gold",
                        description: "Gain 50 gold",
                        effect: { type: "gold", value: 50 },
                        cost: null
                    },
                    {
                        name: "Take the Artifact",
                        description: "Gain a random artifact",
                        effect: { type: "artifact" },
                        cost: null
                    },
                    {
                        name: "Take the Cards",
                        description: "Choose from 3 rare cards",
                        effect: { type: "card_choice", rarity: "rare", count: 3 },
                        cost: null
                    }
                ]
            },
            
            // Risk/reward events
            gamble: {
                name: "Mysterious Gambler",
                description: "A hooded figure offers you a game of chance",
                weight: 15,
                outcomes: [
                    {
                        name: "Bet 25 Gold",
                        description: "50% chance to double your bet, 50% chance to lose it",
                        effect: { type: "gamble", bet: 25, multiplier: 2 },
                        cost: { type: "gold", value: 25 }
                    },
                    {
                        name: "Bet 10 Health",
                        description: "50% chance to gain a legendary card, 50% chance to lose health",
                        effect: { type: "health_gamble", reward: "legendary_card" },
                        cost: { type: "health", value: 10 }
                    },
                    {
                        name: "Walk Away",
                        description: "Leave without risking anything",
                        effect: { type: "nothing" },
                        cost: null
                    }
                ]
            },
            
            cursed_altar: {
                name: "Cursed Altar",
                description: "Dark energy emanates from this ominous altar",
                weight: 10,
                outcomes: [
                    {
                        name: "Make a Sacrifice",
                        description: "Lose 15 health to gain a powerful artifact",
                        effect: { type: "artifact", rarity: "rare" },
                        cost: { type: "health", value: 15 }
                    },
                    {
                        name: "Destroy a Card",
                        description: "Remove a card from your deck to gain 2 card upgrades",
                        effect: { type: "upgrade_tokens", value: 2 },
                        cost: { type: "remove_card" }
                    },
                    {
                        name: "Ignore the Altar",
                        description: "Leave this cursed place alone",
                        effect: { type: "nothing" },
                        cost: null
                    }
                ]
            },
            
            // Shops
            merchant: {
                name: "Traveling Merchant",
                description: "A merchant has set up shop in this chamber",
                weight: 20,
                isShop: true,
                inventory: [
                    { type: "card", rarity: "common", price: 30 },
                    { type: "card", rarity: "uncommon", price: 60 },
                    { type: "card", rarity: "rare", price: 120 },
                    { type: "upgrade", description: "Card Upgrade", price: 40 },
                    { type: "health", description: "Health Potion (+20 HP)", value: 20, price: 25 },
                    { type: "energy", description: "Energy Elixir (+10 Energy)", value: 10, price: 35 }
                ]
            },
            
            artifact_dealer: {
                name: "Artifact Dealer",
                description: "A specialist in magical artifacts offers rare wares",
                weight: 8,
                isShop: true,
                unlockRequirement: "artifact_shop",
                inventory: [
                    { type: "artifact", rarity: "uncommon", price: 80 },
                    { type: "artifact", rarity: "rare", price: 150 },
                    { type: "artifact", rarity: "legendary", price: 300 }
                ]
            },
            
            // Story events
            lost_adventurer: {
                name: "Lost Adventurer",
                description: "You encounter another adventurer who seems lost",
                weight: 12,
                outcomes: [
                    {
                        name: "Help Them",
                        description: "Guide them to safety and gain their gratitude",
                        effect: { type: "blessing", description: "Next combat starts with +2 energy" },
                        cost: null
                    },
                    {
                        name: "Trade Information",
                        description: "Exchange knowledge about the dungeon",
                        effect: { type: "reveal_next_floor" },
                        cost: null
                    },
                    {
                        name: "Rob Them",
                        description: "Take their belongings by force",
                        effect: { type: "gold", value: 40 },
                        cost: { type: "curse", description: "Start next combat with -1 energy" }
                    }
                ]
            },
            
            ancient_library: {
                name: "Ancient Library",
                description: "Dusty tomes contain forgotten knowledge",
                weight: 10,
                outcomes: [
                    {
                        name: "Study Combat Techniques",
                        description: "Learn new attack strategies",
                        effect: { type: "card_choice", rarity: "uncommon", count: 2, filter: "attack" },
                        cost: null
                    },
                    {
                        name: "Study Defensive Arts",
                        description: "Learn protective magic",
                        effect: { type: "card_choice", rarity: "uncommon", count: 2, filter: "defense" },
                        cost: null
                    },
                    {
                        name: "Study Ancient Lore",
                        description: "Gain insight into the dungeon's secrets",
                        effect: { type: "meta_points", value: 10 },
                        cost: null
                    }
                ]
            }
        };
        
        console.log("🎭 RunEvents system initialized");
    }
    
    /**
     * Generate a random event for the current floor
     */
    generateRandomEvent(floor, difficulty, runData) {
        // Filter events based on unlocks and requirements
        const availableEvents = this.getAvailableEvents(runData.playerId);
        
        // Select event based on weights
        const selectedEvent = this.weightedRandomSelect(availableEvents);
        
        const event = {
            id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: selectedEvent.key,
            floor: floor,
            difficulty: difficulty,
            ...selectedEvent,
            completed: false
        };
        
        // Customize event based on floor and difficulty
        this.customizeEvent(event, floor, difficulty);
        
        return event;
    }
    
    /**
     * Get available events based on player unlocks
     */
    getAvailableEvents(playerId) {
        const availableEvents = {};
        
        for (const [eventKey, eventData] of Object.entries(this.eventTypes)) {
            // Check unlock requirements
            if (eventData.unlockRequirement) {
                // This would check with MetaProgression system
                // For now, assume all are unlocked
                if (!this.metaProgression.isFeatureUnlocked({ id: playerId }, eventData.unlockRequirement)) {
                    continue;
                }
            }
            
            availableEvents[eventKey] = eventData;
        }
        
        return availableEvents;
    }
    
    /**
     * Customize event based on floor and difficulty
     */
    customizeEvent(event, floor, difficulty) {
        // Scale rewards and costs based on floor
        const floorMultiplier = 1 + (floor - 1) * 0.1;
        
        if (event.outcomes) {
            event.outcomes.forEach(outcome => {
                if (outcome.effect) {
                    this.scaleEventEffect(outcome.effect, floorMultiplier);
                }
                if (outcome.cost) {
                    this.scaleEventCost(outcome.cost, floorMultiplier);
                }
            });
        }
        
        if (event.inventory) {
            event.inventory.forEach(item => {
                if (item.price) {
                    item.price = Math.floor(item.price * floorMultiplier);
                }
                if (item.value) {
                    item.value = Math.floor(item.value * floorMultiplier);
                }
            });
        }
    }
    
    /**
     * Scale event effects based on floor
     */
    scaleEventEffect(effect, multiplier) {
        if (effect.value && typeof effect.value === 'number') {
            effect.value = Math.floor(effect.value * multiplier);
        }
    }
    
    /**
     * Scale event costs based on floor
     */
    scaleEventCost(cost, multiplier) {
        if (cost.value && typeof cost.value === 'number') {
            cost.value = Math.floor(cost.value * multiplier);
        }
    }
    
    /**
     * Present an event to the player
     */
    presentEvent(player, event, runData) {
        player.sendMessage("§d✨ SPECIAL EVENT ✨");
        player.sendMessage(`§6${event.name}`);
        player.sendMessage(`§7${event.description}`);
        
        if (event.isShop) {
            this.presentShop(player, event, runData);
        } else {
            this.presentEventChoices(player, event, runData);
        }
        
        // Store current event in run data
        runData.currentEvent = event;
    }
    
    /**
     * Present event choices to the player
     */
    presentEventChoices(player, event, runData) {
        player.sendMessage("§eChoose your action:");
        
        event.outcomes.forEach((outcome, index) => {
            let choiceText = `§a${index + 1}. ${outcome.name}`;
            
            if (outcome.cost) {
                choiceText += ` §c(Cost: ${this.formatCost(outcome.cost)})`;
            }
            
            player.sendMessage(choiceText);
            player.sendMessage(`§7   ${outcome.description}`);
        });
        
        player.sendMessage("§7Use /combat event <number> to make your choice");
    }
    
    /**
     * Present shop interface to the player
     */
    presentShop(player, event, runData) {
        player.sendMessage("§a🏪 SHOP INVENTORY:");
        player.sendMessage(`§eYour Gold: ${runData.runGold || 0}`);
        
        event.inventory.forEach((item, index) => {
            let itemText = `§a${index + 1}. `;
            
            switch (item.type) {
                case "card":
                    itemText += `${item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)} Card`;
                    break;
                case "artifact":
                    itemText += `${item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)} Artifact`;
                    break;
                default:
                    itemText += item.description;
                    break;
            }
            
            itemText += ` §6- ${item.price} Gold`;
            
            // Check if player can afford
            if ((runData.runGold || 0) < item.price) {
                itemText += " §c(Can't afford)";
            }
            
            player.sendMessage(itemText);
        });
        
        player.sendMessage("§7Use /combat buy <number> to purchase an item");
        player.sendMessage("§7Use /combat event leave to exit the shop");
    }
    
    /**
     * Handle event choice selection
     */
    handleEventChoice(player, choiceIndex, runData) {
        const event = runData.currentEvent;
        if (!event || event.completed) {
            player.sendMessage("§cNo active event to interact with.");
            return false;
        }
        
        if (event.isShop) {
            player.sendMessage("§cUse /combat buy <number> to purchase items from shops.");
            return false;
        }
        
        if (choiceIndex < 1 || choiceIndex > event.outcomes.length) {
            player.sendMessage("§cInvalid choice number.");
            return false;
        }
        
        const outcome = event.outcomes[choiceIndex - 1];
        
        // Check if player can afford the cost
        if (outcome.cost && !this.canAffordCost(outcome.cost, runData)) {
            player.sendMessage("§cYou cannot afford this choice.");
            return false;
        }
        
        // Apply cost
        if (outcome.cost) {
            this.applyCost(outcome.cost, runData);
        }
        
        // Apply effect
        this.applyEventEffect(player, outcome.effect, runData);
        
        // Mark event as completed
        event.completed = true;
        runData.eventsCompleted = (runData.eventsCompleted || 0) + 1;
        
        player.sendMessage(`§a✅ You chose: ${outcome.name}`);
        
        return true;
    }
    
    /**
     * Handle shop purchases
     */
    handleShopPurchase(player, itemIndex, runData) {
        const event = runData.currentEvent;
        if (!event || !event.isShop || event.completed) {
            player.sendMessage("§cNo active shop to purchase from.");
            return false;
        }
        
        if (itemIndex < 1 || itemIndex > event.inventory.length) {
            player.sendMessage("§cInvalid item number.");
            return false;
        }
        
        const item = event.inventory[itemIndex - 1];
        const playerGold = runData.runGold || 0;
        
        if (playerGold < item.price) {
            player.sendMessage("§cYou don't have enough gold for this item.");
            return false;
        }
        
        // Deduct gold
        runData.runGold = playerGold - item.price;
        
        // Give item
        this.giveShopItem(player, item, runData);
        
        player.sendMessage(`§a✅ Purchased ${this.formatShopItem(item)} for ${item.price} gold`);
        player.sendMessage(`§eRemaining Gold: ${runData.runGold}`);
        
        return true;
    }
    
    /**
     * Check if player can afford a cost
     */
    canAffordCost(cost, runData) {
        switch (cost.type) {
            case "gold":
                return (runData.runGold || 0) >= cost.value;
            case "health":
                return runData.runHealth >= cost.value;
            case "remove_card":
                return runData.temporaryCards.length > 0;
            default:
                return true;
        }
    }
    
    /**
     * Apply cost to player
     */
    applyCost(cost, runData) {
        switch (cost.type) {
            case "gold":
                runData.runGold = (runData.runGold || 0) - cost.value;
                break;
            case "health":
                runData.runHealth = Math.max(1, runData.runHealth - cost.value);
                break;
            case "remove_card":
                if (runData.temporaryCards.length > 0) {
                    const randomIndex = Math.floor(Math.random() * runData.temporaryCards.length);
                    runData.temporaryCards.splice(randomIndex, 1);
                }
                break;
        }
    }
    
    /**
     * Apply event effect to player
     */
    applyEventEffect(player, effect, runData) {
        switch (effect.type) {
            case "health":
                runData.maxRunHealth += effect.value;
                runData.runHealth += effect.value;
                player.sendMessage(`§a+${effect.value} Max Health!`);
                break;
                
            case "energy":
                runData.maxRunEnergy += effect.value;
                runData.runEnergy = runData.maxRunEnergy;
                player.sendMessage(`§b+${effect.value} Max Energy!`);
                break;
                
            case "gold":
                runData.runGold = (runData.runGold || 0) + effect.value;
                player.sendMessage(`§e+${effect.value} Gold!`);
                break;
                
            case "artifact":
                const artifact = this.runRewards.generateRandomArtifact(effect.rarity || "common");
                runData.activeArtifacts.push(artifact);
                this.runRewards.applyArtifactEffect(artifact, runData);
                player.sendMessage(`§d+${artifact.name}!`);
                break;
                
            case "card_choice":
                this.presentCardChoice(player, effect, runData);
                break;
                
            case "upgrade_random":
                this.upgradeRandomCard(player, runData);
                break;
                
            case "gamble":
                this.handleGamble(player, effect, runData);
                break;
                
            case "meta_points":
                this.metaProgression.awardMetaPoints(player, effect.value, "(Event reward)");
                break;
                
            case "nothing":
                player.sendMessage("§7Nothing happens...");
                break;
        }
    }
    
    /**
     * Give shop item to player
     */
    giveShopItem(player, item, runData) {
        switch (item.type) {
            case "card":
                const card = this.runRewards.generateRandomCard(item.rarity, runData);
                runData.temporaryCards.push(card);
                break;
                
            case "artifact":
                const artifact = this.runRewards.generateRandomArtifact(item.rarity);
                runData.activeArtifacts.push(artifact);
                this.runRewards.applyArtifactEffect(artifact, runData);
                break;
                
            case "health":
                runData.runHealth = Math.min(runData.maxRunHealth, runData.runHealth + item.value);
                break;
                
            case "energy":
                runData.runEnergy = Math.min(runData.maxRunEnergy, runData.runEnergy + item.value);
                break;
                
            case "upgrade":
                // Add upgrade token
                runData.upgradeTokens = (runData.upgradeTokens || 0) + 1;
                break;
        }
    }
    
    /**
     * Format cost for display
     */
    formatCost(cost) {
        switch (cost.type) {
            case "gold":
                return `${cost.value} Gold`;
            case "health":
                return `${cost.value} Health`;
            case "remove_card":
                return "Remove a card";
            default:
                return "Unknown cost";
        }
    }
    
    /**
     * Format shop item for display
     */
    formatShopItem(item) {
        switch (item.type) {
            case "card":
                return `${item.rarity} Card`;
            case "artifact":
                return `${item.rarity} Artifact`;
            default:
                return item.description;
        }
    }
    
    /**
     * Weighted random selection helper
     */
    weightedRandomSelect(options) {
        const totalWeight = Object.values(options).reduce((sum, option) => sum + option.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const [key, option] of Object.entries(options)) {
            random -= option.weight;
            if (random <= 0) {
                return { ...option, key };
            }
        }
        
        // Fallback
        const firstKey = Object.keys(options)[0];
        return { ...options[firstKey], key: firstKey };
    }
    
    /**
     * Present card choice to player
     */
    presentCardChoice(player, effect, runData) {
        player.sendMessage("§6🃏 CARD CHOICE:");
        player.sendMessage("§7Choose one of the following cards:");
        
        for (let i = 0; i < effect.count; i++) {
            const card = this.runRewards.generateRandomCard(effect.rarity, runData);
            player.sendMessage(`§a${i + 1}. ${card.name} (${card.rarity})`);
            player.sendMessage(`§7   ${card.description}`);
        }
        
        player.sendMessage("§7Use /combat choose <number> to select a card");
    }
    
    /**
     * Upgrade a random card
     */
    upgradeRandomCard(player, runData) {
        if (runData.temporaryCards.length === 0) {
            player.sendMessage("§7No cards to upgrade.");
            return;
        }
        
        const randomCard = runData.temporaryCards[Math.floor(Math.random() * runData.temporaryCards.length)];
        
        // Apply random upgrade
        const upgradeTypes = ["damage", "cost", "effect"];
        const upgradeType = upgradeTypes[Math.floor(Math.random() * upgradeTypes.length)];
        
        this.runRewards.applyCardUpgrade(randomCard.id, upgradeType, runData);
        player.sendMessage(`§6✨ ${randomCard.name} has been upgraded!`);
    }
    
    /**
     * Handle gambling outcome
     */
    handleGamble(player, effect, runData) {
        const success = Math.random() < 0.5;
        
        if (success) {
            const winnings = effect.bet * effect.multiplier;
            runData.runGold = (runData.runGold || 0) + winnings;
            player.sendMessage(`§a🎰 You won! +${winnings} Gold!`);
        } else {
            player.sendMessage("§c🎰 You lost the gamble...");
        }
    }
}
