/**
 * UIManager - Handles UI display and interaction for the card combat system
 */

import { ActionFormData, ModalFormData, MessageFormData } from "@minecraft/server-ui";
import { world, system } from "@minecraft/server";

export class UIManager {
    constructor() {
        this.activeUIs = new Map();
    }
    
    /**
     * Show combat UI to player
     */
    async showCombatUI(player, combatSession) {
        const playerData = world.playerDataManager?.getPlayerData(player);
        if (!playerData) return;
        
        const form = new ActionFormData()
            .title("§6⚔ Combat Turn ⚔")
            .body(this.buildCombatInfo(player, combatSession, playerData));
        
        // Add card options
        for (let i = 0; i < playerData.currentHand.length; i++) {
            const cardId = playerData.currentHand[i];
            const card = world.cardManager?.getCard(cardId);
            if (card) {
                const canPlay = playerData.currentEnergy >= card.cost;
                const cardText = `${canPlay ? "§a" : "§c"}${card.name} (${card.cost})§r\n§7${card.description}`;
                form.button(cardText, canPlay ? undefined : "textures/ui/cancel");
            }
        }
        
        // Add end turn option
        form.button("§eEnd Turn", "textures/ui/check");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            this.handleCombatUIResponse(player, combatSession, response.selection, playerData);
        } catch (error) {
            console.error("Error showing combat UI:", error);
        }
    }
    
    /**
     * Handle combat UI response
     */
    handleCombatUIResponse(player, combatSession, selection, playerData) {
        if (selection === playerData.currentHand.length) {
            // End turn selected
            world.combatManager?.endTurn(combatSession);
            return;
        }
        
        // Card selected
        const cardId = playerData.currentHand[selection];
        const card = world.cardManager?.getCard(cardId);
        
        if (!card) {
            player.sendMessage("§cInvalid card selected!");
            return;
        }
        
        // Check if player can play the card
        if (playerData.currentEnergy < card.cost) {
            player.sendMessage("§cNot enough energy to play this card!");
            this.showCombatUI(player, combatSession); // Show UI again
            return;
        }
        
        // Play the card
        this.playCard(player, combatSession, cardId);
    }
    
    /**
     * Play a card
     */
    async playCard(player, combatSession, cardId) {
        const card = world.cardManager?.getCard(cardId);
        if (!card) return;
        
        // Remove card from hand and spend energy
        const success = world.playerDataManager?.playCard(player, cardId);
        if (!success) {
            player.sendMessage("§cFailed to play card!");
            return;
        }
        
        // Apply card effects
        await this.applyCardEffects(player, combatSession, card);
        
        // Show updated UI
        this.showCombatUI(player, combatSession);
    }
    
    /**
     * Apply card effects
     */
    async applyCardEffects(player, combatSession, card) {
        const playerData = world.playerDataManager?.getPlayerData(player);
        if (!playerData) return;
        
        // Get target if needed
        let target = null;
        if (card.type === "attack" || card.type === "spell") {
            target = await this.selectTarget(player, combatSession);
            if (!target) {
                player.sendMessage("§cNo target selected!");
                return;
            }
        }
        
        // Apply effects based on card type
        switch (card.type) {
            case "attack":
                if (target && card.damage) {
                    this.dealDamage(target, card.damage, player);
                    player.sendMessage(`§aDealt ${card.damage} damage to ${target.nameTag || target.typeId}!`);
                }
                break;
                
            case "defense":
                if (card.block) {
                    playerData.block += card.block;
                    player.sendMessage(`§9Gained ${card.block} block!`);
                }
                break;
                
            case "spell":
                await this.applySpellEffects(player, combatSession, card, target);
                break;
                
            case "item":
                await this.applyItemEffects(player, combatSession, card);
                break;
        }
        
        // Apply additional effects
        for (const effect of card.effects || []) {
            await this.applySpecialEffect(player, combatSession, effect);
        }
        
        // Update player data
        world.playerDataManager?.updatePlayerData(player, playerData);
    }
    
    /**
     * Select target for targeted cards
     */
    async selectTarget(player, combatSession) {
        const enemies = combatSession.participants.filter(p => p !== player && p.isValid());
        
        if (enemies.length === 0) {
            return null;
        }
        
        if (enemies.length === 1) {
            return enemies[0];
        }
        
        // Multiple targets - show selection UI
        const form = new ActionFormData()
            .title("§6Select Target")
            .body("Choose your target:");
        
        for (const enemy of enemies) {
            const health = enemy.getComponent("health");
            const healthText = health ? `(${health.currentValue}/${health.defaultValue} HP)` : "";
            form.button(`${enemy.nameTag || enemy.typeId} ${healthText}`);
        }
        
        try {
            const response = await form.show(player);
            if (response.canceled || response.selection === undefined) {
                return null;
            }
            return enemies[response.selection];
        } catch (error) {
            console.error("Error selecting target:", error);
            return null;
        }
    }
    
    /**
     * Deal damage to target
     */
    dealDamage(target, damage, source) {
        const health = target.getComponent("health");
        if (health) {
            health.setCurrentValue(Math.max(0, health.currentValue - damage));
        }
        
        // Visual effects
        target.dimension.spawnParticle("minecraft:critical_hit_emitter", target.location);
    }
    
    /**
     * Apply spell effects
     */
    async applySpellEffects(player, combatSession, card, target) {
        if (card.damage && target) {
            this.dealDamage(target, card.damage, player);
            player.sendMessage(`§aDealt ${card.damage} spell damage!`);
        }
        
        if (card.healing) {
            this.healPlayer(player, card.healing);
        }
    }
    
    /**
     * Apply item effects
     */
    async applyItemEffects(player, combatSession, card) {
        if (card.healing) {
            this.healPlayer(player, card.healing);
        }
        
        // Handle special item effects
        for (const effect of card.effects || []) {
            await this.applySpecialEffect(player, combatSession, effect);
        }
    }
    
    /**
     * Apply special effects
     */
    async applySpecialEffect(player, combatSession, effect) {
        const playerData = world.playerDataManager?.getPlayerData(player);
        if (!playerData) return;
        
        switch (effect) {
            case "draw_card":
                const drawnCard = world.playerDataManager?.drawCard(player);
                if (drawnCard) {
                    player.sendMessage("§aDrew a card!");
                }
                break;
                
            case "strength_buff":
                playerData.statusEffects.set("strength", 1);
                player.sendMessage("§cGained strength! Next attack deals +3 damage.");
                break;
                
            case "heal_and_block":
                this.healPlayer(player, 8);
                playerData.block += 5;
                player.sendMessage("§aHealed and gained block!");
                break;
                
            case "extra_turn":
                // This would need special handling in combat manager
                player.sendMessage("§6You get an extra turn!");
                break;
        }
    }
    
    /**
     * Heal player
     */
    healPlayer(player, amount) {
        const health = player.getComponent("health");
        if (health) {
            const newHealth = Math.min(health.defaultValue, health.currentValue + amount);
            health.setCurrentValue(newHealth);
            player.sendMessage(`§aHealed for ${amount} health!`);
        }
    }
    
    /**
     * Build combat info display
     */
    buildCombatInfo(player, combatSession, playerData) {
        const lines = [
            `§6Round: ${combatSession.round}`,
            `§eEnergy: ${playerData.currentEnergy}/${playerData.maxEnergy}`,
            `§9Block: ${playerData.block}`,
            ""
        ];
        
        // Show participants
        lines.push("§6Participants:");
        for (const participant of combatSession.participants) {
            if (!participant.isValid()) continue;
            
            const health = participant.getComponent("health");
            const healthText = health ? `${health.currentValue}/${health.defaultValue}` : "?/?";
            const isCurrentTurn = combatSession.turnOrder[combatSession.currentTurn] === participant;
            const turnIndicator = isCurrentTurn ? "§e► " : "  ";
            
            lines.push(`${turnIndicator}${participant.nameTag || participant.typeId}: ${healthText} HP`);
        }
        
        lines.push("");
        lines.push("§7Choose a card to play or end your turn:");
        
        return lines.join("\n");
    }
    
    /**
     * Show deck builder UI
     */
    async showDeckBuilder(player) {
        const playerData = world.playerDataManager?.getPlayerData(player);
        if (!playerData) return;
        
        const form = new ActionFormData()
            .title("§6Deck Builder")
            .body("Manage your card deck:")
            .button("§aView Current Deck", "textures/items/book_normal")
            .button("§eEdit Deck", "textures/items/book_writable")
            .button("§bView Collection", "textures/items/book_enchanted")
            .button("§cSave Deck", "textures/ui/save")
            .button("§9Load Deck", "textures/ui/upload");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            switch (response.selection) {
                case 0:
                    this.showCurrentDeck(player);
                    break;
                case 1:
                    this.showDeckEditor(player);
                    break;
                case 2:
                    this.showCardCollection(player);
                    break;
                case 3:
                    this.showSaveDeckForm(player);
                    break;
                case 4:
                    this.showLoadDeckForm(player);
                    break;
            }
        } catch (error) {
            console.error("Error showing deck builder:", error);
        }
    }
    
    /**
     * Show current deck
     */
    async showCurrentDeck(player) {
        const playerData = world.playerDataManager?.getPlayerData(player);
        if (!playerData) return;
        
        const deckText = playerData.activeDeck.map(cardId => {
            const card = world.cardManager?.getCard(cardId);
            return card ? world.cardManager.formatCard(cardId) : cardId;
        }).join("\n");
        
        const form = new MessageFormData()
            .title("§6Current Deck")
            .body(`§7Your active deck:\n\n${deckText}`)
            .button1("§aOK")
            .button2("§eEdit");
        
        try {
            const response = await form.show(player);
            if (response.selection === 1) {
                this.showDeckEditor(player);
            }
        } catch (error) {
            console.error("Error showing current deck:", error);
        }
    }
    
    /**
     * Show card collection
     */
    async showCardCollection(player) {
        const playerData = world.playerDataManager?.getPlayerData(player);
        if (!playerData) return;
        
        const collectionText = Array.from(playerData.collection.entries())
            .map(([cardId, count]) => {
                const card = world.cardManager?.getCard(cardId);
                const cardName = card ? world.cardManager.formatCard(cardId) : cardId;
                return `${cardName} x${count}`;
            })
            .join("\n");
        
        const form = new MessageFormData()
            .title("§6Card Collection")
            .body(`§7Your cards:\n\n${collectionText}`)
            .button1("§aOK")
            .button2("§eDeck Builder");
        
        try {
            const response = await form.show(player);
            if (response.selection === 1) {
                this.showDeckBuilder(player);
            }
        } catch (error) {
            console.error("Error showing card collection:", error);
        }
    }
    
    /**
     * Close combat UI
     */
    closeCombatUI(player) {
        // Remove any active UI references
        this.activeUIs.delete(player.id);
    }
    
    /**
     * Handle card play outside combat
     */
    handleCardPlay(player, itemStack) {
        // Handle card items during combat
        player.sendMessage("§eCard items can only be used during combat setup!");
    }
    
    /**
     * Handle card items outside combat
     */
    handleCardItemOutsideCombat(player, itemStack) {
        // Handle deck building items, etc.
        if (itemStack.typeId.includes("deck_builder")) {
            this.showDeckBuilder(player);
        }
    }
    
    // Placeholder methods for deck editing
    async showDeckEditor(player) {
        player.sendMessage("§eDeck editor coming soon!");
    }
    
    async showSaveDeckForm(player) {
        player.sendMessage("§eSave deck feature coming soon!");
    }
    
    async showLoadDeckForm(player) {
        player.sendMessage("§eLoad deck feature coming soon!");
    }
}
