# Combat Camera System Documentation

## Overview

The Combat Camera System provides an immersive top-down camera view during card combat encounters, giving players a strategic overview of the battlefield while maintaining full UI functionality.

## Features

### 🎥 Camera Management
- **Automatic Activation**: Camera switches to top-down view when combat begins
- **Smooth Transitions**: Gradual camera movement with easing animations
- **Dynamic Positioning**: Camera height adjusts based on combat area size
- **Focus Tracking**: Camera centers on current turn participant
- **Emergency Reset**: Instant camera restoration if issues occur

### 🎮 Player Experience
- **Movement Lock**: Player movement is restricted during combat camera mode
- **UI Adaptation**: Interface elements are repositioned for optimal viewing
- **Visual Feedback**: Clear indicators show camera mode is active
- **Manual Control**: Players can reset camera or view battlefield overview

### 🖥️ UI Integration
- **Dual UI Modes**: Separate interfaces for normal and camera views
- **Compact Design**: Camera UI uses space efficiently
- **Enhanced Information**: Additional battlefield information displayed
- **Responsive Layout**: UI adapts to different screen sizes

## Technical Implementation

### Core Components

#### CombatCameraManager
- **Location**: `behavior_pack/scripts/camera/CombatCameraManager.js`
- **Purpose**: Handles all camera positioning, transitions, and state management
- **Key Methods**:
  - `startCombatCamera(player, combatSession)` - Initiates camera mode
  - `endCombatCamera(player)` - Returns to normal view
  - `updateCameras()` - Updates camera positions every tick
  - `focusOnParticipant(player, target)` - Centers camera on specific participant

#### Camera Integration
- **CombatManager Integration**: Camera system is fully integrated with combat flow
- **UI Controller Enhancement**: Separate UI modes for camera and normal views
- **Main Loop Integration**: Camera updates run in main game loop

### Camera Positioning Algorithm

```javascript
// Calculate optimal camera position
const centerX = (minX + maxX) / 2;
const centerZ = (minZ + maxZ) / 2;
const height = baseHeight + (areaSize * scaleFactor);

// Position camera above combat center
const cameraPosition = {
    x: centerX,
    y: avgY + height,
    z: centerZ
};
```

### Transition System

The camera uses smooth transitions with easing functions:

1. **Capture Original State**: Store player's current position and rotation
2. **Calculate Target**: Determine optimal camera position for combat
3. **Interpolate**: Smoothly move between positions using cubic easing
4. **Apply Transform**: Update player position and rotation each tick

## Configuration

### Camera Settings
```javascript
const config = {
    combatHeight: 12,        // Base height above combat
    maxHeight: 20,           // Maximum camera height
    minHeight: 8,            // Minimum camera height
    pitch: -85,              // Camera angle (degrees)
    transitionDuration: 40,  // Transition time (ticks)
    participantPadding: 3,   // Area padding around participants
    lockMovement: true       // Lock player movement during combat
};
```

### UI Adjustments
- **Hand Panel**: Repositioned to bottom with compact layout
- **Status Panel**: Added participant overview on left side
- **Action Buttons**: Streamlined for camera view
- **Overlay**: Semi-transparent top overlay with turn information

## Usage

### Automatic Activation
The camera system activates automatically when combat begins:

```javascript
// In CombatManager.js
this.initializeCombatCameras(combatSession);
```

### Manual Control
Players can control the camera using commands:

- `!combat resetcamera` - Reset camera to normal view
- View Battlefield button - Show detailed participant overview
- Reset Camera button - Return to normal view (in UI)

### Admin Commands
Administrators have additional camera control:

- `!combat resetcamera` (admin) - Reset all player cameras
- Emergency reset functionality for troubleshooting

## UI Modes

### Standard Combat UI
- **When**: Normal camera view (first/third person)
- **Features**: Full card descriptions, detailed information
- **Layout**: Traditional vertical layout with large card display

### Camera Combat UI
- **When**: Top-down camera view active
- **Features**: Compact card display, battlefield overview
- **Layout**: Horizontal layout optimized for overhead view
- **Additional Elements**:
  - Participant status panel
  - Camera mode indicator
  - Battlefield overview button

## Troubleshooting

### Common Issues

#### Camera Not Activating
- **Cause**: Combat session not properly initialized
- **Solution**: Check combat manager initialization
- **Debug**: Use `!combat test` to verify system status

#### Camera Stuck in Combat Mode
- **Cause**: Combat ended abnormally
- **Solution**: Use `!combat resetcamera` command
- **Prevention**: Emergency cleanup runs automatically

#### UI Not Adapting
- **Cause**: Player tags not properly set
- **Solution**: Restart combat or reset camera
- **Debug**: Check for `combat_camera_active` tag

#### Performance Issues
- **Cause**: Too many camera updates
- **Solution**: Optimize update frequency
- **Monitoring**: Use `!combat performance` command

### Debug Commands

```javascript
// Check camera state
player.hasTag("combat_camera_active")

// Emergency reset
combatManager.emergencyResetAllCameras()

// Check active cameras
cameraManager.activeCameras.size
```

## Best Practices

### For Players
1. **Use Reset Command**: If camera feels stuck, use `!combat resetcamera`
2. **Check Battlefield**: Use "View Battlefield" for detailed information
3. **Report Issues**: Use admin commands to report camera problems

### For Administrators
1. **Monitor Performance**: Regularly check camera system performance
2. **Emergency Procedures**: Know how to reset all cameras quickly
3. **Testing**: Use test scenarios to verify camera functionality

### For Developers
1. **Error Handling**: Always wrap camera operations in try-catch blocks
2. **State Management**: Properly track camera states and transitions
3. **Cleanup**: Implement proper cleanup for disconnected players
4. **Performance**: Optimize camera updates for multiplayer environments

## Future Enhancements

### Planned Features
- **Multiple Camera Angles**: Different viewing perspectives
- **Zoom Control**: Player-controlled camera zoom
- **Cinematic Transitions**: Enhanced visual effects during transitions
- **Spectator Mode**: Allow non-participants to observe combat

### Technical Improvements
- **Better Interpolation**: More sophisticated camera movement
- **Collision Detection**: Prevent camera clipping through blocks
- **Dynamic FOV**: Adjust field of view based on combat area
- **Mobile Optimization**: Enhanced support for mobile devices

## API Reference

### CombatCameraManager Methods

#### `startCombatCamera(player, combatSession)`
Initiates combat camera mode for a player.
- **Parameters**: Player entity, combat session object
- **Returns**: Boolean success status
- **Side Effects**: Locks player movement, adjusts UI

#### `endCombatCamera(player)`
Returns player to normal camera view.
- **Parameters**: Player entity
- **Returns**: Boolean success status
- **Side Effects**: Unlocks movement, restores UI

#### `updateCameras()`
Updates all active camera positions.
- **Called**: Every tick from main loop
- **Performance**: Optimized for multiple players

#### `focusOnParticipant(player, target)`
Centers camera on specific participant.
- **Parameters**: Viewing player, target participant
- **Use Case**: Focus on current turn participant

### Integration Points

#### CombatManager Integration
```javascript
// Start cameras when combat begins
this.initializeCombatCameras(combatSession);

// Focus on current turn
this.focusCameraOnCurrentTurn(combatSession);

// End cameras when combat ends
this.endCombatCameras(combatSession);
```

#### UI Controller Integration
```javascript
// Check camera mode
const hasCombatCamera = player.hasTag("combat_camera_active");

// Show appropriate UI
if (hasCombatCamera) {
    this.showCombatCameraUI(player, combatSession, playerData);
} else {
    this.showStandardCombatUI(player, combatSession, playerData);
}
```

---

**Version**: 1.0.0  
**Last Updated**: 2024-12-19  
**Compatibility**: Minecraft Bedrock 1.21.70+
