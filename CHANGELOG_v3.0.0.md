# Card Combat System v3.0.0 - Roguelike Dungeons Update

## 🎯 Major New Features

### 🏰 Roguelike Dungeon System
- **4 Difficulty Levels**: Normal, Hard, Nightmare, and Endless runs
- **Procedural Generation**: Unique encounters, enemies, and events every run
- **Floor Progression**: Navigate through increasingly challenging floors
- **Run Management**: Start, abandon, and track dungeon progress

### 🎁 Temporary Progression System
- **Run-Specific Cards**: Collect powerful temporary cards during runs
- **Card Upgrades**: Enhance cards with damage, cost, and effect improvements
- **Artifact System**: Passive benefits that last for the entire run
- **Scaling Rewards**: Better loot on higher floors and difficulties

### 🏆 Meta-Progression System
- **Persistent Unlocks**: Permanent improvements between runs
- **Achievement System**: Comprehensive goals with point rewards
- **Meta Points**: Currency for unlocking new features and improvements
- **Statistics Tracking**: Detailed progress monitoring across all runs

### 🎭 Random Events System
- **8 Event Types**: Shrines, treasure, gambling, shops, and story encounters
- **Risk/Reward Mechanics**: Meaningful choices with consequences
- **Shop System**: Merchants and artifact dealers with dynamic pricing
- **Player Agency**: Multiple outcomes for every event encounter

### 🖥️ Comprehensive UI System
- **Dungeon Menu**: Professional interface for run selection and management
- **Progress Tracking**: Real-time status of health, energy, gold, and items
- **Event Interfaces**: Interactive choices for shops and special encounters
- **Meta Progression Display**: Achievement and unlock visualization

## 🔧 Technical Improvements

### 💻 Modular Architecture
- **6 New Systems**: RoguelikeManager, DungeonGenerator, RunRewards, MetaProgression, RunEvents, RoguelikeUI
- **Seamless Integration**: Works perfectly with existing combat and class systems
- **Scalable Design**: Easy to add new content and features
- **Performance Optimized**: Efficient data management and processing

### 🎮 Enhanced Commands
- **New Commands**: `/combat dungeon`, `/combat meta`, `/combat artifacts`
- **Event Interaction**: `/combat event`, `/combat buy`, `/combat choose`
- **Comprehensive Help**: Updated command documentation

## 📋 Complete Roguelike Features

### ✅ Dungeon System
- 4 run types with unique challenges and rewards
- Procedural enemy generation with 15+ enemy templates
- Environmental modifiers (dungeon, crypt, volcano, forest, void)
- Boss encounters with phase-based combat
- Infinite scaling for endless mode

### ✅ Progression Systems
- Temporary cards (common to legendary rarity)
- Card upgrade system with 4 enhancement types
- 20+ artifact types with passive effects
- Meta-progression with 5 unlock categories
- Achievement system with 15+ goals

### ✅ Events & Encounters
- Beneficial events (shrines, treasure, library)
- Risk/reward events (gambling, cursed altars)
- Shop system with dynamic inventory
- Story encounters with multiple outcomes
- Scaling difficulty and rewards

## 🚀 Installation & Usage

1. **Install**: Import the .mcaddon file in Minecraft Bedrock
2. **Create World**: Enable both behavior and resource packs
3. **Join Game**: Use `/combat dungeon` to start your first run
4. **Explore**: Navigate through procedurally generated floors
5. **Progress**: Unlock new features and improve your meta-progression!

## 🎮 New Commands

### Roguelike Commands
- `/combat dungeon` - Open dungeon menu and start runs
- `/combat dungeon start <type>` - Start specific run type
- `/combat dungeon status` - View current run progress
- `/combat dungeon abandon` - Abandon current run
- `/combat meta` - View meta-progression and unlocks
- `/combat artifacts` - View active artifacts

### Event Commands
- `/combat event <number>` - Make event choices
- `/combat buy <number>` - Purchase items from shops
- `/combat choose <number>` - Select cards from choices

## 🎯 Version Information

- **Version**: 3.0.0
- **Minecraft**: Bedrock Edition 1.21.90
- **Dependencies**: @minecraft/server 2.1.0-beta, @minecraft/server-ui 2.1.0-beta
- **New Features**: Complete roguelike dungeon system
- **Compatibility**: Fully backward compatible with existing saves

---

**Experience the ultimate Card Combat adventure with Roguelike Dungeons!** 🏰⚔️
