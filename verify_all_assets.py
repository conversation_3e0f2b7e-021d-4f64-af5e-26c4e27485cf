#!/usr/bin/env python3
"""
Verify All Assets for Minecraft Addon
Checks that all referenced assets exist and are properly integrated
"""

import json
from pathlib import Path

class AssetVerifier:
    def __init__(self):
        self.missing_assets = []
        self.found_assets = []
        
    def verify_sound_assets(self):
        """Verify all sound assets exist"""
        print("🎵 Verifying audio assets...")
        
        sound_def_path = Path("resource_pack/sounds/sound_definitions.json")
        if not sound_def_path.exists():
            print("❌ sound_definitions.json not found")
            return False
        
        with open(sound_def_path) as f:
            sound_defs = json.load(f)
        
        for sound_id, sound_data in sound_defs["sound_definitions"].items():
            for sound_entry in sound_data["sounds"]:
                sound_path = Path("resource_pack") / f"{sound_entry['name']}.wav"
                if sound_path.exists():
                    self.found_assets.append(str(sound_path))
                    print(f"✅ {sound_path}")
                else:
                    self.missing_assets.append(str(sound_path))
                    print(f"❌ {sound_path}")
        
        return len(self.missing_assets) == 0
    
    def verify_ui_textures(self):
        """Verify all UI textures exist"""
        print("\n🎨 Verifying UI textures...")
        
        ui_def_path = Path("resource_pack/textures/ui_texture_definitions.json")
        if not ui_def_path.exists():
            print("❌ ui_texture_definitions.json not found")
            return False
        
        with open(ui_def_path) as f:
            ui_defs = json.load(f)
        
        for texture_id, texture_data in ui_defs["texture_data"].items():
            texture_path = Path("resource_pack") / f"{texture_data['textures']}.png"
            if texture_path.exists():
                self.found_assets.append(str(texture_path))
                print(f"✅ {texture_path}")
            else:
                self.missing_assets.append(str(texture_path))
                print(f"❌ {texture_path}")
        
        return len(self.missing_assets) == 0
    
    def verify_item_textures(self):
        """Verify all item textures exist"""
        print("\n🎮 Verifying item textures...")
        
        item_def_path = Path("resource_pack/textures/item_texture.json")
        if not item_def_path.exists():
            print("❌ item_texture.json not found")
            return False
        
        with open(item_def_path) as f:
            item_defs = json.load(f)
        
        for item_id, texture_data in item_defs["texture_data"].items():
            texture_path = Path("resource_pack") / f"{texture_data['textures']}.png"
            if texture_path.exists():
                self.found_assets.append(str(texture_path))
                print(f"✅ {texture_path}")
            else:
                self.missing_assets.append(str(texture_path))
                print(f"❌ {texture_path}")
        
        return len(self.missing_assets) == 0
    
    def verify_pack_icons(self):
        """Verify pack icons exist"""
        print("\n📦 Verifying pack icons...")
        
        icons = [
            "behavior_pack/pack_icon.png",
            "resource_pack/pack_icon.png"
        ]
        
        for icon_path in icons:
            path = Path(icon_path)
            if path.exists():
                self.found_assets.append(str(path))
                print(f"✅ {path}")
            else:
                self.missing_assets.append(str(path))
                print(f"❌ {path}")
        
        return len(self.missing_assets) == 0
    
    def verify_addon_structure(self):
        """Verify basic addon structure"""
        print("\n📁 Verifying addon structure...")
        
        required_files = [
            "behavior_pack/manifest.json",
            "resource_pack/manifest.json",
            "resource_pack/sounds/sound_definitions.json",
            "resource_pack/textures/ui_texture_definitions.json",
            "resource_pack/textures/item_texture.json"
        ]
        
        all_good = True
        for file_path in required_files:
            path = Path(file_path)
            if path.exists():
                print(f"✅ {path}")
            else:
                print(f"❌ {path}")
                all_good = False
        
        return all_good
    
    def count_generated_assets(self):
        """Count all generated assets"""
        print("\n📊 Counting generated assets...")
        
        # Count audio files
        audio_dir = Path("resource_pack/sounds/combat")
        audio_files = list(audio_dir.glob("*.wav")) if audio_dir.exists() else []
        
        # Count texture files
        texture_dirs = [
            Path("resource_pack/textures/ui/card_combat"),
            Path("resource_pack/textures/items"),
            Path("resource_pack/textures/items/cards")
        ]
        
        texture_files = []
        for texture_dir in texture_dirs:
            if texture_dir.exists():
                texture_files.extend(list(texture_dir.glob("*.png")))
        
        # Count pack icons
        pack_icons = [
            Path("behavior_pack/pack_icon.png"),
            Path("resource_pack/pack_icon.png")
        ]
        existing_icons = [icon for icon in pack_icons if icon.exists()]
        
        print(f"🎵 Audio files: {len(audio_files)}")
        print(f"🎨 Texture files: {len(texture_files)}")
        print(f"📦 Pack icons: {len(existing_icons)}")
        print(f"📊 Total assets: {len(audio_files) + len(texture_files) + len(existing_icons)}")
        
        return {
            "audio": len(audio_files),
            "textures": len(texture_files),
            "icons": len(existing_icons),
            "total": len(audio_files) + len(texture_files) + len(existing_icons)
        }
    
    def run_full_verification(self):
        """Run complete asset verification"""
        print("🔍 COMPLETE ASSET VERIFICATION")
        print("=" * 50)
        
        # Verify structure
        structure_ok = self.verify_addon_structure()
        
        # Verify assets
        sounds_ok = self.verify_sound_assets()
        ui_ok = self.verify_ui_textures()
        items_ok = self.verify_item_textures()
        icons_ok = self.verify_pack_icons()
        
        # Count assets
        asset_counts = self.count_generated_assets()
        
        # Final summary
        print("\n" + "=" * 50)
        print("🎯 VERIFICATION SUMMARY")
        print("=" * 50)
        
        all_verified = structure_ok and sounds_ok and ui_ok and items_ok and icons_ok
        
        if all_verified:
            print("✅ ALL ASSETS VERIFIED SUCCESSFULLY!")
            print(f"✅ Found {len(self.found_assets)} assets")
            print(f"✅ Missing {len(self.missing_assets)} assets")
        else:
            print("❌ SOME ASSETS ARE MISSING!")
            print(f"✅ Found {len(self.found_assets)} assets")
            print(f"❌ Missing {len(self.missing_assets)} assets")
        
        print(f"\n📊 Asset Breakdown:")
        print(f"  🎵 Audio: {asset_counts['audio']} files")
        print(f"  🎨 Textures: {asset_counts['textures']} files")
        print(f"  📦 Icons: {asset_counts['icons']} files")
        print(f"  📊 Total: {asset_counts['total']} files")
        
        if all_verified:
            print(f"\n🎉 YOUR ADDON IS COMPLETE AND READY!")
            print(f"🚀 All assets are properly integrated")
            print(f"🎮 Ready for Minecraft Bedrock Edition testing")
        else:
            print(f"\n⚠️  Some assets need attention:")
            for missing in self.missing_assets:
                print(f"   - {missing}")
        
        return all_verified

def main():
    """Main verification function"""
    verifier = AssetVerifier()
    success = verifier.run_full_verification()
    
    if success:
        print("\n🏆 VERIFICATION COMPLETE - ALL SYSTEMS GO!")
    else:
        print("\n🔧 VERIFICATION COMPLETE - SOME ISSUES FOUND")

if __name__ == "__main__":
    main()
