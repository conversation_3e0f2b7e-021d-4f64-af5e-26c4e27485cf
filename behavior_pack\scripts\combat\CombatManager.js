/**
 * CombatManager - Handles turn-based combat logic
 */

import { world, system } from "@minecraft/server";
import { CombatCameraManager } from "../camera/CombatCameraManager.js";

export class CombatManager {
    constructor(cardManager, uiManager, playerDataManager, audioManager = null, classManager = null) {
        this.cardManager = cardManager;
        this.uiManager = uiManager;
        this.playerDataManager = playerDataManager;
        this.audioManager = audioManager;
        this.classManager = classManager;
        this.cameraManager = new CombatCameraManager();
        
        // Active combat sessions
        this.activeCombats = new Map();
        
        // Debug mode
        this.debugMode = new Set();
        
        // Combat detection settings
        this.combatRange = 8; // Range to detect combat
        this.combatCooldown = new Map(); // Prevent spam combat initiation
    }
    
    /**
     * Check if damage should be intercepted for card combat
     */
    shouldInterceptDamage(event) {
        const { hurtEntity, damageSource } = event;

        // Always intercept damage between combat-capable entities
        if (!damageSource.damagingEntity) {
            return false;
        }

        const target = hurtEntity;
        const attacker = damageSource.damagingEntity;

        // Intercept if either is a player or combat-capable mob
        const targetIsPlayer = target.typeId === "minecraft:player";
        const attackerIsPlayer = attacker.typeId === "minecraft:player";
        const targetIsMob = this.isCombatCapableMob(target);
        const attackerIsMob = this.isCombatCapableMob(attacker);

        return targetIsPlayer || attackerIsPlayer || (targetIsMob && attackerIsMob);
    }

    /**
     * Handle entity hurt events
     */
    handleEntityHurt(event) {
        const { hurtEntity, damageSource } = event;

        // Check if this should trigger turn-based combat
        if (this.shouldStartCombat(hurtEntity, damageSource)) {
            // Start turn-based combat
            this.initiateCombat(hurtEntity, damageSource.damagingEntity);
        }
    }
    
    /**
     * Handle entity hit events
     */
    handleEntityHit(event) {
        const { damagingEntity, hitEntity } = event;
        
        // Check if this should trigger turn-based combat
        if (this.shouldStartCombat(hitEntity, { damagingEntity })) {
            // Start turn-based combat
            this.initiateCombat(hitEntity, damagingEntity);
        }
    }
    
    /**
     * Handle item use events
     */
    handleItemUse(event) {
        const { source, itemStack } = event;
        
        // Check if it's a card-related item
        if (itemStack.typeId.includes("card_combat")) {
            this.handleCardItem(source, itemStack);
        }
    }
    
    /**
     * Determine if combat should start - ALWAYS START CARD COMBAT
     */
    shouldStartCombat(target, damageSource) {
        // Don't start combat if already in combat
        if (this.isInCombat(target)) {
            return false;
        }

        // Check cooldown
        const targetId = target.id;
        if (this.combatCooldown.has(targetId)) {
            const cooldownTime = this.combatCooldown.get(targetId);
            if (Date.now() - cooldownTime < 1000) { // Reduced to 1 second cooldown
                return false;
            }
        }

        // Must have a damaging entity
        if (!damageSource.damagingEntity) {
            return false;
        }

        const attacker = damageSource.damagingEntity;

        // CARD COMBAT IS NOW DEFAULT - Start combat for ANY entity interaction
        // At least one participant must be a player OR both must be mobs that can fight
        const targetIsPlayer = target.typeId === "minecraft:player";
        const attackerIsPlayer = attacker.typeId === "minecraft:player";
        const targetIsMob = this.isCombatCapableMob(target);
        const attackerIsMob = this.isCombatCapableMob(attacker);

        // Allow combat between: Player vs Any, Any vs Player, or Mob vs Mob
        if (!targetIsPlayer && !attackerIsPlayer && !targetIsMob && !attackerIsMob) {
            return false;
        }

        // Both entities must be alive
        if (!target.isValid() || !attacker.isValid()) {
            return false;
        }

        // Exclude certain entity types from card combat
        const excludedTypes = [
            "minecraft:item",
            "minecraft:xp_orb",
            "minecraft:arrow",
            "minecraft:fireball"
        ];

        if (excludedTypes.includes(target.typeId) || excludedTypes.includes(attacker.typeId)) {
            return false;
        }

        return true;
    }

    /**
     * Check if an entity is capable of card combat
     */
    isCombatCapableMob(entity) {
        const combatCapableMobs = [
            "minecraft:zombie",
            "minecraft:skeleton",
            "minecraft:spider",
            "minecraft:creeper",
            "minecraft:enderman",
            "minecraft:witch",
            "minecraft:pillager",
            "minecraft:vindicator",
            "minecraft:evoker",
            "minecraft:blaze",
            "minecraft:ghast",
            "minecraft:piglin",
            "minecraft:hoglin",
            "minecraft:wither_skeleton",
            "card_combat:card_duelist"
        ];

        return combatCapableMobs.includes(entity.typeId);
    }
    
    /**
     * Initiate turn-based combat
     */
    initiateCombat(entity1, entity2) {
        const combatId = this.generateCombatId();
        
        // Set cooldown
        this.combatCooldown.set(entity1.id, Date.now());
        this.combatCooldown.set(entity2.id, Date.now());
        
        // Create combat session
        const combatSession = {
            id: combatId,
            participants: [entity1, entity2],
            currentTurn: 0,
            turnOrder: this.determineTurnOrder([entity1, entity2]),
            round: 1,
            state: "starting",
            startTime: Date.now(),
            location: this.calculateCombatCenter([entity1, entity2])
        };
        
        this.activeCombats.set(combatId, combatSession);
        
        // Notify participants
        this.notifyParticipants(combatSession, "Combat initiated!");
        
        // Start combat sequence
        this.startCombatSequence(combatSession);
        
        if (this.debugMode.size > 0) {
            console.log(`Combat started: ${combatId}`, combatSession);
        }
    }
    
    /**
     * Start the combat sequence
     */
    startCombatSequence(combatSession) {
        // Freeze participants
        this.freezeParticipants(combatSession);

        // Initialize hands for all participants
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player") {
                this.playerDataManager.drawInitialHand(participant);
            } else {
                // Give AI entities basic card decks
                this.initializeAIEntityDeck(participant);
            }
        }

        // Set state to active
        combatSession.state = "active";

        // Initialize combat cameras for all player participants
        this.initializeCombatCameras(combatSession);

        // Play combat start sound
        if (this.audioManager) {
            this.audioManager.playSoundForCombat('combat_start', combatSession);
        }

        // Announce card combat mode
        this.notifyParticipants(combatSession, "§6⚔ CARD COMBAT INITIATED! ⚔");
        this.notifyParticipants(combatSession, "§eAll damage is now card-based!");

        // Start first turn
        this.startTurn(combatSession);
    }

    /**
     * Initialize AI entity with basic cards
     */
    initializeAIEntityDeck(entity) {
        // Create basic AI deck based on entity type
        const mobDecks = {
            "minecraft:zombie": ["basic_strike", "basic_strike", "basic_block", "power_attack"],
            "minecraft:skeleton": ["basic_strike", "basic_block", "critical_strike", "basic_strike"],
            "minecraft:spider": ["basic_strike", "basic_strike", "critical_strike", "basic_block"],
            "minecraft:creeper": ["devastating_blow", "basic_strike", "basic_block", "power_attack"],
            "minecraft:enderman": ["critical_strike", "basic_block", "power_attack", "basic_strike"],
            "minecraft:witch": ["heal", "basic_strike", "basic_block", "health_potion"],
            "minecraft:pillager": ["basic_strike", "critical_strike", "basic_block", "power_attack"],
            "minecraft:vindicator": ["power_attack", "basic_strike", "basic_block", "devastating_blow"],
            "minecraft:evoker": ["fireball", "heal", "basic_block", "lightning_bolt"],
            "minecraft:blaze": ["fireball", "fireball", "basic_strike", "basic_block"],
            "minecraft:ghast": ["fireball", "basic_block", "basic_strike", "critical_strike"],
            "minecraft:piglin": ["basic_strike", "basic_block", "power_attack", "critical_strike"],
            "minecraft:hoglin": ["devastating_blow", "power_attack", "basic_strike", "basic_block"],
            "minecraft:wither_skeleton": ["devastating_blow", "critical_strike", "basic_block", "power_attack"],
            "card_combat:card_duelist": ["basic_strike", "critical_strike", "heal", "fireball", "basic_block", "power_attack"]
        };

        const deck = mobDecks[entity.typeId] || ["basic_strike", "basic_strike", "basic_block", "basic_block"];

        // Store the deck on the entity (simplified approach)
        entity.addTag(`ai_deck:${deck.join(",")}`);
        entity.addTag("ai_energy:3"); // Start with 3 energy
        entity.addTag("ai_hand_size:3"); // Draw 3 cards
    }
    
    /**
     * Determine turn order based on speed/initiative
     */
    determineTurnOrder(participants) {
        // For now, players go first, then by entity type
        return participants.sort((a, b) => {
            if (a.typeId === "minecraft:player" && b.typeId !== "minecraft:player") {
                return -1;
            }
            if (b.typeId === "minecraft:player" && a.typeId !== "minecraft:player") {
                return 1;
            }
            return 0;
        });
    }
    
    /**
     * Start a turn for the current participant
     */
    startTurn(combatSession) {
        const currentParticipant = combatSession.turnOrder[combatSession.currentTurn];
        
        if (!currentParticipant.isValid()) {
            this.endCombat(combatSession, "participant_invalid");
            return;
        }
        
        // Focus camera on current turn participant
        this.focusCameraOnCurrentTurn(combatSession);

        // Play turn start sound
        if (this.audioManager && currentParticipant.typeId === "minecraft:player") {
            this.audioManager.playSound('turn_start', currentParticipant);
        }

        // Show UI for players
        if (currentParticipant.typeId === "minecraft:player") {
            this.uiManager.showCombatUI(currentParticipant, combatSession);
        } else {
            // AI turn for mobs
            this.handleAITurn(combatSession, currentParticipant);
        }
        
        // Set turn timer
        this.setTurnTimer(combatSession);
    }
    
    /**
     * Handle AI turn for mobs - now uses CARD COMBAT
     */
    handleAITurn(combatSession, aiEntity) {
        // Get AI's available cards
        const aiCards = this.getAICards(aiEntity);
        const aiEnergy = this.getAIEnergy(aiEntity);

        // Find playable cards
        const playableCards = aiCards.filter(cardId => {
            const card = this.cardManager.getCard(cardId);
            return card && card.cost <= aiEnergy;
        });

        if (playableCards.length > 0) {
            // AI plays a random playable card
            const selectedCard = playableCards[Math.floor(Math.random() * playableCards.length)];
            const card = this.cardManager.getCard(selectedCard);

            // Find target
            const targets = combatSession.participants.filter(p => p !== aiEntity && p.isValid());
            const target = targets.length > 0 ? targets[0] : null;

            // Play the card
            this.playAICard(combatSession, aiEntity, card, target);

            // Reduce AI energy
            this.setAIEnergy(aiEntity, aiEnergy - card.cost);

            // Notify participants
            this.notifyParticipants(combatSession, `§c${this.getEntityDisplayName(aiEntity)} §eplays §6${card.name}§e!`);
        } else {
            // No playable cards - end turn and gain energy
            this.setAIEnergy(aiEntity, Math.min(aiEnergy + 1, 5)); // Max 5 energy
            this.notifyParticipants(combatSession, `§c${this.getEntityDisplayName(aiEntity)} §egains energy and ends turn.`);
        }

        // End turn after a short delay
        system.runTimeout(() => {
            this.endTurn(combatSession);
        }, 60); // 3 seconds to read the action
    }

    /**
     * Get AI entity's cards
     */
    getAICards(entity) {
        const deckTag = entity.getTags().find(tag => tag.startsWith("ai_deck:"));
        if (deckTag) {
            const deckData = deckTag.replace("ai_deck:", "");
            return deckData.split(",");
        }
        return ["basic_strike", "basic_block"]; // Fallback
    }

    /**
     * Get AI entity's energy
     */
    getAIEnergy(entity) {
        const energyTag = entity.getTags().find(tag => tag.startsWith("ai_energy:"));
        if (energyTag) {
            return parseInt(energyTag.replace("ai_energy:", "")) || 3;
        }
        return 3; // Default energy
    }

    /**
     * Set AI entity's energy
     */
    setAIEnergy(entity, energy) {
        // Remove old energy tag
        const oldEnergyTag = entity.getTags().find(tag => tag.startsWith("ai_energy:"));
        if (oldEnergyTag) {
            entity.removeTag(oldEnergyTag);
        }
        // Add new energy tag
        entity.addTag(`ai_energy:${energy}`);
    }

    /**
     * Play AI card with effects
     */
    playAICard(combatSession, aiEntity, card, target) {
        // Apply card effects based on type
        switch (card.type) {
            case "attack":
                if (target) {
                    this.applyDamage(target, card.damage || 2, aiEntity);
                    if (this.audioManager && target.typeId === "minecraft:player") {
                        this.audioManager.playAttackHit(target, card.damage || 2);
                    }
                }
                break;
            case "defense":
                // AI gains block/shield
                this.notifyParticipants(combatSession, `§c${this.getEntityDisplayName(aiEntity)} §egains defense!`);
                break;
            case "spell":
                if (target && card.damage) {
                    this.applyDamage(target, card.damage, aiEntity);
                    if (this.audioManager) {
                        this.audioManager.playSound('spell_cast1', target);
                    }
                }
                break;
            case "item":
                if (card.heal) {
                    this.healEntity(aiEntity, card.heal);
                    if (this.audioManager) {
                        this.audioManager.playSound('heal', aiEntity);
                    }
                }
                break;
        }
    }

    /**
     * Get display name for entity
     */
    getEntityDisplayName(entity) {
        if (entity.typeId === "minecraft:player") {
            return entity.name;
        }

        const displayNames = {
            "minecraft:zombie": "Zombie",
            "minecraft:skeleton": "Skeleton",
            "minecraft:spider": "Spider",
            "minecraft:creeper": "Creeper",
            "minecraft:enderman": "Enderman",
            "minecraft:witch": "Witch",
            "minecraft:pillager": "Pillager",
            "minecraft:vindicator": "Vindicator",
            "minecraft:evoker": "Evoker",
            "minecraft:blaze": "Blaze",
            "minecraft:ghast": "Ghast",
            "minecraft:piglin": "Piglin",
            "minecraft:hoglin": "Hoglin",
            "minecraft:wither_skeleton": "Wither Skeleton",
            "card_combat:card_duelist": "Card Duelist"
        };

        return displayNames[entity.typeId] || entity.typeId.replace("minecraft:", "");
    }
    
    /**
     * Calculate AI damage
     */
    calculateAIDamage(aiEntity) {
        // Base damage based on entity type
        const baseDamage = {
            "minecraft:zombie": 3,
            "minecraft:skeleton": 2,
            "minecraft:spider": 2,
            "minecraft:creeper": 4
        };
        
        return baseDamage[aiEntity.typeId] || 2;
    }
    
    /**
     * End current turn and move to next
     */
    endTurn(combatSession) {
        // Move to next participant
        combatSession.currentTurn = (combatSession.currentTurn + 1) % combatSession.turnOrder.length;
        
        // If we've completed a full round
        if (combatSession.currentTurn === 0) {
            combatSession.round++;
        }
        
        // Check for combat end conditions
        if (this.checkCombatEndConditions(combatSession)) {
            return;
        }
        
        // Start next turn
        this.startTurn(combatSession);
    }
    
    /**
     * Check if combat should end
     */
    checkCombatEndConditions(combatSession) {
        const aliveParticipants = combatSession.participants.filter(p => p.isValid() && p.getComponent("health").currentValue > 0);
        
        if (aliveParticipants.length <= 1) {
            this.endCombat(combatSession, "victory");
            return true;
        }
        
        // Max rounds limit
        if (combatSession.round > 20) {
            this.endCombat(combatSession, "timeout");
            return true;
        }
        
        return false;
    }
    
    /**
     * End combat session
     */
    endCombat(combatSession, reason) {
        // Unfreeze participants
        this.unfreezeParticipants(combatSession);
        
        // End combat cameras for all player participants
        this.endCombatCameras(combatSession);

        // Play combat end sound
        if (this.audioManager) {
            this.audioManager.playSoundForCombat('combat_end', combatSession);
        }

        // Close UIs
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                this.uiManager.closeCombatUI(participant);
            }
        }
        
        // Award experience for victory
        if (reason === "victory" && this.classManager) {
            this.awardCombatExperience(combatSession);
        }

        // Notify participants
        this.notifyParticipants(combatSession, `Combat ended: ${reason}`);

        // Remove from active combats
        this.activeCombats.delete(combatSession.id);
        
        if (this.debugMode.size > 0) {
            console.log(`Combat ended: ${combatSession.id}, reason: ${reason}`);
        }
    }

    /**
     * Award experience to players for combat victory
     */
    awardCombatExperience(combatSession) {
        const aliveParticipants = combatSession.participants.filter(p =>
            p.isValid() &&
            p.typeId === "minecraft:player" &&
            p.getComponent("health").currentValue > 0
        );

        for (const player of aliveParticipants) {
            // Base experience: 50 XP
            let experience = 50;

            // Bonus for round efficiency (less rounds = more XP)
            const roundBonus = Math.max(0, (10 - combatSession.round) * 5);
            experience += roundBonus;

            // Bonus for number of enemies defeated
            const enemyCount = combatSession.participants.length - 1; // Exclude the player
            const enemyBonus = enemyCount * 25;
            experience += enemyBonus;

            // Award the experience
            this.classManager.awardExperience(player, experience);

            if (roundBonus > 0) {
                player.sendMessage(`§b+${roundBonus} XP §7(Quick Victory Bonus)`);
            }
            if (enemyBonus > 0) {
                player.sendMessage(`§b+${enemyBonus} XP §7(Multiple Enemies Bonus)`);
            }
        }
    }
    
    /**
     * Utility methods
     */
    
    generateCombatId() {
        return `combat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    isInCombat(entity) {
        for (const combat of this.activeCombats.values()) {
            if (combat.participants.includes(entity)) {
                return true;
            }
        }
        return false;
    }
    
    freezeParticipants(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.isValid()) {
                participant.addTag("in_combat");
                participant.addTag("frozen");
            }
        }
    }
    
    unfreezeParticipants(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.isValid()) {
                participant.removeTag("in_combat");
                participant.removeTag("frozen");
            }
        }
    }
    
    notifyParticipants(combatSession, message) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                participant.sendMessage(`§6[Combat] §r${message}`);
            }
        }
    }
    
    setTurnTimer(combatSession) {
        // 30 second turn timer
        system.runTimeout(() => {
            if (this.activeCombats.has(combatSession.id)) {
                this.endTurn(combatSession);
            }
        }, 600); // 30 seconds
    }
    
    applyDamage(target, damage, source) {
        const health = target.getComponent("health");
        if (health) {
            health.setCurrentValue(Math.max(0, health.currentValue - damage));
        }
    }
    
    handlePlayerLeave(player) {
        // End any combats the player was in
        for (const [combatId, combat] of this.activeCombats.entries()) {
            if (combat.participants.includes(player)) {
                this.endCombat(combat, "player_left");
            }
        }
    }
    
    toggleDebugMode(player) {
        if (this.debugMode.has(player.id)) {
            this.debugMode.delete(player.id);
            player.sendMessage("§cDebug mode disabled");
        } else {
            this.debugMode.add(player.id);
            player.sendMessage("§aDebug mode enabled");
        }
    }
    
    handleCardItem(player, itemStack) {
        // Handle card-related items
        if (this.isInCombat(player)) {
            // Handle card play during combat
            this.uiManager.handleCardPlay(player, itemStack);
        } else {
            // Handle card items outside combat (deck building, etc.)
            this.uiManager.handleCardItemOutsideCombat(player, itemStack);
        }
    }

    /**
     * Camera Management Methods
     */

    /**
     * Initialize combat cameras for all player participants
     */
    initializeCombatCameras(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                const success = this.cameraManager.startCombatCamera(participant, combatSession);
                if (!success) {
                    console.warn(`Failed to start combat camera for player ${participant.name}`);
                }
            }
        }
    }

    /**
     * End combat cameras for all player participants
     */
    endCombatCameras(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                const success = this.cameraManager.endCombatCamera(participant);
                if (!success) {
                    console.warn(`Failed to end combat camera for player ${participant.name}`);
                }
            }
        }
    }

    /**
     * Focus camera on current turn participant
     */
    focusCameraOnCurrentTurn(combatSession) {
        const currentParticipant = combatSession.turnOrder[combatSession.currentTurn];
        if (!currentParticipant || !currentParticipant.isValid()) return;

        // Focus all player cameras on the current turn participant
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                this.cameraManager.focusOnParticipant(participant, currentParticipant);
            }
        }
    }

    /**
     * Calculate combat center for camera positioning
     */
    calculateCombatCenter(participants) {
        if (participants.length === 0) {
            return { x: 0, y: 70, z: 0 };
        }

        let totalX = 0, totalY = 0, totalZ = 0;
        let validCount = 0;

        for (const participant of participants) {
            if (participant.isValid()) {
                const pos = participant.location;
                totalX += pos.x;
                totalY += pos.y;
                totalZ += pos.z;
                validCount++;
            }
        }

        if (validCount === 0) {
            return { x: 0, y: 70, z: 0 };
        }

        return {
            x: totalX / validCount,
            y: totalY / validCount,
            z: totalZ / validCount
        };
    }

    /**
     * Emergency camera reset for all players
     */
    emergencyResetAllCameras() {
        for (const player of world.getAllPlayers()) {
            if (this.cameraManager.hasActiveCombatCamera(player)) {
                this.cameraManager.emergencyResetCamera(player);
            }
        }
    }

    /**
     * Update camera system (called from main update loop)
     */
    updateCameras() {
        this.cameraManager.updateCameras();
    }

    /**
     * Cleanup camera system
     */
    cleanupCameras() {
        this.cameraManager.cleanup();
    }
}
