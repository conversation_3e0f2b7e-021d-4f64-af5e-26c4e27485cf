/**
 * CombatManager - Handles turn-based combat logic
 */

import { world, system } from "@minecraft/server";
import { CombatCameraManager } from "../camera/CombatCameraManager.js";

export class CombatManager {
    constructor(cardManager, uiManager, playerDataManager, audioManager = null) {
        this.cardManager = cardManager;
        this.uiManager = uiManager;
        this.playerDataManager = playerDataManager;
        this.audioManager = audioManager;
        this.cameraManager = new CombatCameraManager();
        
        // Active combat sessions
        this.activeCombats = new Map();
        
        // Debug mode
        this.debugMode = new Set();
        
        // Combat detection settings
        this.combatRange = 8; // Range to detect combat
        this.combatCooldown = new Map(); // Prevent spam combat initiation
    }
    
    /**
     * Handle entity hurt events
     */
    handleEntityHurt(event) {
        const { hurtEntity, damageSource } = event;
        
        // Check if this should trigger turn-based combat
        if (this.shouldStartCombat(hurtEntity, damageSource)) {
            // Cancel the original damage
            event.cancel = true;
            
            // Start turn-based combat
            this.initiateCombat(hurtEntity, damageSource.damagingEntity);
        }
    }
    
    /**
     * Handle entity hit events
     */
    handleEntityHit(event) {
        const { damagingEntity, hitEntity } = event;
        
        // Check if this should trigger turn-based combat
        if (this.shouldStartCombat(hitEntity, { damagingEntity })) {
            // Start turn-based combat
            this.initiateCombat(hitEntity, damagingEntity);
        }
    }
    
    /**
     * Handle item use events
     */
    handleItemUse(event) {
        const { source, itemStack } = event;
        
        // Check if it's a card-related item
        if (itemStack.typeId.includes("card_combat")) {
            this.handleCardItem(source, itemStack);
        }
    }
    
    /**
     * Determine if combat should start
     */
    shouldStartCombat(target, damageSource) {
        // Don't start combat if already in combat
        if (this.isInCombat(target)) {
            return false;
        }
        
        // Check cooldown
        const targetId = target.id;
        if (this.combatCooldown.has(targetId)) {
            const cooldownTime = this.combatCooldown.get(targetId);
            if (Date.now() - cooldownTime < 3000) { // 3 second cooldown
                return false;
            }
        }
        
        // Must have a damaging entity
        if (!damageSource.damagingEntity) {
            return false;
        }
        
        const attacker = damageSource.damagingEntity;
        
        // At least one participant must be a player
        const targetIsPlayer = target.typeId === "minecraft:player";
        const attackerIsPlayer = attacker.typeId === "minecraft:player";
        
        if (!targetIsPlayer && !attackerIsPlayer) {
            return false;
        }
        
        // Both entities must be alive
        if (!target.isValid() || !attacker.isValid()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Initiate turn-based combat
     */
    initiateCombat(entity1, entity2) {
        const combatId = this.generateCombatId();
        
        // Set cooldown
        this.combatCooldown.set(entity1.id, Date.now());
        this.combatCooldown.set(entity2.id, Date.now());
        
        // Create combat session
        const combatSession = {
            id: combatId,
            participants: [entity1, entity2],
            currentTurn: 0,
            turnOrder: this.determineTurnOrder([entity1, entity2]),
            round: 1,
            state: "starting",
            startTime: Date.now(),
            location: this.calculateCombatCenter([entity1, entity2])
        };
        
        this.activeCombats.set(combatId, combatSession);
        
        // Notify participants
        this.notifyParticipants(combatSession, "Combat initiated!");
        
        // Start combat sequence
        this.startCombatSequence(combatSession);
        
        if (this.debugMode.size > 0) {
            console.log(`Combat started: ${combatId}`, combatSession);
        }
    }
    
    /**
     * Start the combat sequence
     */
    startCombatSequence(combatSession) {
        // Freeze participants
        this.freezeParticipants(combatSession);
        
        // Initialize player hands if they're players
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player") {
                this.playerDataManager.drawInitialHand(participant);
            }
        }
        
        // Set state to active
        combatSession.state = "active";

        // Initialize combat cameras for all player participants
        this.initializeCombatCameras(combatSession);

        // Play combat start sound
        if (this.audioManager) {
            this.audioManager.playSoundForCombat('combat_start', combatSession);
        }

        // Start first turn
        this.startTurn(combatSession);
    }
    
    /**
     * Determine turn order based on speed/initiative
     */
    determineTurnOrder(participants) {
        // For now, players go first, then by entity type
        return participants.sort((a, b) => {
            if (a.typeId === "minecraft:player" && b.typeId !== "minecraft:player") {
                return -1;
            }
            if (b.typeId === "minecraft:player" && a.typeId !== "minecraft:player") {
                return 1;
            }
            return 0;
        });
    }
    
    /**
     * Start a turn for the current participant
     */
    startTurn(combatSession) {
        const currentParticipant = combatSession.turnOrder[combatSession.currentTurn];
        
        if (!currentParticipant.isValid()) {
            this.endCombat(combatSession, "participant_invalid");
            return;
        }
        
        // Focus camera on current turn participant
        this.focusCameraOnCurrentTurn(combatSession);

        // Play turn start sound
        if (this.audioManager && currentParticipant.typeId === "minecraft:player") {
            this.audioManager.playSound('turn_start', currentParticipant);
        }

        // Show UI for players
        if (currentParticipant.typeId === "minecraft:player") {
            this.uiManager.showCombatUI(currentParticipant, combatSession);
        } else {
            // AI turn for mobs
            this.handleAITurn(combatSession, currentParticipant);
        }
        
        // Set turn timer
        this.setTurnTimer(combatSession);
    }
    
    /**
     * Handle AI turn for mobs
     */
    handleAITurn(combatSession, aiEntity) {
        // Simple AI: attack the first valid target
        const targets = combatSession.participants.filter(p => p !== aiEntity && p.isValid());
        
        if (targets.length > 0) {
            const target = targets[0];
            const damage = this.calculateAIDamage(aiEntity);
            
            // Apply damage
            this.applyDamage(target, damage, aiEntity);

            // Play attack sound
            if (this.audioManager && target.typeId === "minecraft:player") {
                this.audioManager.playAttackHit(target, damage);
            }

            // Notify participants
            this.notifyParticipants(combatSession, `${aiEntity.typeId} attacks for ${damage} damage!`);
        }
        
        // End turn after a short delay
        system.runTimeout(() => {
            this.endTurn(combatSession);
        }, 40); // 2 seconds
    }
    
    /**
     * Calculate AI damage
     */
    calculateAIDamage(aiEntity) {
        // Base damage based on entity type
        const baseDamage = {
            "minecraft:zombie": 3,
            "minecraft:skeleton": 2,
            "minecraft:spider": 2,
            "minecraft:creeper": 4
        };
        
        return baseDamage[aiEntity.typeId] || 2;
    }
    
    /**
     * End current turn and move to next
     */
    endTurn(combatSession) {
        // Move to next participant
        combatSession.currentTurn = (combatSession.currentTurn + 1) % combatSession.turnOrder.length;
        
        // If we've completed a full round
        if (combatSession.currentTurn === 0) {
            combatSession.round++;
        }
        
        // Check for combat end conditions
        if (this.checkCombatEndConditions(combatSession)) {
            return;
        }
        
        // Start next turn
        this.startTurn(combatSession);
    }
    
    /**
     * Check if combat should end
     */
    checkCombatEndConditions(combatSession) {
        const aliveParticipants = combatSession.participants.filter(p => p.isValid() && p.getComponent("health").currentValue > 0);
        
        if (aliveParticipants.length <= 1) {
            this.endCombat(combatSession, "victory");
            return true;
        }
        
        // Max rounds limit
        if (combatSession.round > 20) {
            this.endCombat(combatSession, "timeout");
            return true;
        }
        
        return false;
    }
    
    /**
     * End combat session
     */
    endCombat(combatSession, reason) {
        // Unfreeze participants
        this.unfreezeParticipants(combatSession);
        
        // End combat cameras for all player participants
        this.endCombatCameras(combatSession);

        // Play combat end sound
        if (this.audioManager) {
            this.audioManager.playSoundForCombat('combat_end', combatSession);
        }

        // Close UIs
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                this.uiManager.closeCombatUI(participant);
            }
        }
        
        // Notify participants
        this.notifyParticipants(combatSession, `Combat ended: ${reason}`);
        
        // Remove from active combats
        this.activeCombats.delete(combatSession.id);
        
        if (this.debugMode.size > 0) {
            console.log(`Combat ended: ${combatSession.id}, reason: ${reason}`);
        }
    }
    
    /**
     * Utility methods
     */
    
    generateCombatId() {
        return `combat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    isInCombat(entity) {
        for (const combat of this.activeCombats.values()) {
            if (combat.participants.includes(entity)) {
                return true;
            }
        }
        return false;
    }
    
    freezeParticipants(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.isValid()) {
                participant.addTag("in_combat");
                participant.addTag("frozen");
            }
        }
    }
    
    unfreezeParticipants(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.isValid()) {
                participant.removeTag("in_combat");
                participant.removeTag("frozen");
            }
        }
    }
    
    notifyParticipants(combatSession, message) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                participant.sendMessage(`§6[Combat] §r${message}`);
            }
        }
    }
    
    setTurnTimer(combatSession) {
        // 30 second turn timer
        system.runTimeout(() => {
            if (this.activeCombats.has(combatSession.id)) {
                this.endTurn(combatSession);
            }
        }, 600); // 30 seconds
    }
    
    applyDamage(target, damage, source) {
        const health = target.getComponent("health");
        if (health) {
            health.setCurrentValue(Math.max(0, health.currentValue - damage));
        }
    }
    
    handlePlayerLeave(player) {
        // End any combats the player was in
        for (const [combatId, combat] of this.activeCombats.entries()) {
            if (combat.participants.includes(player)) {
                this.endCombat(combat, "player_left");
            }
        }
    }
    
    toggleDebugMode(player) {
        if (this.debugMode.has(player.id)) {
            this.debugMode.delete(player.id);
            player.sendMessage("§cDebug mode disabled");
        } else {
            this.debugMode.add(player.id);
            player.sendMessage("§aDebug mode enabled");
        }
    }
    
    handleCardItem(player, itemStack) {
        // Handle card-related items
        if (this.isInCombat(player)) {
            // Handle card play during combat
            this.uiManager.handleCardPlay(player, itemStack);
        } else {
            // Handle card items outside combat (deck building, etc.)
            this.uiManager.handleCardItemOutsideCombat(player, itemStack);
        }
    }

    /**
     * Camera Management Methods
     */

    /**
     * Initialize combat cameras for all player participants
     */
    initializeCombatCameras(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                const success = this.cameraManager.startCombatCamera(participant, combatSession);
                if (!success) {
                    console.warn(`Failed to start combat camera for player ${participant.name}`);
                }
            }
        }
    }

    /**
     * End combat cameras for all player participants
     */
    endCombatCameras(combatSession) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                const success = this.cameraManager.endCombatCamera(participant);
                if (!success) {
                    console.warn(`Failed to end combat camera for player ${participant.name}`);
                }
            }
        }
    }

    /**
     * Focus camera on current turn participant
     */
    focusCameraOnCurrentTurn(combatSession) {
        const currentParticipant = combatSession.turnOrder[combatSession.currentTurn];
        if (!currentParticipant || !currentParticipant.isValid()) return;

        // Focus all player cameras on the current turn participant
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                this.cameraManager.focusOnParticipant(participant, currentParticipant);
            }
        }
    }

    /**
     * Calculate combat center for camera positioning
     */
    calculateCombatCenter(participants) {
        if (participants.length === 0) {
            return { x: 0, y: 70, z: 0 };
        }

        let totalX = 0, totalY = 0, totalZ = 0;
        let validCount = 0;

        for (const participant of participants) {
            if (participant.isValid()) {
                const pos = participant.location;
                totalX += pos.x;
                totalY += pos.y;
                totalZ += pos.z;
                validCount++;
            }
        }

        if (validCount === 0) {
            return { x: 0, y: 70, z: 0 };
        }

        return {
            x: totalX / validCount,
            y: totalY / validCount,
            z: totalZ / validCount
        };
    }

    /**
     * Emergency camera reset for all players
     */
    emergencyResetAllCameras() {
        for (const player of world.getAllPlayers()) {
            if (this.cameraManager.hasActiveCombatCamera(player)) {
                this.cameraManager.emergencyResetCamera(player);
            }
        }
    }

    /**
     * Update camera system (called from main update loop)
     */
    updateCameras() {
        this.cameraManager.updateCameras();
    }

    /**
     * Cleanup camera system
     */
    cleanupCameras() {
        this.cameraManager.cleanup();
    }
}
