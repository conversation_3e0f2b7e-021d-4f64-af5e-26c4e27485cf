/**
 * UltimateCards - Special cards for ultimate abilities
 */

export class UltimateCards {
    static getUltimateCards() {
        return {
            // Warrior Ultimates
            berserker_wrath: {
                id: "berserker_wrath",
                name: "<PERSON><PERSON>rk<PERSON>'s Wrath",
                description: "For 3 turns: double attack damage, heal for 25% of damage dealt",
                type: "ultimate",
                energyCost: 6,
                rarity: "legendary",
                classRestriction: "warrior",
                levelRequirement: 10,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "ultimate10"
                    }
                ]
            },
            
            avatar_of_war: {
                id: "avatar_of_war",
                name: "Avatar of War",
                description: "Enhanced <PERSON>rserk<PERSON>'s Wrath: +50% damage to all allies, immunity to damage for 1 turn",
                type: "ultimate",
                energyCost: 8,
                rarity: "legendary",
                classRestriction: "warrior",
                levelRequirement: 20,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "master20"
                    }
                ]
            },
            
            // Mage Ultimates
            arcane_ascendance: {
                id: "arcane_ascendance",
                name: "Arcane Ascendance",
                description: "Gain 3 energy, draw 3 cards, next 3 spells cost 0 energy",
                type: "ultimate",
                energyCost: 5,
                rarity: "legendary",
                classRestriction: "mage",
                levelRequirement: 10,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "ultimate10"
                    }
                ]
            },
            
            archmage_transcendence: {
                id: "archmage_transcendence",
                name: "Archmage Transcendence",
                description: "Enhanced Arcane Ascendance: +5 energy, draw 5 cards, next 5 spells free",
                type: "ultimate",
                energyCost: 6,
                rarity: "legendary",
                classRestriction: "mage",
                levelRequirement: 20,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "master20"
                    }
                ]
            },
            
            // Rogue Ultimates
            shadow_clone: {
                id: "shadow_clone",
                name: "Shadow Clone",
                description: "Create a shadow clone that copies your next 3 attacks with 75% damage",
                type: "ultimate",
                energyCost: 4,
                rarity: "legendary",
                classRestriction: "rogue",
                levelRequirement: 10,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "ultimate10"
                    }
                ]
            },
            
            master_assassin: {
                id: "master_assassin",
                name: "Master Assassin",
                description: "Enhanced Shadow Clone: 3 clones, 100% damage, lasts 5 attacks",
                type: "ultimate",
                energyCost: 6,
                rarity: "legendary",
                classRestriction: "rogue",
                levelRequirement: 20,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "master20"
                    }
                ]
            },
            
            // Paladin Ultimates
            divine_intervention: {
                id: "divine_intervention",
                name: "Divine Intervention",
                description: "Fully heal all allies, grant immunity for 1 turn, cleanse debuffs",
                type: "ultimate",
                energyCost: 6,
                rarity: "legendary",
                classRestriction: "paladin",
                levelRequirement: 10,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "ultimate10"
                    }
                ]
            },
            
            divine_avatar: {
                id: "divine_avatar",
                name: "Divine Avatar",
                description: "Enhanced Divine Intervention: immunity for 2 turns, +10 max health permanently",
                type: "ultimate",
                energyCost: 8,
                rarity: "legendary",
                classRestriction: "paladin",
                levelRequirement: 20,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "master20"
                    }
                ]
            },
            
            // Necromancer Ultimates
            army_of_the_dead: {
                id: "army_of_the_dead",
                name: "Army of the Dead",
                description: "Summon 3 skeleton warriors, gain energy equal to enemies killed",
                type: "ultimate",
                energyCost: 5,
                rarity: "legendary",
                classRestriction: "necromancer",
                levelRequirement: 10,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "ultimate10"
                    }
                ]
            },
            
            lich_lord: {
                id: "lich_lord",
                name: "Lich Lord",
                description: "Enhanced Army: 5 skeletons, gain 2 energy per kill, skeletons have 15 HP",
                type: "ultimate",
                energyCost: 7,
                rarity: "legendary",
                classRestriction: "necromancer",
                levelRequirement: 20,
                effects: [
                    {
                        type: "ultimate_ability",
                        abilityId: "master20"
                    }
                ]
            }
        };
    }
    
    /**
     * Get ultimate cards for a specific class
     */
    static getClassUltimateCards(className) {
        const allCards = this.getUltimateCards();
        const classCards = {};
        
        for (const [id, card] of Object.entries(allCards)) {
            if (card.classRestriction === className) {
                classCards[id] = card;
            }
        }
        
        return classCards;
    }
    
    /**
     * Get ultimate cards available to a player based on level
     */
    static getAvailableUltimateCards(className, level) {
        const classCards = this.getClassUltimateCards(className);
        const availableCards = {};
        
        for (const [id, card] of Object.entries(classCards)) {
            if (level >= card.levelRequirement) {
                availableCards[id] = card;
            }
        }
        
        return availableCards;
    }
}
