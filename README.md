# Card Combat System - Minecraft Bedrock Edition Addon

A comprehensive turn-based card combat system that transforms Minecraft's combat into strategic card-based encounters.

## 🎮 Features

### Core Combat System
- **Turn-Based Combat**: All combat becomes strategic and turn-based
- **Card-Based Actions**: Use cards instead of direct attacks
- **Energy System**: Manage energy to play cards strategically
- **Status Effects**: Buffs, debuffs, and damage over time effects
- **Block System**: Defensive mechanics to mitigate damage
- **Combat Camera**: Automatic top-down camera view for strategic overview
- **Audio System**: Complete audio feedback with 18+ sound effects

### Card System
- **50+ Unique Cards**: Attack, Defense, Spell, and Item cards
- **4 Rarity Tiers**: Common, Uncommon, Rare, and Legendary
- **Deck Building**: Customize your deck with collected cards
- **Card Effects**: Special abilities and synergies
- **Card Packs**: Collectible packs with random cards

### Progression System
- **50 Levels**: Extensive character progression
- **Experience Rewards**: Gain XP from combat victories
- **Achievements**: 15+ achievements to unlock
- **Card Unlocks**: New cards unlock as you level up
- **Titles**: Earn prestigious titles as you advance

### AI System
- **Smart AI**: Entities use strategic card-based combat
- **Difficulty Scaling**: AI adapts to player skill level
- **Card Duelists**: Special NPCs that challenge players
- **Varied Strategies**: Different AI personalities and tactics

## 🚀 Installation

1. Download the addon files
2. Import the behavior pack into your world
3. Import the resource pack into your world
4. Enable both packs in world settings
5. Start playing!

## 🎯 How to Play

### Starting Combat
- Attack any mob to initiate turn-based combat
- Combat automatically freezes participants
- Camera switches to top-down strategic view
- Turn order is determined by initiative rolls

### Playing Cards
- Use the combat UI to select cards from your hand
- Each card costs energy to play
- Different card types have different effects:
  - **Attack Cards**: Deal damage to enemies
  - **Defense Cards**: Gain block to reduce incoming damage
  - **Spell Cards**: Magical effects with various abilities
  - **Item Cards**: Consumables with immediate effects

### Building Your Deck
- Use the Deck Builder item to customize your deck
- Collect cards from combat victories and card packs
- Balance your deck with different card types
- Consider card synergies and combos

### Progression
- Win combats to gain experience
- Level up to unlock new cards and abilities
- Complete achievements for bonus rewards
- Collect card fragments to craft new cards

## 🎮 Commands

### Player Commands
- `!combat deck` - Open deck builder
- `!combat collection` - View your card collection
- `!combat progress` - Check your progression status
- `!combat resetcamera` - Reset camera to normal view
- `!combat help` - Show all available commands

### Admin Commands (requires admin tag)
- `!combat reset` - Reset player data
- `!combat test` - Run system tests
- `!combat scenario <name>` - Create test scenarios
- `!combat performance` - Run performance tests
- `!combat memory` - Check memory usage
- `!combat testsounds` - Test all audio effects

## 🔧 Technical Details

- **Compatibility**: Minecraft Bedrock Edition 1.21.70+
- **Script API**: Uses Script API 2.0.0 for advanced functionality
- **Architecture**: Modular system with comprehensive testing
- **Performance**: Optimized for multiplayer environments
- **Extensibility**: Easy to add new cards and features

## 📋 System Requirements

- Minecraft Bedrock Edition 1.21.70+
- Experimental Features: Holiday Creator Features enabled
- Script API enabled in world settings
- Both behavior and resource packs installed

## 🧪 Testing & Development

The addon includes comprehensive testing tools and development utilities for ensuring system stability and performance.

---

**Version**: 1.0.0
**Last Updated**: 2024-12-19
**Author**: Card Combat Development Team
