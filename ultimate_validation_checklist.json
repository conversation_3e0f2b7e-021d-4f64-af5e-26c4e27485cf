{"system_integration": ["☐ UltimateManager properly initialized in main.js", "☐ ClassManager has reference to UltimateManager", "☐ CombatManager integrates with UltimateManager", "☐ ClassCommands includes ultimate commands", "☐ PlayerDataManager includes ability fields"], "ability_unlocks": ["☐ Level 5: Passive abilities unlock and show notification", "☐ Level 10: Ultimate abilities unlock with cards added to deck", "☐ Level 15: Advanced passives unlock", "☐ Level 20: Master abilities unlock with enhanced cards"], "passive_abilities": ["☐ Battle Hardened: +1 block at turn start (<PERSON>)", "☐ Arcane Intellect: Extra card when at max energy (Mage)", "☐ Assassinate: 10% instant kill on low HP enemies (Rogue)", "☐ Divine Grace: +2 HP at turn end (<PERSON><PERSON><PERSON>)", "☐ Death Magic: +1 energy on creature death (Necromancer)"], "ultimate_abilities": ["☐ <PERSON><PERSON><PERSON><PERSON>'s <PERSON>: Double damage + healing for 3 turns", "☐ Arcane Ascendance: +3 energy, +3 cards, 3 free spells", "☐ Shadow Clone: Clone copies 3 attacks at 75% damage", "☐ Divine Intervention: Heal all + immunity + cleanse", "☐ Army of the Dead: 3 skeletons + energy per kill"], "combat_integration": ["☐ Passive abilities trigger at correct combat events", "☐ Ultimate abilities consume energy and apply effects", "☐ Once-per-combat limitation enforced", "☐ Cooldowns reset between combats", "☐ Status effects properly tracked and applied"], "card_system": ["☐ Ultimate cards added to deck at correct levels", "☐ Cards have correct energy costs and descriptions", "☐ Playing ultimate cards triggers same effects as direct usage", "☐ Cards respect combat limitations"], "commands": ["☐ /class abilities shows all abilities with unlock status", "☐ /class ultimate works in combat", "☐ /class ultimate shows information out of combat", "☐ Commands provide helpful feedback"]}