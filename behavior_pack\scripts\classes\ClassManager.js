/**
 * ClassManager - Handles player classes and class-specific abilities
 */

import { world, system } from "@minecraft/server";

export class ClassManager {
    constructor(playerDataManager, cardManager) {
        this.playerDataManager = playerDataManager;
        this.cardManager = cardManager;
        
        // Available classes
        this.classes = {
            warrior: {
                name: "Warrior",
                description: "Masters of melee combat with high health and armor",
                icon: "textures/ui/class_icons/warrior",
                baseHealth: 30,
                baseEnergy: 3,
                healthPerLevel: 5,
                energyPerLevel: 1,
                specialties: ["attack", "defense"],
                startingCards: [
                    "basic_strike", "basic_strike", "basic_block", "basic_block",
                    "power_attack", "iron_defense", "health_potion"
                ],
                classCards: [
                    "berserker_rage", "shield_slam", "battle_cry", "armor_up",
                    "cleave", "intimidate", "last_stand", "weapon_mastery"
                ]
            },
            mage: {
                name: "<PERSON><PERSON>",
                description: "Wielders of arcane magic with powerful spells",
                icon: "textures/ui/class_icons/mage",
                baseHealth: 20,
                baseEnergy: 5,
                healthPerLevel: 3,
                energyPerLevel: 2,
                specialties: ["spell", "energy"],
                startingCards: [
                    "basic_strike", "basic_block", "fireball", "fireball",
                    "lightning_bolt", "heal", "energy_potion"
                ],
                classCards: [
                    "meteor", "ice_shard", "mana_burn", "arcane_missiles",
                    "teleport", "magic_shield", "spell_steal", "time_warp"
                ]
            },
            rogue: {
                name: "Rogue",
                description: "Swift assassins with critical strikes and stealth",
                icon: "textures/ui/class_icons/rogue",
                baseHealth: 25,
                baseEnergy: 4,
                healthPerLevel: 4,
                energyPerLevel: 1,
                specialties: ["critical", "speed"],
                startingCards: [
                    "basic_strike", "basic_block", "critical_strike", "critical_strike",
                    "quick_strike", "dodge", "poison_blade"
                ],
                classCards: [
                    "backstab", "stealth", "poison_cloud", "dual_strike",
                    "evasion", "shadow_step", "assassinate", "smoke_bomb"
                ]
            },
            paladin: {
                name: "Paladin",
                description: "Holy warriors with healing and protection magic",
                icon: "textures/ui/class_icons/paladin",
                baseHealth: 28,
                baseEnergy: 4,
                healthPerLevel: 4,
                energyPerLevel: 1,
                specialties: ["heal", "defense", "holy"],
                startingCards: [
                    "basic_strike", "basic_block", "heal", "heal",
                    "holy_light", "divine_protection", "blessing"
                ],
                classCards: [
                    "divine_strike", "consecration", "lay_on_hands", "sanctuary",
                    "turn_undead", "divine_favor", "resurrection", "holy_wrath"
                ]
            },
            necromancer: {
                name: "Necromancer",
                description: "Dark magic users who manipulate life and death",
                icon: "textures/ui/class_icons/necromancer",
                baseHealth: 22,
                baseEnergy: 5,
                healthPerLevel: 3,
                energyPerLevel: 2,
                specialties: ["dark", "summon", "drain"],
                startingCards: [
                    "basic_strike", "basic_block", "drain_life", "drain_life",
                    "bone_spear", "dark_ritual", "corpse_explosion"
                ],
                classCards: [
                    "raise_skeleton", "death_coil", "bone_armor", "soul_burn",
                    "life_tap", "curse", "animate_dead", "death_and_decay"
                ]
            }
        };
        
        // Class progression
        this.maxLevel = 20;
        this.experienceTable = this.generateExperienceTable();
    }
    
    /**
     * Generate experience requirements for each level
     */
    generateExperienceTable() {
        const table = [0]; // Level 1 requires 0 XP
        for (let level = 2; level <= this.maxLevel; level++) {
            // Exponential growth: level^2 * 100
            table.push(Math.floor(Math.pow(level, 2) * 100));
        }
        return table;
    }
    
    /**
     * Get player's current class
     */
    getPlayerClass(player) {
        const data = this.playerDataManager.getPlayerData(player);
        return data.class || null;
    }
    
    /**
     * Set player's class
     */
    setPlayerClass(player, className) {
        if (!this.classes[className]) {
            throw new Error(`Invalid class: ${className}`);
        }
        
        const data = this.playerDataManager.getPlayerData(player);
        const oldClass = data.class;
        
        // Set new class
        data.class = className;
        data.classLevel = 1;
        data.classExperience = 0;
        
        // Reset stats based on new class
        this.updatePlayerStats(player);
        
        // Give starting cards for new class
        this.giveStartingCards(player, className);
        
        // Notify player
        const classInfo = this.classes[className];
        player.sendMessage(`§6🎭 Class Changed! §r§7You are now a §e${classInfo.name}§7!`);
        player.sendMessage(`§7${classInfo.description}`);
        
        if (oldClass && oldClass !== className) {
            player.sendMessage(`§c⚠ Your previous class cards have been removed.`);
        }
        
        this.playerDataManager.savePlayerData(player);
    }
    
    /**
     * Give starting cards for a class
     */
    giveStartingCards(player, className) {
        const classInfo = this.classes[className];
        const data = this.playerDataManager.getPlayerData(player);
        
        // Clear existing deck
        data.deck = [];
        
        // Add starting cards
        for (const cardId of classInfo.startingCards) {
            data.deck.push(cardId);
        }
        
        player.sendMessage(`§a✓ Received ${classInfo.startingCards.length} starting cards for ${classInfo.name}!`);
    }
    
    /**
     * Update player stats based on class and level
     */
    updatePlayerStats(player) {
        const data = this.playerDataManager.getPlayerData(player);
        const className = data.class;
        
        if (!className || !this.classes[className]) return;
        
        const classInfo = this.classes[className];
        const level = data.classLevel || 1;
        
        // Calculate stats
        const maxHealth = classInfo.baseHealth + (classInfo.healthPerLevel * (level - 1));
        const maxEnergy = classInfo.baseEnergy + (classInfo.energyPerLevel * (level - 1));
        
        // Update player data
        data.maxHealth = maxHealth;
        data.maxEnergy = maxEnergy;
        data.health = Math.min(data.health || maxHealth, maxHealth);
        data.energy = Math.min(data.energy || maxEnergy, maxEnergy);
    }
    
    /**
     * Award experience to player
     */
    awardExperience(player, amount) {
        const data = this.playerDataManager.getPlayerData(player);
        const className = data.class;
        
        if (!className) return;
        
        const oldLevel = data.classLevel || 1;
        data.classExperience = (data.classExperience || 0) + amount;
        
        // Check for level up
        const newLevel = this.calculateLevel(data.classExperience);
        
        if (newLevel > oldLevel && newLevel <= this.maxLevel) {
            data.classLevel = newLevel;
            this.handleLevelUp(player, oldLevel, newLevel);
        }
        
        player.sendMessage(`§b+${amount} XP §7(${data.classExperience}/${this.getExperienceForNextLevel(newLevel)})`);
        this.playerDataManager.savePlayerData(player);
    }
    
    /**
     * Calculate level from experience
     */
    calculateLevel(experience) {
        for (let level = this.maxLevel; level >= 1; level--) {
            if (experience >= this.experienceTable[level - 1]) {
                return level;
            }
        }
        return 1;
    }
    
    /**
     * Get experience required for next level
     */
    getExperienceForNextLevel(currentLevel) {
        if (currentLevel >= this.maxLevel) return this.experienceTable[this.maxLevel - 1];
        return this.experienceTable[currentLevel];
    }
    
    /**
     * Handle level up
     */
    handleLevelUp(player, oldLevel, newLevel) {
        const data = this.playerDataManager.getPlayerData(player);
        const className = data.class;
        const classInfo = this.classes[className];
        
        // Update stats
        this.updatePlayerStats(player);
        
        // Notify player
        player.sendMessage(`§6🎉 LEVEL UP! §r§e${classInfo.name} Level ${newLevel}§r`);
        player.sendMessage(`§a+${classInfo.healthPerLevel} Health, +${classInfo.energyPerLevel} Energy`);
        
        // Award class card every 3 levels
        if (newLevel % 3 === 0 && newLevel <= 18) {
            this.awardClassCard(player, className);
        }
        
        // Special rewards at certain levels
        if (newLevel === 5) {
            player.sendMessage(`§d🎁 Unlocked: Advanced Card Packs!`);
        } else if (newLevel === 10) {
            player.sendMessage(`§d🎁 Unlocked: Legendary Card Packs!`);
        } else if (newLevel === 20) {
            player.sendMessage(`§6👑 MAX LEVEL REACHED! You are a master ${classInfo.name}!`);
        }
    }
    
    /**
     * Award a random class card
     */
    awardClassCard(player, className) {
        const classInfo = this.classes[className];
        const data = this.playerDataManager.getPlayerData(player);
        
        // Get cards player doesn't have yet
        const availableCards = classInfo.classCards.filter(cardId => 
            !data.deck.includes(cardId)
        );
        
        if (availableCards.length === 0) {
            player.sendMessage(`§e📚 You already have all ${classInfo.name} class cards!`);
            return;
        }
        
        // Award random card
        const cardId = availableCards[Math.floor(Math.random() * availableCards.length)];
        data.deck.push(cardId);
        
        player.sendMessage(`§d🎴 New Class Card: §e${this.cardManager.getCardName(cardId)}§r`);
        this.playerDataManager.savePlayerData(player);
    }
    
    /**
     * Get class info for display
     */
    getClassInfo(className) {
        return this.classes[className] || null;
    }
    
    /**
     * Get all available classes
     */
    getAllClasses() {
        return Object.keys(this.classes);
    }
    
    /**
     * Check if player can use a card (class restrictions)
     */
    canUseCard(player, cardId) {
        const data = this.playerDataManager.getPlayerData(player);
        const className = data.class;
        
        if (!className) return true; // No class = can use any card
        
        const classInfo = this.classes[className];
        
        // Check if it's a class-specific card
        for (const [otherClass, otherInfo] of Object.entries(this.classes)) {
            if (otherClass !== className && otherInfo.classCards.includes(cardId)) {
                return false; // Can't use other class cards
            }
        }
        
        return true; // Can use neutral cards and own class cards
    }
    
    /**
     * Get class bonus for card effects
     */
    getClassBonus(player, cardType) {
        const data = this.playerDataManager.getPlayerData(player);
        const className = data.class;
        
        if (!className) return 1.0;
        
        const classInfo = this.classes[className];
        const level = data.classLevel || 1;
        
        // 5% bonus per level for specialty types
        if (classInfo.specialties.includes(cardType)) {
            return 1.0 + (level * 0.05);
        }
        
        return 1.0;
    }
}
