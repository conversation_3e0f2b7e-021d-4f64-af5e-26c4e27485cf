/**
 * PlayerDataManager - Handles player data, decks, collections, and progression
 */

import { world } from "@minecraft/server";

export class PlayerDataManager {
    constructor() {
        this.playerData = new Map();
        this.defaultHandSize = 5;
        this.maxHandSize = 10;
        this.defaultEnergy = 3;
        this.maxEnergy = 10;
    }
    
    /**
     * Initialize player data
     */
    initializePlayer(player) {
        if (this.playerData.has(player.id)) {
            return; // Already initialized
        }
        
        // Load existing data or create new
        const savedData = this.loadPlayerData(player);
        
        if (savedData) {
            this.playerData.set(player.id, savedData);
        } else {
            // Create new player data
            const newPlayerData = {
                playerId: player.id,
                playerName: player.name,
                
                // Card Collection
                collection: this.createStarterCollection(),
                
                // Deck Management
                activeDeck: this.createStarterDeck(),
                savedDecks: new Map(),
                
                // Combat State
                currentHand: [],
                currentEnergy: this.defaultEnergy,
                maxEnergy: this.defaultEnergy,
                block: 0,
                statusEffects: new Map(),
                
                // Progression
                level: 1,
                experience: 0,
                wins: 0,
                losses: 0,
                cardsPlayed: 0,
                
                // Settings
                autoEndTurn: false,
                showCardTooltips: true,
                
                // Timestamps
                createdAt: Date.now(),
                lastPlayed: Date.now()
            };
            
            this.playerData.set(player.id, newPlayerData);
            this.savePlayerData(player);
            
            // Welcome message
            player.sendMessage("§aWelcome to Card Combat! You've received a starter deck.");
            player.sendMessage("§eUse !combat deck to customize your deck or !combat help for commands.");
        }
    }
    
    /**
     * Get player data
     */
    getPlayerData(player) {
        return this.playerData.get(player.id);
    }
    
    /**
     * Update player data
     */
    updatePlayerData(player, updates) {
        const data = this.getPlayerData(player);
        if (data) {
            Object.assign(data, updates);
            data.lastPlayed = Date.now();
            this.savePlayerData(player);
        }
    }
    
    /**
     * Create starter collection
     */
    createStarterCollection() {
        const collection = new Map();
        
        // Give starter cards
        const starterCards = [
            { id: "basic_strike", count: 5 },
            { id: "power_attack", count: 2 },
            { id: "basic_block", count: 5 },
            { id: "shield_wall", count: 2 },
            { id: "heal", count: 3 },
            { id: "health_potion", count: 3 }
        ];
        
        for (const { id, count } of starterCards) {
            collection.set(id, count);
        }
        
        return collection;
    }
    
    /**
     * Create starter deck
     */
    createStarterDeck() {
        return [
            "basic_strike", "basic_strike", "basic_strike",
            "power_attack", "power_attack",
            "basic_block", "basic_block", "basic_block",
            "shield_wall",
            "heal", "heal",
            "health_potion", "health_potion"
        ];
    }
    
    /**
     * Draw initial hand for combat
     */
    drawInitialHand(player) {
        const data = this.getPlayerData(player);
        if (!data) return;
        
        // Shuffle deck
        const shuffledDeck = this.shuffleArray([...data.activeDeck]);
        
        // Draw initial hand
        data.currentHand = shuffledDeck.slice(0, this.defaultHandSize);
        data.currentEnergy = data.maxEnergy;
        data.block = 0;
        data.statusEffects.clear();
        
        this.updatePlayerData(player, data);
    }
    
    /**
     * Draw a card
     */
    drawCard(player) {
        const data = this.getPlayerData(player);
        if (!data) return null;
        
        if (data.currentHand.length >= this.maxHandSize) {
            return null; // Hand full
        }
        
        // For simplicity, draw a random card from deck
        // In a full implementation, this would use a proper deck/discard system
        const availableCards = data.activeDeck.filter(cardId => 
            !data.currentHand.includes(cardId) || 
            data.activeDeck.filter(id => id === cardId).length > 1
        );
        
        if (availableCards.length === 0) {
            return null; // No cards to draw
        }
        
        const drawnCard = availableCards[Math.floor(Math.random() * availableCards.length)];
        data.currentHand.push(drawnCard);
        
        this.updatePlayerData(player, data);
        return drawnCard;
    }
    
    /**
     * Play a card
     */
    playCard(player, cardId) {
        const data = this.getPlayerData(player);
        if (!data) return false;
        
        // Check if card is in hand
        const cardIndex = data.currentHand.indexOf(cardId);
        if (cardIndex === -1) {
            return false;
        }
        
        // Check energy cost
        const cardCost = this.getCardCost(cardId);
        if (data.currentEnergy < cardCost) {
            return false;
        }
        
        // Remove card from hand
        data.currentHand.splice(cardIndex, 1);
        
        // Spend energy
        data.currentEnergy -= cardCost;
        
        // Update stats
        data.cardsPlayed++;
        
        this.updatePlayerData(player, data);
        return true;
    }
    
    /**
     * Add card to collection
     */
    addCardToCollection(player, cardId, count = 1) {
        const data = this.getPlayerData(player);
        if (!data) return;
        
        const currentCount = data.collection.get(cardId) || 0;
        data.collection.set(cardId, currentCount + count);
        
        this.updatePlayerData(player, data);
        
        player.sendMessage(`§aReceived ${count}x ${cardId}!`);
    }
    
    /**
     * Remove card from collection
     */
    removeCardFromCollection(player, cardId, count = 1) {
        const data = this.getPlayerData(player);
        if (!data) return false;
        
        const currentCount = data.collection.get(cardId) || 0;
        if (currentCount < count) {
            return false;
        }
        
        const newCount = currentCount - count;
        if (newCount === 0) {
            data.collection.delete(cardId);
        } else {
            data.collection.set(cardId, newCount);
        }
        
        this.updatePlayerData(player, data);
        return true;
    }
    
    /**
     * Save deck
     */
    saveDeck(player, deckName, deck) {
        const data = this.getPlayerData(player);
        if (!data) return false;
        
        // Validate deck
        if (!this.validateDeck(deck, data.collection)) {
            return false;
        }
        
        data.savedDecks.set(deckName, [...deck]);
        this.updatePlayerData(player, data);
        
        player.sendMessage(`§aDeck "${deckName}" saved!`);
        return true;
    }
    
    /**
     * Load deck
     */
    loadDeck(player, deckName) {
        const data = this.getPlayerData(player);
        if (!data) return false;
        
        const deck = data.savedDecks.get(deckName);
        if (!deck) {
            return false;
        }
        
        data.activeDeck = [...deck];
        this.updatePlayerData(player, data);
        
        player.sendMessage(`§aDeck "${deckName}" loaded!`);
        return true;
    }
    
    /**
     * Validate deck against collection
     */
    validateDeck(deck, collection) {
        const cardCounts = new Map();
        
        // Count cards in deck
        for (const cardId of deck) {
            cardCounts.set(cardId, (cardCounts.get(cardId) || 0) + 1);
        }
        
        // Check if player owns enough cards
        for (const [cardId, count] of cardCounts) {
            const owned = collection.get(cardId) || 0;
            if (owned < count) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Add experience and handle level up
     */
    addExperience(player, amount) {
        const data = this.getPlayerData(player);
        if (!data) return;
        
        data.experience += amount;
        
        // Check for level up
        const requiredExp = this.getRequiredExperience(data.level);
        if (data.experience >= requiredExp) {
            this.levelUp(player);
        }
        
        this.updatePlayerData(player, data);
    }
    
    /**
     * Level up player
     */
    levelUp(player) {
        const data = this.getPlayerData(player);
        if (!data) return;
        
        data.level++;
        data.maxEnergy = Math.min(this.maxEnergy, this.defaultEnergy + Math.floor(data.level / 3));
        
        // Give reward card
        const rewardCard = this.generateRewardCard(data.level);
        if (rewardCard) {
            this.addCardToCollection(player, rewardCard.id, 1);
        }
        
        player.sendMessage(`§6Level Up! You are now level ${data.level}!`);
        if (rewardCard) {
            player.sendMessage(`§aReceived reward card: ${rewardCard.name}!`);
        }
        
        this.updatePlayerData(player, data);
    }
    
    /**
     * Get required experience for level
     */
    getRequiredExperience(level) {
        return level * 100 + (level - 1) * 50;
    }
    
    /**
     * Generate reward card based on level
     */
    generateRewardCard(level) {
        // Simple reward system - better cards at higher levels
        const cardManager = world.cardManager; // Assuming global access
        if (!cardManager) return null;
        
        if (level <= 5) {
            return cardManager.generateRandomCard();
        } else if (level <= 10) {
            // Higher chance for uncommon+
            const random = Math.random();
            if (random < 0.7) {
                return cardManager.getCardsByRarity("uncommon")[0];
            } else {
                return cardManager.generateRandomCard();
            }
        } else {
            // Even higher chance for rare+
            const random = Math.random();
            if (random < 0.5) {
                return cardManager.getCardsByRarity("rare")[0];
            } else if (random < 0.8) {
                return cardManager.getCardsByRarity("uncommon")[0];
            } else {
                return cardManager.generateRandomCard();
            }
        }
    }
    
    /**
     * Reset player data
     */
    resetPlayer(player) {
        this.playerData.delete(player.id);
        this.deletePlayerData(player);
        this.initializePlayer(player);
    }
    
    /**
     * Utility methods
     */
    
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    getCardCost(cardId) {
        // This should reference the CardManager
        // For now, return a default cost
        return 1;
    }
    
    /**
     * Data persistence methods (simplified)
     */
    
    savePlayerData(player) {
        const data = this.getPlayerData(player);
        if (data) {
            // In a real implementation, this would save to world storage
            // For now, we'll use player dynamic properties
            try {
                player.setDynamicProperty("cardCombatData", JSON.stringify(data));
            } catch (error) {
                console.warn("Failed to save player data:", error);
            }
        }
    }
    
    loadPlayerData(player) {
        try {
            const savedData = player.getDynamicProperty("cardCombatData");
            if (savedData) {
                const data = JSON.parse(savedData);
                // Convert Maps back from objects
                data.collection = new Map(Object.entries(data.collection || {}));
                data.savedDecks = new Map(Object.entries(data.savedDecks || {}));
                data.statusEffects = new Map(Object.entries(data.statusEffects || {}));
                return data;
            }
        } catch (error) {
            console.warn("Failed to load player data:", error);
        }
        return null;
    }
    
    deletePlayerData(player) {
        try {
            player.setDynamicProperty("cardCombatData", undefined);
        } catch (error) {
            console.warn("Failed to delete player data:", error);
        }
    }
}
