#!/usr/bin/env python3
"""
Direct Asset Generator for Minecraft Addon
Uses ElevenLabs and other APIs directly to generate audio and textures
"""

import os
import requests
import json
from pathlib import Path

class AssetGenerator:
    def __init__(self):
        self.base_path = Path("generated_assets")
        self.audio_path = self.base_path / "audio"
        self.texture_path = self.base_path / "textures"
        self.ui_path = self.base_path / "ui"
        
        # Create directories if they don't exist
        for path in [self.audio_path, self.texture_path, self.ui_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    def generate_audio_elevenlabs(self, text, voice_id="21m00Tcm4TlvDq8ikWAM", api_key=None):
        """
        Generate audio using ElevenLabs API
        """
        if not api_key:
            print("❌ ElevenLabs API key required")
            return None
            
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
        
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": api_key
        }
        
        data = {
            "text": text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }
        
        try:
            response = requests.post(url, json=data, headers=headers)
            if response.status_code == 200:
                # Generate filename based on text
                filename = f"audio_{text[:20].replace(' ', '_').lower()}.mp3"
                filepath = self.audio_path / filename
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ Audio generated: {filepath}")
                return filepath
            else:
                print(f"❌ ElevenLabs API error: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error generating audio: {e}")
            return None
    
    def generate_minecraft_sounds(self, api_key=None):
        """
        Generate all the sounds needed for the Minecraft addon
        """
        if not api_key:
            print("❌ Please provide ElevenLabs API key")
            return
            
        # Sound effects needed for the card combat system
        sounds_to_generate = [
            "Card draw sound effect",
            "Card play magical sound",
            "Sword attack combat sound",
            "Shield block defensive sound", 
            "Spell cast magical effect",
            "Victory fanfare music",
            "Defeat sad music",
            "Turn start notification",
            "Menu button click",
            "Error notification sound"
        ]
        
        print("🎵 Generating Minecraft addon sounds...")
        generated_files = []
        
        for sound_desc in sounds_to_generate:
            print(f"Generating: {sound_desc}")
            filepath = self.generate_audio_elevenlabs(sound_desc, api_key=api_key)
            if filepath:
                generated_files.append(filepath)
        
        print(f"\n✅ Generated {len(generated_files)} audio files!")
        return generated_files
    
    def update_sound_definitions(self, generated_files):
        """
        Update the sound_definitions.json with new audio files
        """
        sound_def_path = Path("resource_pack/sounds/sound_definitions.json")
        
        if not sound_def_path.exists():
            print("❌ sound_definitions.json not found")
            return
            
        try:
            with open(sound_def_path, 'r') as f:
                sound_defs = json.load(f)
            
            # Add new sounds to definitions
            for filepath in generated_files:
                sound_name = filepath.stem  # filename without extension
                relative_path = f"sounds/{filepath.name}"
                
                if sound_name not in sound_defs.get("sound_definitions", {}):
                    sound_defs.setdefault("sound_definitions", {})[sound_name] = {
                        "category": "ui",
                        "sounds": [relative_path]
                    }
            
            # Write back to file
            with open(sound_def_path, 'w') as f:
                json.dump(sound_defs, f, indent=2)
                
            print(f"✅ Updated sound_definitions.json with {len(generated_files)} new sounds")
            
        except Exception as e:
            print(f"❌ Error updating sound definitions: {e}")
    
    def copy_to_resource_pack(self, generated_files):
        """
        Copy generated audio files to the resource pack
        """
        resource_sounds_path = Path("resource_pack/sounds")
        resource_sounds_path.mkdir(parents=True, exist_ok=True)
        
        copied_files = []
        for filepath in generated_files:
            dest_path = resource_sounds_path / filepath.name
            try:
                import shutil
                shutil.copy2(filepath, dest_path)
                copied_files.append(dest_path)
                print(f"✅ Copied {filepath.name} to resource pack")
            except Exception as e:
                print(f"❌ Error copying {filepath.name}: {e}")
        
        return copied_files

def main():
    """
    Main function to generate assets
    """
    print("🎮 Minecraft Addon Asset Generator")
    print("=" * 40)
    
    generator = AssetGenerator()
    
    # Get API key from user
    api_key = input("Enter your ElevenLabs API key (or press Enter to skip): ").strip()
    
    if api_key:
        # Generate audio files
        generated_files = generator.generate_minecraft_sounds(api_key)
        
        if generated_files:
            # Copy to resource pack
            copied_files = generator.copy_to_resource_pack(generated_files)
            
            # Update sound definitions
            generator.update_sound_definitions(copied_files)
            
            print("\n🎉 Asset generation complete!")
            print(f"Generated {len(generated_files)} audio files")
            print("Files are ready for use in your Minecraft addon!")
        else:
            print("❌ No files were generated")
    else:
        print("ℹ️  Skipping audio generation (no API key provided)")
        print("To generate audio:")
        print("1. Get API key from https://elevenlabs.io")
        print("2. Run this script again with your API key")

if __name__ == "__main__":
    main()
