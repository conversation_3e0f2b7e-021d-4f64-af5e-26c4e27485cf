/**
 * DamageCalculator - Advanced damage calculation system for card combat
 */

export class DamageCalculator {
    constructor(playerDataManager) {
        this.playerDataManager = playerDataManager;
        
        // Damage type modifiers
        this.damageTypes = {
            PHYSICAL: "physical",
            FIRE: "fire",
            ICE: "ice",
            LIGHTNING: "lightning",
            POISON: "poison",
            HOLY: "holy",
            DARK: "dark"
        };
        
        // Resistance/weakness multipliers
        this.typeEffectiveness = {
            [this.damageTypes.FIRE]: {
                weakness: ["ice"],
                resistance: ["fire"],
                immunity: []
            },
            [this.damageTypes.ICE]: {
                weakness: ["fire"],
                resistance: ["ice"],
                immunity: []
            },
            [this.damageTypes.LIGHTNING]: {
                weakness: [],
                resistance: ["lightning"],
                immunity: []
            }
        };
    }
    
    /**
     * Calculate final damage after all modifiers
     */
    calculateDamage(attacker, target, baseDamage, damageType = this.damageTypes.PHYSICAL, cardEffects = []) {
        let finalDamage = baseDamage;
        const calculation = {
            baseDamage: baseDamage,
            modifiers: [],
            finalDamage: 0,
            blocked: 0,
            actualDamage: 0
        };
        
        // Apply attacker modifiers
        const attackerModifiers = this.getAttackerModifiers(attacker, damageType, cardEffects);
        finalDamage += attackerModifiers.total;
        calculation.modifiers.push(...attackerModifiers.details);
        
        // Apply target resistances/weaknesses
        const typeModifiers = this.getTypeModifiers(target, damageType);
        finalDamage = Math.floor(finalDamage * typeModifiers.multiplier);
        if (typeModifiers.multiplier !== 1.0) {
            calculation.modifiers.push({
                name: `${damageType} type effectiveness`,
                value: `x${typeModifiers.multiplier}`,
                type: typeModifiers.multiplier > 1.0 ? "weakness" : "resistance"
            });
        }
        
        // Apply target defensive modifiers
        const defenseModifiers = this.getDefenseModifiers(target);
        finalDamage = Math.max(1, finalDamage - defenseModifiers.total);
        calculation.modifiers.push(...defenseModifiers.details);
        
        // Apply block
        const blockResult = this.applyBlock(target, finalDamage);
        calculation.blocked = blockResult.blocked;
        calculation.actualDamage = blockResult.actualDamage;
        
        calculation.finalDamage = finalDamage;
        
        return calculation;
    }
    
    /**
     * Get attacker damage modifiers
     */
    getAttackerModifiers(attacker, damageType, cardEffects) {
        const modifiers = {
            total: 0,
            details: []
        };
        
        const attackerData = this.playerDataManager.getPlayerData(attacker);
        if (!attackerData) return modifiers;
        
        // Strength buff
        const strength = attackerData.statusEffects.get("strength");
        if (strength) {
            modifiers.total += strength.amount;
            modifiers.details.push({
                name: "Strength",
                value: `+${strength.amount}`,
                type: "buff"
            });
        }
        
        // Weakness debuff
        const weakness = attackerData.statusEffects.get("weakness");
        if (weakness) {
            modifiers.total -= weakness.amount;
            modifiers.details.push({
                name: "Weakness",
                value: `-${weakness.amount}`,
                type: "debuff"
            });
        }
        
        // Rage (increases damage when low health)
        const health = attacker.getComponent("health");
        if (health) {
            const healthPercent = health.currentValue / health.defaultValue;
            if (healthPercent < 0.25) {
                const rageBonus = Math.floor(3 * (0.25 - healthPercent) / 0.25);
                modifiers.total += rageBonus;
                modifiers.details.push({
                    name: "Desperate Rage",
                    value: `+${rageBonus}`,
                    type: "conditional"
                });
            }
        }
        
        // Card-specific effects
        for (const effect of cardEffects) {
            const effectModifier = this.getCardEffectDamageModifier(effect, attacker, damageType);
            if (effectModifier.value !== 0) {
                modifiers.total += effectModifier.value;
                modifiers.details.push(effectModifier);
            }
        }
        
        return modifiers;
    }
    
    /**
     * Get type effectiveness modifiers
     */
    getTypeModifiers(target, damageType) {
        const targetData = this.playerDataManager.getPlayerData(target);
        let multiplier = 1.0;
        
        // Check for type-specific resistances/weaknesses
        if (targetData) {
            // Fire resistance
            if (damageType === this.damageTypes.FIRE && targetData.statusEffects.has("fire_resistance")) {
                multiplier *= 0.5;
            }
            
            // Ice vulnerability
            if (damageType === this.damageTypes.ICE && targetData.statusEffects.has("frozen")) {
                multiplier *= 1.5;
            }
            
            // Lightning conductivity (wet status)
            if (damageType === this.damageTypes.LIGHTNING && targetData.statusEffects.has("wet")) {
                multiplier *= 1.3;
            }
        }
        
        // Entity-specific resistances
        const entityResistances = this.getEntityTypeResistances(target, damageType);
        multiplier *= entityResistances;
        
        return { multiplier };
    }
    
    /**
     * Get entity type resistances
     */
    getEntityTypeResistances(target, damageType) {
        const entityType = target.typeId;
        
        // Undead entities
        if (target.hasTag("undead")) {
            switch (damageType) {
                case this.damageTypes.HOLY:
                    return 1.5; // Weakness to holy
                case this.damageTypes.POISON:
                    return 0.0; // Immune to poison
                case this.damageTypes.DARK:
                    return 0.5; // Resistant to dark
                default:
                    return 1.0;
            }
        }
        
        // Fire entities (blazes, magma cubes, etc.)
        if (entityType.includes("blaze") || entityType.includes("magma")) {
            switch (damageType) {
                case this.damageTypes.FIRE:
                    return 0.0; // Immune to fire
                case this.damageTypes.ICE:
                    return 2.0; // Weakness to ice
                default:
                    return 1.0;
            }
        }
        
        // Water entities
        if (entityType.includes("guardian") || entityType.includes("squid")) {
            switch (damageType) {
                case this.damageTypes.LIGHTNING:
                    return 1.5; // Weakness to lightning
                case this.damageTypes.ICE:
                    return 0.5; // Resistance to ice
                default:
                    return 1.0;
            }
        }
        
        return 1.0;
    }
    
    /**
     * Get defensive modifiers
     */
    getDefenseModifiers(target) {
        const modifiers = {
            total: 0,
            details: []
        };
        
        const targetData = this.playerDataManager.getPlayerData(target);
        if (!targetData) return modifiers;
        
        // Defense buff
        const defense = targetData.statusEffects.get("defense");
        if (defense) {
            modifiers.total += defense.amount;
            modifiers.details.push({
                name: "Defense",
                value: `-${defense.amount}`,
                type: "buff"
            });
        }
        
        // Vulnerability debuff
        const vulnerability = targetData.statusEffects.get("vulnerability");
        if (vulnerability) {
            modifiers.total -= vulnerability.amount;
            modifiers.details.push({
                name: "Vulnerability",
                value: `+${vulnerability.amount}`,
                type: "debuff"
            });
        }
        
        // Armor (equipment-based defense)
        const armorValue = this.calculateArmorValue(target);
        if (armorValue > 0) {
            modifiers.total += armorValue;
            modifiers.details.push({
                name: "Armor",
                value: `-${armorValue}`,
                type: "equipment"
            });
        }
        
        return modifiers;
    }
    
    /**
     * Calculate armor value from equipment
     */
    calculateArmorValue(entity) {
        // Simplified armor calculation
        // In a full implementation, this would check actual equipment
        let armorValue = 0;
        
        if (entity.hasTag("heavy_armor")) {
            armorValue += 3;
        } else if (entity.hasTag("medium_armor")) {
            armorValue += 2;
        } else if (entity.hasTag("light_armor")) {
            armorValue += 1;
        }
        
        return armorValue;
    }
    
    /**
     * Apply block damage reduction
     */
    applyBlock(target, damage) {
        const targetData = this.playerDataManager.getPlayerData(target);
        let blocked = 0;
        let actualDamage = damage;
        
        if (targetData && targetData.block > 0) {
            blocked = Math.min(targetData.block, damage);
            actualDamage = damage - blocked;
            
            // Reduce block
            targetData.block -= blocked;
            
            // Update player data
            this.playerDataManager.updatePlayerData(target, targetData);
        }
        
        return { blocked, actualDamage };
    }
    
    /**
     * Get card effect damage modifiers
     */
    getCardEffectDamageModifier(effect, attacker, damageType) {
        switch (effect) {
            case "double_damage":
                return {
                    name: "Double Damage",
                    value: "+100%",
                    type: "card_effect"
                };
            case "piercing":
                return {
                    name: "Piercing",
                    value: "Ignores block",
                    type: "card_effect"
                };
            case "elemental_mastery":
                if (damageType !== this.damageTypes.PHYSICAL) {
                    return {
                        name: "Elemental Mastery",
                        value: "+2",
                        type: "card_effect"
                    };
                }
                break;
            case "critical_hit":
                return {
                    name: "Critical Hit",
                    value: "+50%",
                    type: "card_effect"
                };
        }
        
        return { name: "", value: 0, type: "" };
    }
    
    /**
     * Calculate healing with modifiers
     */
    calculateHealing(healer, target, baseHealing, healingType = "normal") {
        let finalHealing = baseHealing;
        const calculation = {
            baseHealing: baseHealing,
            modifiers: [],
            finalHealing: 0,
            overheal: 0
        };
        
        // Apply healer modifiers
        const healerData = this.playerDataManager.getPlayerData(healer);
        if (healerData) {
            const healingPower = healerData.statusEffects.get("healing_power");
            if (healingPower) {
                const bonus = Math.floor(baseHealing * healingPower.amount / 100);
                finalHealing += bonus;
                calculation.modifiers.push({
                    name: "Healing Power",
                    value: `+${bonus}`,
                    type: "buff"
                });
            }
        }
        
        // Apply target modifiers
        const targetData = this.playerDataManager.getPlayerData(target);
        if (targetData) {
            const healingReduction = targetData.statusEffects.get("healing_reduction");
            if (healingReduction) {
                const reduction = Math.floor(finalHealing * healingReduction.amount / 100);
                finalHealing -= reduction;
                calculation.modifiers.push({
                    name: "Healing Reduction",
                    value: `-${reduction}`,
                    type: "debuff"
                });
            }
        }
        
        // Calculate overheal
        const health = target.getComponent("health");
        if (health) {
            const maxPossibleHealing = health.defaultValue - health.currentValue;
            calculation.overheal = Math.max(0, finalHealing - maxPossibleHealing);
            finalHealing = Math.min(finalHealing, maxPossibleHealing);
        }
        
        calculation.finalHealing = Math.max(0, finalHealing);
        
        return calculation;
    }
    
    /**
     * Apply damage with full calculation and effects
     */
    applyCalculatedDamage(attacker, target, baseDamage, damageType = this.damageTypes.PHYSICAL, cardEffects = []) {
        const calculation = this.calculateDamage(attacker, target, baseDamage, damageType, cardEffects);
        
        // Apply the actual damage
        const health = target.getComponent("health");
        if (health && calculation.actualDamage > 0) {
            health.setCurrentValue(Math.max(0, health.currentValue - calculation.actualDamage));
        }
        
        // Show damage calculation to attacker if it's a player
        if (attacker.typeId === "minecraft:player") {
            this.showDamageCalculation(attacker, calculation);
        }
        
        // Apply damage type effects
        this.applyDamageTypeEffects(target, damageType, calculation.actualDamage);
        
        return calculation;
    }
    
    /**
     * Apply damage type specific effects
     */
    applyDamageTypeEffects(target, damageType, damage) {
        const targetData = this.playerDataManager.getPlayerData(target);
        if (!targetData) return;
        
        switch (damageType) {
            case this.damageTypes.FIRE:
                // Chance to apply burn
                if (Math.random() < 0.3) {
                    targetData.statusEffects.set("burn", {
                        duration: 3,
                        damage: Math.ceil(damage * 0.2)
                    });
                }
                break;
                
            case this.damageTypes.ICE:
                // Chance to apply slow
                if (Math.random() < 0.4) {
                    targetData.statusEffects.set("slow", {
                        duration: 2,
                        amount: 1
                    });
                }
                break;
                
            case this.damageTypes.LIGHTNING:
                // Chance to apply stun
                if (Math.random() < 0.2) {
                    targetData.statusEffects.set("stun", {
                        duration: 1
                    });
                }
                break;
                
            case this.damageTypes.POISON:
                // Apply poison
                const existingPoison = targetData.statusEffects.get("poison");
                const poisonDamage = Math.ceil(damage * 0.3);
                if (existingPoison) {
                    existingPoison.duration = Math.max(existingPoison.duration, 4);
                    existingPoison.damage = Math.max(existingPoison.damage, poisonDamage);
                } else {
                    targetData.statusEffects.set("poison", {
                        duration: 4,
                        damage: poisonDamage
                    });
                }
                break;
        }
        
        this.playerDataManager.updatePlayerData(target, targetData);
    }
    
    /**
     * Show damage calculation to player
     */
    showDamageCalculation(player, calculation) {
        const lines = [`§7Damage: ${calculation.baseDamage}`];
        
        for (const modifier of calculation.modifiers) {
            const color = modifier.type === "buff" ? "§a" : modifier.type === "debuff" ? "§c" : "§7";
            lines.push(`${color}${modifier.name}: ${modifier.value}`);
        }
        
        if (calculation.blocked > 0) {
            lines.push(`§9Blocked: ${calculation.blocked}`);
        }
        
        lines.push(`§6Final: ${calculation.actualDamage} damage`);
        
        player.sendMessage(lines.join("\n"));
    }
}
