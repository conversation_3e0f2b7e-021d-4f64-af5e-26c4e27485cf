/**
 * DungeonGenerator.js
 * Procedural generation system for dungeon encounters, enemies, and layouts
 */

export class DungeonGenerator {
    constructor() {
        // Enemy templates for different difficulty tiers
        this.enemyTemplates = {
            basic: {
                skeleton: {
                    name: "Skeleton Warrior",
                    baseHealth: 25,
                    baseDamage: 8,
                    abilities: ["bone_throw"],
                    cardTypes: ["attack", "defense"],
                    description: "Undead warrior with basic combat skills"
                },
                zombie: {
                    name: "Shambling Zombie",
                    baseHealth: 35,
                    baseDamage: 6,
                    abilities: ["infectious_bite"],
                    cardTypes: ["attack", "poison"],
                    description: "Slow but resilient undead creature"
                },
                spider: {
                    name: "Giant Spider",
                    baseHealth: 20,
                    baseDamage: 10,
                    abilities: ["web_trap", "poison_bite"],
                    cardTypes: ["attack", "trap"],
                    description: "Fast arachnid with poison attacks"
                },
                goblin: {
                    name: "Goblin Raider",
                    baseHealth: 18,
                    baseDamage: 12,
                    abilities: ["quick_strike"],
                    cardTypes: ["attack", "speed"],
                    description: "Agile humanoid with quick attacks"
                }
            },
            elite: {
                lich: {
                    name: "Ancient Lich",
                    baseHealth: 80,
                    baseDamage: 15,
                    abilities: ["death_magic", "soul_drain", "bone_armor"],
                    cardTypes: ["magic", "necromancy", "defense"],
                    description: "Powerful undead spellcaster"
                },
                dragon: {
                    name: "Young Dragon",
                    baseHealth: 120,
                    baseDamage: 20,
                    abilities: ["fire_breath", "wing_buffet", "intimidate"],
                    cardTypes: ["fire", "attack", "fear"],
                    description: "Mighty draconic beast"
                },
                golem: {
                    name: "Stone Golem",
                    baseHealth: 100,
                    baseDamage: 18,
                    abilities: ["rock_throw", "stone_skin", "earthquake"],
                    cardTypes: ["earth", "defense", "area"],
                    description: "Animated stone construct"
                },
                wraith: {
                    name: "Shadow Wraith",
                    baseHealth: 60,
                    baseDamage: 22,
                    abilities: ["phase_shift", "life_drain", "shadow_strike"],
                    cardTypes: ["shadow", "drain", "stealth"],
                    description: "Ethereal undead assassin"
                }
            },
            boss: {
                demon_lord: {
                    name: "Demon Lord Bael",
                    baseHealth: 200,
                    baseDamage: 25,
                    abilities: ["hellfire", "demon_summon", "dark_pact", "infernal_rage"],
                    cardTypes: ["fire", "summon", "dark", "rage"],
                    description: "Powerful demon from the depths of hell",
                    phases: ["normal", "enraged", "desperate"]
                },
                ancient_dragon: {
                    name: "Ancient Red Dragon",
                    baseHealth: 250,
                    baseDamage: 30,
                    abilities: ["inferno_breath", "tail_sweep", "dragon_roar", "molten_scales"],
                    cardTypes: ["fire", "area", "fear", "defense"],
                    description: "Legendary dragon of immense power",
                    phases: ["flying", "grounded", "berserk"]
                },
                void_entity: {
                    name: "Void Harbinger",
                    baseHealth: 180,
                    baseDamage: 28,
                    abilities: ["void_blast", "reality_tear", "time_distortion", "void_shield"],
                    cardTypes: ["void", "reality", "time", "shield"],
                    description: "Cosmic entity from beyond reality",
                    phases: ["material", "ethereal", "transcendent"]
                }
            }
        };
        
        // Encounter patterns for different floor types
        this.encounterPatterns = {
            normal: {
                single: { weight: 40, enemies: 1 },
                pair: { weight: 35, enemies: 2 },
                group: { weight: 20, enemies: 3 },
                swarm: { weight: 5, enemies: 4 }
            },
            elite: {
                solo_elite: { weight: 60, enemies: 1, elite: true },
                elite_with_minions: { weight: 40, enemies: 2, elite: true, minions: 1 }
            },
            boss: {
                solo_boss: { weight: 70, enemies: 1, boss: true },
                boss_with_guards: { weight: 30, enemies: 2, boss: true, guards: 1 }
            }
        };
        
        // Environmental modifiers
        this.environments = {
            dungeon: {
                name: "Dark Dungeon",
                modifiers: { visibility: -1 },
                description: "Dimly lit stone corridors"
            },
            crypt: {
                name: "Ancient Crypt",
                modifiers: { undead_power: 1, holy_weakness: 1 },
                description: "Consecrated burial ground"
            },
            volcano: {
                name: "Volcanic Chamber",
                modifiers: { fire_damage: 1, ice_weakness: 1 },
                description: "Scorching hot lava flows"
            },
            forest: {
                name: "Twisted Forest",
                modifiers: { nature_power: 1, poison_resistance: -1 },
                description: "Corrupted woodland"
            },
            void: {
                name: "Void Realm",
                modifiers: { magic_chaos: 2, reality_unstable: 1 },
                description: "Space between dimensions"
            }
        };
        
        console.log("🎲 DungeonGenerator initialized");
    }
    
    /**
     * Generate a complete encounter for a floor
     */
    generateEncounter(floor, difficulty, encounterType = "normal") {
        const pattern = this.selectEncounterPattern(encounterType);
        const environment = this.selectEnvironment(floor);
        const enemies = this.generateEnemies(pattern, difficulty, encounterType);
        
        return {
            floor: floor,
            type: encounterType,
            pattern: pattern,
            environment: environment,
            enemies: enemies,
            difficulty: difficulty,
            specialRules: this.generateSpecialRules(floor, environment)
        };
    }
    
    /**
     * Select encounter pattern based on type
     */
    selectEncounterPattern(encounterType) {
        const patterns = this.encounterPatterns[encounterType] || this.encounterPatterns.normal;
        return this.weightedRandomSelect(patterns);
    }
    
    /**
     * Select environment for the floor
     */
    selectEnvironment(floor) {
        // Different environments become available at different floors
        const availableEnvironments = Object.keys(this.environments).filter(env => {
            switch (env) {
                case "dungeon": return true; // Always available
                case "crypt": return floor >= 3;
                case "volcano": return floor >= 5;
                case "forest": return floor >= 7;
                case "void": return floor >= 10;
                default: return true;
            }
        });
        
        const envKey = availableEnvironments[Math.floor(Math.random() * availableEnvironments.length)];
        return { ...this.environments[envKey], key: envKey };
    }
    
    /**
     * Generate enemies based on pattern and difficulty
     */
    generateEnemies(pattern, difficulty, encounterType) {
        const enemies = [];
        const enemyCount = pattern.enemies || 1;
        
        // Determine enemy tier based on encounter type
        let enemyTier = "basic";
        if (encounterType === "elite" || pattern.elite) {
            enemyTier = "elite";
        } else if (encounterType === "boss" || pattern.boss) {
            enemyTier = "boss";
        }
        
        // Generate primary enemies
        for (let i = 0; i < enemyCount; i++) {
            const isMainEnemy = i === 0;
            const currentTier = isMainEnemy ? enemyTier : "basic";
            
            const enemy = this.createEnemy(currentTier, difficulty, isMainEnemy);
            enemies.push(enemy);
        }
        
        return enemies;
    }
    
    /**
     * Create a single enemy
     */
    createEnemy(tier, difficulty, isMainEnemy = false) {
        const templates = this.enemyTemplates[tier];
        const templateKeys = Object.keys(templates);
        const templateKey = templateKeys[Math.floor(Math.random() * templateKeys.length)];
        const template = templates[templateKey];
        
        // Scale stats based on difficulty
        const scaledHealth = Math.floor(template.baseHealth * (1 + difficulty * 0.2));
        const scaledDamage = Math.floor(template.baseDamage * (1 + difficulty * 0.15));
        
        const enemy = {
            id: `${templateKey}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: template.name,
            tier: tier,
            health: scaledHealth,
            maxHealth: scaledHealth,
            damage: scaledDamage,
            abilities: [...template.abilities],
            cardTypes: [...template.cardTypes],
            description: template.description,
            isMainEnemy: isMainEnemy,
            
            // Add random variations
            variations: this.generateEnemyVariations(template, difficulty),
            
            // Boss-specific properties
            ...(tier === "boss" && template.phases ? { 
                phases: template.phases,
                currentPhase: 0,
                phaseThresholds: this.calculatePhaseThresholds(scaledHealth)
            } : {})
        };
        
        return enemy;
    }
    
    /**
     * Generate random variations for enemies
     */
    generateEnemyVariations(template, difficulty) {
        const variations = [];
        
        // Random chance for variations based on difficulty
        const variationChance = Math.min(0.3 + (difficulty * 0.05), 0.8);
        
        if (Math.random() < variationChance) {
            const possibleVariations = [
                { name: "Enraged", healthMod: 1.2, damageMod: 1.3, description: "Filled with fury" },
                { name: "Armored", healthMod: 1.4, damageMod: 0.9, description: "Protected by heavy armor" },
                { name: "Swift", healthMod: 0.8, damageMod: 1.1, description: "Moves with incredible speed" },
                { name: "Poisonous", healthMod: 1.0, damageMod: 1.0, description: "Attacks inflict poison" },
                { name: "Regenerating", healthMod: 1.1, damageMod: 1.0, description: "Slowly heals over time" },
                { name: "Berserker", healthMod: 0.9, damageMod: 1.4, description: "Deals more damage when wounded" }
            ];
            
            const variation = possibleVariations[Math.floor(Math.random() * possibleVariations.length)];
            variations.push(variation);
        }
        
        return variations;
    }
    
    /**
     * Calculate phase thresholds for boss enemies
     */
    calculatePhaseThresholds(maxHealth) {
        return [
            Math.floor(maxHealth * 0.66), // Phase 1 -> 2 at 66% health
            Math.floor(maxHealth * 0.33)  // Phase 2 -> 3 at 33% health
        ];
    }
    
    /**
     * Generate special rules for the encounter
     */
    generateSpecialRules(floor, environment) {
        const rules = [];
        
        // Environment-based rules
        if (environment.modifiers) {
            Object.entries(environment.modifiers).forEach(([modifier, value]) => {
                switch (modifier) {
                    case "fire_damage":
                        rules.push("Fire attacks deal +50% damage");
                        break;
                    case "undead_power":
                        rules.push("Undead enemies have +25% health");
                        break;
                    case "magic_chaos":
                        rules.push("Spell effects are unpredictable");
                        break;
                    case "visibility":
                        rules.push("Reduced visibility affects accuracy");
                        break;
                }
            });
        }
        
        // Floor-based special rules
        if (floor >= 5 && Math.random() < 0.3) {
            const floorRules = [
                "Enemies gain energy faster",
                "All damage is increased by 25%",
                "Healing effects are reduced",
                "Card costs are increased by 1",
                "Status effects last longer"
            ];
            rules.push(floorRules[Math.floor(Math.random() * floorRules.length)]);
        }
        
        return rules;
    }
    
    /**
     * Generate loot for defeated enemies
     */
    generateLoot(enemy, difficulty, runType) {
        const loot = {
            gold: 0,
            cards: [],
            artifacts: [],
            upgrades: []
        };
        
        // Base gold based on enemy tier and difficulty
        const baseGold = {
            basic: 10,
            elite: 25,
            boss: 50
        };
        
        loot.gold = Math.floor((baseGold[enemy.tier] || 10) * (1 + difficulty * 0.1));
        
        // Card rewards
        const cardChance = {
            basic: 0.6,
            elite: 0.9,
            boss: 1.0
        };
        
        if (Math.random() < (cardChance[enemy.tier] || 0.6)) {
            loot.cards.push(this.generateRandomCard(enemy, difficulty));
        }
        
        // Artifact chance (rare)
        if (enemy.tier === "boss" || (enemy.tier === "elite" && Math.random() < 0.2)) {
            loot.artifacts.push(this.generateRandomArtifact(difficulty));
        }
        
        // Upgrade materials
        if (Math.random() < 0.4) {
            loot.upgrades.push("Card Upgrade Token");
        }
        
        return loot;
    }
    
    /**
     * Generate a random card based on enemy type
     */
    generateRandomCard(enemy, difficulty) {
        const cardTypes = enemy.cardTypes || ["attack"];
        const cardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
        
        return {
            name: `${enemy.name} ${cardType.charAt(0).toUpperCase() + cardType.slice(1)}`,
            type: cardType,
            cost: Math.min(1 + Math.floor(difficulty / 3), 5),
            rarity: this.determineCardRarity(enemy.tier),
            description: `A ${cardType} card inspired by the ${enemy.name}`,
            temporary: true // Only exists during the run
        };
    }
    
    /**
     * Generate a random artifact
     */
    generateRandomArtifact(difficulty) {
        const artifacts = [
            { name: "Ring of Power", effect: "+1 Energy per turn" },
            { name: "Amulet of Protection", effect: "Reduce all damage by 1" },
            { name: "Boots of Speed", effect: "Draw +1 card per turn" },
            { name: "Crown of Wisdom", effect: "All cards cost 1 less energy" },
            { name: "Cloak of Shadows", effect: "First attack each turn misses" }
        ];
        
        return artifacts[Math.floor(Math.random() * artifacts.length)];
    }
    
    /**
     * Determine card rarity based on enemy tier
     */
    determineCardRarity(tier) {
        switch (tier) {
            case "basic": return Math.random() < 0.8 ? "common" : "uncommon";
            case "elite": return Math.random() < 0.5 ? "uncommon" : "rare";
            case "boss": return Math.random() < 0.3 ? "rare" : "legendary";
            default: return "common";
        }
    }
    
    /**
     * Weighted random selection helper
     */
    weightedRandomSelect(options) {
        const totalWeight = Object.values(options).reduce((sum, option) => sum + option.weight, 0);
        let random = Math.random() * totalWeight;
        
        for (const [key, option] of Object.entries(options)) {
            random -= option.weight;
            if (random <= 0) {
                return { ...option, key };
            }
        }
        
        // Fallback to first option
        const firstKey = Object.keys(options)[0];
        return { ...options[firstKey], key: firstKey };
    }
}
