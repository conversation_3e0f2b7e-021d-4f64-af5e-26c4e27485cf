/**
 * CardEffects - Handles special card effects and abilities
 */

export class CardEffects {
    constructor(combat<PERSON>anager, playerDataManager) {
        this.combatManager = combatManager;
        this.playerDataManager = playerDataManager;
        
        // Register all effect handlers
        this.effectHandlers = new Map();
        this.registerEffectHandlers();
    }
    
    /**
     * Register all effect handlers
     */
    registerEffectHandlers() {
        // Damage effects
        this.effectHandlers.set("fire_damage", this.fireEffect.bind(this));
        this.effectHandlers.set("poison_damage", this.poisonEffect.bind(this));
        this.effectHandlers.set("area_damage", this.areaEffect.bind(this));
        this.effectHandlers.set("piercing_damage", this.piercingEffect.bind(this));
        
        // Healing effects
        this.effectHandlers.set("regeneration", this.regenerationEffect.bind(this));
        this.effectHandlers.set("life_steal", this.lifeStealEffect.bind(this));
        
        // Buff effects
        this.effectHandlers.set("strength_buff", this.strengthEffect.bind(this));
        this.effectHandlers.set("defense_buff", this.defenseEffect.bind(this));
        this.effectHandlers.set("speed_buff", this.speedEffect.bind(this));
        this.effectHandlers.set("energy_boost", this.energyBoostEffect.bind(this));
        
        // Debuff effects
        this.effectHandlers.set("weakness", this.weaknessEffect.bind(this));
        this.effectHandlers.set("vulnerability", this.vulnerabilityEffect.bind(this));
        this.effectHandlers.set("slow", this.slowEffect.bind(this));
        this.effectHandlers.set("silence", this.silenceEffect.bind(this));
        
        // Utility effects
        this.effectHandlers.set("draw_card", this.drawCardEffect.bind(this));
        this.effectHandlers.set("discard_card", this.discardCardEffect.bind(this));
        this.effectHandlers.set("extra_turn", this.extraTurnEffect.bind(this));
        this.effectHandlers.set("skip_turn", this.skipTurnEffect.bind(this));
        
        // Special effects
        this.effectHandlers.set("heal_and_block", this.healAndBlockEffect.bind(this));
        this.effectHandlers.set("damage_and_heal", this.damageAndHealEffect.bind(this));
        this.effectHandlers.set("copy_card", this.copyCardEffect.bind(this));
        this.effectHandlers.set("transform_card", this.transformCardEffect.bind(this));
    }
    
    /**
     * Apply an effect
     */
    async applyEffect(effectName, context) {
        const handler = this.effectHandlers.get(effectName);
        if (handler) {
            try {
                await handler(context);
            } catch (error) {
                console.error(`Error applying effect ${effectName}:`, error);
            }
        } else {
            console.warn(`Unknown effect: ${effectName}`);
        }
    }
    
    /**
     * Damage Effects
     */
    
    async fireEffect(context) {
        const { target, damage, source } = context;
        if (!target || !damage) return;
        
        // Apply fire damage
        this.dealDamage(target, damage, source);
        
        // Add burn status effect
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("burn", {
                duration: 3,
                damage: Math.ceil(damage * 0.3)
            });
        }
        
        // Visual effects
        target.dimension.spawnParticle("minecraft:lava_particle", target.location);
        
        if (source && source.typeId === "minecraft:player") {
            source.sendMessage(`§cDealt ${damage} fire damage!`);
        }
    }
    
    async poisonEffect(context) {
        const { target, damage, source } = context;
        if (!target || !damage) return;
        
        // Apply initial damage
        this.dealDamage(target, Math.ceil(damage * 0.5), source);
        
        // Add poison status effect
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("poison", {
                duration: 4,
                damage: Math.ceil(damage * 0.25)
            });
        }
        
        // Visual effects
        target.dimension.spawnParticle("minecraft:villager_angry", target.location);
    }
    
    async areaEffect(context) {
        const { combatSession, damage, source } = context;
        if (!combatSession || !damage) return;
        
        // Damage all enemies
        const enemies = combatSession.participants.filter(p => p !== source && p.isValid());
        for (const enemy of enemies) {
            this.dealDamage(enemy, damage, source);
            enemy.dimension.spawnParticle("minecraft:explosion_particle", enemy.location);
        }
        
        if (source && source.typeId === "minecraft:player") {
            source.sendMessage(`§cDealt ${damage} area damage to ${enemies.length} enemies!`);
        }
    }
    
    async piercingEffect(context) {
        const { target, damage, source } = context;
        if (!target || !damage) return;
        
        // Piercing damage ignores block
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            const originalBlock = playerData.block;
            playerData.block = 0; // Temporarily remove block
            this.dealDamage(target, damage, source);
            playerData.block = originalBlock; // Restore block
        } else {
            this.dealDamage(target, damage, source);
        }
        
        if (source && source.typeId === "minecraft:player") {
            source.sendMessage(`§cDealt ${damage} piercing damage!`);
        }
    }
    
    /**
     * Healing Effects
     */
    
    async regenerationEffect(context) {
        const { target, healing, duration = 3 } = context;
        if (!target || !healing) return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("regeneration", {
                duration: duration,
                healing: healing
            });
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§aGained regeneration for ${duration} turns!`);
        }
    }
    
    async lifeStealEffect(context) {
        const { target, damage, source } = context;
        if (!target || !damage || !source) return;
        
        // Deal damage
        this.dealDamage(target, damage, source);
        
        // Heal source for portion of damage
        const healAmount = Math.ceil(damage * 0.5);
        this.healEntity(source, healAmount);
        
        if (source.typeId === "minecraft:player") {
            source.sendMessage(`§aDealt ${damage} damage and healed ${healAmount} health!`);
        }
    }
    
    /**
     * Buff Effects
     */
    
    async strengthEffect(context) {
        const { target, duration = 3, amount = 3 } = context;
        if (!target) return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("strength", {
                duration: duration,
                amount: amount
            });
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§cGained strength! Next ${duration} attacks deal +${amount} damage.`);
        }
    }
    
    async defenseEffect(context) {
        const { target, duration = 3, amount = 2 } = context;
        if (!target) return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("defense", {
                duration: duration,
                amount: amount
            });
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§9Gained defense! Reduce incoming damage by ${amount} for ${duration} turns.`);
        }
    }
    
    async energyBoostEffect(context) {
        const { target, amount = 2 } = context;
        if (!target) return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.currentEnergy = Math.min(playerData.maxEnergy, playerData.currentEnergy + amount);
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§eGained ${amount} energy!`);
        }
    }
    
    /**
     * Debuff Effects
     */
    
    async weaknessEffect(context) {
        const { target, duration = 3, amount = 2 } = context;
        if (!target) return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("weakness", {
                duration: duration,
                amount: amount
            });
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§7Weakened! Damage reduced by ${amount} for ${duration} turns.`);
        }
    }
    
    async vulnerabilityEffect(context) {
        const { target, duration = 3, amount = 2 } = context;
        if (!target) return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.statusEffects.set("vulnerability", {
                duration: duration,
                amount: amount
            });
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§cVulnerable! Take +${amount} damage for ${duration} turns.`);
        }
    }
    
    /**
     * Utility Effects
     */
    
    async drawCardEffect(context) {
        const { target, amount = 1 } = context;
        if (!target || target.typeId !== "minecraft:player") return;
        
        for (let i = 0; i < amount; i++) {
            const drawnCard = this.playerDataManager.drawCard(target);
            if (!drawnCard) break;
        }
        
        target.sendMessage(`§aDrew ${amount} card${amount > 1 ? 's' : ''}!`);
    }
    
    async discardCardEffect(context) {
        const { target, amount = 1 } = context;
        if (!target || target.typeId !== "minecraft:player") return;
        
        const playerData = this.playerDataManager.getPlayerData(target);
        if (!playerData) return;
        
        const discarded = Math.min(amount, playerData.currentHand.length);
        playerData.currentHand.splice(0, discarded);
        
        target.sendMessage(`§7Discarded ${discarded} card${discarded > 1 ? 's' : ''}!`);
    }
    
    async extraTurnEffect(context) {
        const { combatSession, source } = context;
        if (!combatSession || !source) return;
        
        // Mark for extra turn (combat manager will handle this)
        combatSession.extraTurn = source;
        
        if (source.typeId === "minecraft:player") {
            source.sendMessage("§6You get an extra turn!");
        }
    }
    
    /**
     * Special Effects
     */
    
    async healAndBlockEffect(context) {
        const { target, healing = 8, block = 5 } = context;
        if (!target) return;
        
        // Heal
        this.healEntity(target, healing);
        
        // Add block
        const playerData = this.playerDataManager.getPlayerData(target);
        if (playerData) {
            playerData.block += block;
        }
        
        if (target.typeId === "minecraft:player") {
            target.sendMessage(`§aHealed ${healing} health and gained ${block} block!`);
        }
    }
    
    async damageAndHealEffect(context) {
        const { target, source, damage = 5, healing = 3 } = context;
        if (!target || !source) return;
        
        // Deal damage to target
        this.dealDamage(target, damage, source);
        
        // Heal source
        this.healEntity(source, healing);
        
        if (source.typeId === "minecraft:player") {
            source.sendMessage(`§cDealt ${damage} damage and §ahealed ${healing} health!`);
        }
    }
    
    /**
     * Process status effects at turn start/end
     */
    processStatusEffects(entity, timing = "start") {
        const playerData = this.playerDataManager.getPlayerData(entity);
        if (!playerData) return;
        
        const effectsToRemove = [];
        
        for (const [effectName, effectData] of playerData.statusEffects) {
            // Apply effect
            switch (effectName) {
                case "burn":
                case "poison":
                    if (timing === "start") {
                        this.dealDamage(entity, effectData.damage);
                        if (entity.typeId === "minecraft:player") {
                            entity.sendMessage(`§c${effectName} deals ${effectData.damage} damage!`);
                        }
                    }
                    break;
                    
                case "regeneration":
                    if (timing === "start") {
                        this.healEntity(entity, effectData.healing);
                        if (entity.typeId === "minecraft:player") {
                            entity.sendMessage(`§aRegeneration heals ${effectData.healing} health!`);
                        }
                    }
                    break;
            }
            
            // Reduce duration
            effectData.duration--;
            if (effectData.duration <= 0) {
                effectsToRemove.push(effectName);
            }
        }
        
        // Remove expired effects
        for (const effectName of effectsToRemove) {
            playerData.statusEffects.delete(effectName);
            if (entity.typeId === "minecraft:player") {
                entity.sendMessage(`§7${effectName} effect expired.`);
            }
        }
    }
    
    /**
     * Utility methods
     */
    
    dealDamage(target, damage, source = null) {
        const playerData = this.playerDataManager.getPlayerData(target);
        let finalDamage = damage;
        
        // Apply status effect modifiers
        if (playerData) {
            // Apply vulnerability
            const vulnerability = playerData.statusEffects.get("vulnerability");
            if (vulnerability) {
                finalDamage += vulnerability.amount;
            }
            
            // Apply defense
            const defense = playerData.statusEffects.get("defense");
            if (defense) {
                finalDamage = Math.max(1, finalDamage - defense.amount);
            }
            
            // Apply block
            if (playerData.block > 0) {
                const blockedDamage = Math.min(playerData.block, finalDamage);
                playerData.block -= blockedDamage;
                finalDamage -= blockedDamage;
                
                if (target.typeId === "minecraft:player") {
                    target.sendMessage(`§9Blocked ${blockedDamage} damage!`);
                }
            }
        }
        
        // Apply final damage
        if (finalDamage > 0) {
            const health = target.getComponent("health");
            if (health) {
                health.setCurrentValue(Math.max(0, health.currentValue - finalDamage));
            }
        }
    }
    
    healEntity(entity, amount) {
        const health = entity.getComponent("health");
        if (health) {
            const newHealth = Math.min(health.defaultValue, health.currentValue + amount);
            health.setCurrentValue(newHealth);
            
            // Visual effects
            entity.dimension.spawnParticle("minecraft:heart_particle", entity.location);
        }
    }
    
    /**
     * Get damage modifier from status effects
     */
    getDamageModifier(entity) {
        const playerData = this.playerDataManager.getPlayerData(entity);
        if (!playerData) return 0;
        
        let modifier = 0;
        
        // Strength buff
        const strength = playerData.statusEffects.get("strength");
        if (strength) {
            modifier += strength.amount;
        }
        
        // Weakness debuff
        const weakness = playerData.statusEffects.get("weakness");
        if (weakness) {
            modifier -= weakness.amount;
        }
        
        return modifier;
    }
}
