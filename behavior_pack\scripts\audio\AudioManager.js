/**
 * AudioManager - Handles all audio playback for the card combat system
 */

import { world, system } from "@minecraft/server";

export class AudioManager {
    constructor() {
        this.soundSettings = {
            masterVolume: 1.0,
            combatVolume: 1.0,
            uiVolume: 1.0,
            musicVolume: 1.0,
            enabled: true
        };
        
        // Sound variation tracking to prevent repetition
        this.lastPlayedSounds = new Map();
        this.soundCooldowns = new Map();
        
        // Audio categories for volume control
        this.soundCategories = {
            combat: ['card_play', 'card_draw', 'combat_start', 'combat_end', 'turn_start', 
                    'attack_hit', 'block_activate', 'spell_cast', 'heal'],
            ui: ['ui_hover', 'ui_click', 'pack_open', 'rare_card', 'legendary_card', 
                 'deck_shuffle', 'energy_gain', 'status_effect'],
            ambient: ['ambient_combat', 'ambient_menu']
        };
        
        // Sound definitions matching sound_definitions.json
        this.soundPaths = {
            // Card sounds
            card_play: ['combat.card_play1', 'combat.card_play2', 'combat.card_play3'],
            card_draw: ['combat.card_draw'],
            
            // Combat state sounds
            combat_start: ['combat.combat_start'],
            combat_end: ['combat.combat_end'],
            turn_start: ['combat.turn_start'],
            
            // Action sounds
            attack_hit: ['combat.attack_hit1', 'combat.attack_hit2', 'combat.attack_hit3'],
            block_activate: ['combat.block_activate'],
            spell_cast: ['combat.spell_cast1', 'combat.spell_cast2'],
            heal: ['combat.heal'],
            
            // UI sounds
            ui_hover: ['combat.ui_hover'],
            ui_click: ['combat.ui_click'],
            pack_open: ['combat.pack_open'],
            rare_card: ['combat.rare_card'],
            legendary_card: ['combat.legendary_card'],
            deck_shuffle: ['combat.deck_shuffle'],
            energy_gain: ['combat.energy_gain'],
            status_effect: ['combat.status_effect']
        };
    }
    
    /**
     * Play a sound effect
     */
    playSound(soundKey, player, options = {}) {
        if (!this.soundSettings.enabled) return;
        
        try {
            const soundPaths = this.soundPaths[soundKey];
            if (!soundPaths || soundPaths.length === 0) {
                console.warn(`Sound key not found: ${soundKey}`);
                return;
            }
            
            // Check cooldown
            if (this.isOnCooldown(soundKey, player)) {
                return;
            }
            
            // Select sound variant
            const soundPath = this.selectSoundVariant(soundKey, soundPaths);
            
            // Calculate volume
            const volume = this.calculateVolume(soundKey, options.volume);
            
            // Calculate pitch
            const pitch = options.pitch || 1.0;
            
            // Play the sound
            this.executeSound(player, soundPath, volume, pitch, options);
            
            // Set cooldown
            this.setCooldown(soundKey, player, options.cooldown || 50); // 50ms default
            
        } catch (error) {
            console.error(`Error playing sound ${soundKey}:`, error);
        }
    }
    
    /**
     * Play sound for all players in combat
     */
    playSoundForCombat(soundKey, combatSession, options = {}) {
        if (!combatSession || !combatSession.participants) return;
        
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                this.playSound(soundKey, participant, options);
            }
        }
    }
    
    /**
     * Play positional sound at location
     */
    playPositionalSound(soundKey, location, dimension, options = {}) {
        if (!this.soundSettings.enabled) return;
        
        try {
            const soundPaths = this.soundPaths[soundKey];
            if (!soundPaths || soundPaths.length === 0) return;
            
            const soundPath = this.selectSoundVariant(soundKey, soundPaths);
            const volume = this.calculateVolume(soundKey, options.volume);
            
            // Play for all nearby players
            const nearbyPlayers = dimension.getPlayers({
                location: location,
                maxDistance: options.maxDistance || 32
            });
            
            for (const player of nearbyPlayers) {
                this.executeSound(player, soundPath, volume, options.pitch || 1.0, {
                    ...options,
                    location: location
                });
            }
            
        } catch (error) {
            console.error(`Error playing positional sound ${soundKey}:`, error);
        }
    }
    
    /**
     * Select sound variant to prevent repetition
     */
    selectSoundVariant(soundKey, soundPaths) {
        if (soundPaths.length === 1) {
            return soundPaths[0];
        }
        
        // Get last played variant
        const lastPlayed = this.lastPlayedSounds.get(soundKey) || -1;
        
        // Select different variant if possible
        let selectedIndex;
        if (soundPaths.length > 1) {
            do {
                selectedIndex = Math.floor(Math.random() * soundPaths.length);
            } while (selectedIndex === lastPlayed && soundPaths.length > 1);
        } else {
            selectedIndex = 0;
        }
        
        // Remember this selection
        this.lastPlayedSounds.set(soundKey, selectedIndex);
        
        return soundPaths[selectedIndex];
    }
    
    /**
     * Calculate final volume
     */
    calculateVolume(soundKey, baseVolume = 1.0) {
        let categoryVolume = 1.0;
        
        // Determine category volume
        for (const [category, sounds] of Object.entries(this.soundCategories)) {
            if (sounds.some(sound => soundKey.startsWith(sound))) {
                categoryVolume = this.soundSettings[category + 'Volume'] || 1.0;
                break;
            }
        }
        
        return baseVolume * categoryVolume * this.soundSettings.masterVolume;
    }
    
    /**
     * Execute the actual sound command
     */
    executeSound(player, soundPath, volume, pitch, options) {
        try {
            // Build playsound command
            let command = `playsound ${soundPath} @s`;
            
            // Add position if specified
            if (options.location) {
                const loc = options.location;
                command += ` ${loc.x} ${loc.y} ${loc.z}`;
            }
            
            // Add volume and pitch
            command += ` ${volume} ${pitch}`;
            
            // Execute command
            player.runCommand(command);
            
        } catch (error) {
            console.error(`Error executing sound command:`, error);
        }
    }
    
    /**
     * Check if sound is on cooldown
     */
    isOnCooldown(soundKey, player) {
        const cooldownKey = `${soundKey}_${player.id}`;
        const cooldownEnd = this.soundCooldowns.get(cooldownKey);
        
        if (cooldownEnd && Date.now() < cooldownEnd) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Set sound cooldown
     */
    setCooldown(soundKey, player, cooldownMs) {
        const cooldownKey = `${soundKey}_${player.id}`;
        this.soundCooldowns.set(cooldownKey, Date.now() + cooldownMs);
    }
    
    /**
     * Combat-specific sound methods
     */
    
    playCardPlay(player, cardType = null) {
        // Add slight variation based on card type
        const options = {};
        if (cardType === 'legendary') {
            options.pitch = 1.1;
            options.volume = 1.2;
        } else if (cardType === 'rare') {
            options.pitch = 1.05;
        }
        
        this.playSound('card_play', player, options);
    }
    
    playCardDraw(player, cardCount = 1) {
        // Slight pitch variation for multiple cards
        const options = {
            pitch: 1.0 + (cardCount - 1) * 0.05
        };
        
        this.playSound('card_draw', player, options);
    }
    
    playAttackHit(player, damage = 0) {
        // Volume and pitch based on damage
        const options = {
            volume: Math.min(1.5, 0.8 + damage * 0.1),
            pitch: Math.min(1.2, 0.9 + damage * 0.05)
        };
        
        this.playSound('attack_hit', player, options);
    }
    
    playSpellCast(player, spellPower = 1) {
        // Pitch variation based on spell power
        const options = {
            pitch: 0.95 + spellPower * 0.1,
            volume: 0.9 + spellPower * 0.1
        };
        
        this.playSound('spell_cast', player, options);
    }
    
    playCardReveal(player, rarity) {
        switch (rarity) {
            case 'legendary':
                this.playSound('legendary_card', player);
                break;
            case 'rare':
                this.playSound('rare_card', player);
                break;
            default:
                this.playSound('pack_open', player);
        }
    }
    
    /**
     * UI sound methods
     */
    
    playUIHover(player) {
        this.playSound('ui_hover', player, { cooldown: 100 });
    }
    
    playUIClick(player) {
        this.playSound('ui_click', player, { cooldown: 150 });
    }
    
    /**
     * Settings management
     */
    
    setMasterVolume(volume) {
        this.soundSettings.masterVolume = Math.max(0, Math.min(1, volume));
    }
    
    setCombatVolume(volume) {
        this.soundSettings.combatVolume = Math.max(0, Math.min(1, volume));
    }
    
    setUIVolume(volume) {
        this.soundSettings.uiVolume = Math.max(0, Math.min(1, volume));
    }
    
    toggleSounds(enabled) {
        this.soundSettings.enabled = enabled;
    }
    
    /**
     * Get current settings
     */
    getSettings() {
        return { ...this.soundSettings };
    }
    
    /**
     * Cleanup old cooldowns
     */
    cleanup() {
        const now = Date.now();
        for (const [key, endTime] of this.soundCooldowns.entries()) {
            if (now > endTime) {
                this.soundCooldowns.delete(key);
            }
        }
    }
    
    /**
     * Test all sounds
     */
    testAllSounds(player) {
        const testSounds = [
            'card_play', 'card_draw', 'combat_start', 'combat_end', 'turn_start',
            'attack_hit', 'block_activate', 'spell_cast', 'heal',
            'ui_hover', 'ui_click', 'pack_open', 'rare_card', 'legendary_card',
            'deck_shuffle', 'energy_gain', 'status_effect'
        ];
        
        let delay = 0;
        for (const soundKey of testSounds) {
            system.runTimeout(() => {
                player.sendMessage(`§7Testing: ${soundKey}`);
                this.playSound(soundKey, player);
            }, delay);
            delay += 40; // 2 second intervals
        }
        
        player.sendMessage(`§aTesting ${testSounds.length} sounds with 2-second intervals`);
    }
}
