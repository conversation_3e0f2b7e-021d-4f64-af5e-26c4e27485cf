/**
 * ProgressionManager - Handles player progression, achievements, and rewards
 */

export class ProgressionManager {
    constructor(playerDataManager, cardManager) {
        this.playerDataManager = playerDataManager;
        this.cardManager = cardManager;
        
        // Progression milestones
        this.levelMilestones = new Map();
        this.achievements = new Map();
        this.cardUnlocks = new Map();
        
        this.initializeProgressionSystem();
    }
    
    /**
     * Initialize progression system
     */
    initializeProgressionSystem() {
        this.setupLevelMilestones();
        this.setupAchievements();
        this.setupCardUnlocks();
    }
    
    /**
     * Setup level milestones and rewards
     */
    setupLevelMilestones() {
        // Level 1-10: Basic progression
        for (let level = 1; level <= 10; level++) {
            this.levelMilestones.set(level, {
                experienceRequired: level * 100 + (level - 1) * 50,
                rewards: {
                    cardPacks: level % 3 === 0 ? 1 : 0,
                    cardFragments: level * 2,
                    maxEnergy: level <= 5 ? (level % 3 === 0 ? 1 : 0) : 0,
                    deckSlots: level % 5 === 0 ? 1 : 0
                },
                title: level <= 3 ? "Novice" : level <= 6 ? "Apprentice" : "Adept"
            });
        }
        
        // Level 11-25: Intermediate progression
        for (let level = 11; level <= 25; level++) {
            this.levelMilestones.set(level, {
                experienceRequired: level * 150 + (level - 10) * 75,
                rewards: {
                    cardPacks: level % 2 === 0 ? 1 : 0,
                    advancedCardPacks: level % 4 === 0 ? 1 : 0,
                    cardFragments: level * 3,
                    maxEnergy: level % 5 === 0 ? 1 : 0,
                    deckSlots: level % 3 === 0 ? 1 : 0
                },
                title: level <= 15 ? "Expert" : level <= 20 ? "Veteran" : "Master"
            });
        }
        
        // Level 26+: Advanced progression
        for (let level = 26; level <= 50; level++) {
            this.levelMilestones.set(level, {
                experienceRequired: level * 200 + (level - 25) * 100,
                rewards: {
                    advancedCardPacks: 1,
                    legendaryCardPacks: level % 5 === 0 ? 1 : 0,
                    cardFragments: level * 4,
                    rareCardFragments: level % 3 === 0 ? 1 : 0,
                    maxEnergy: level % 10 === 0 ? 1 : 0,
                    deckSlots: level % 2 === 0 ? 1 : 0
                },
                title: level <= 35 ? "Grandmaster" : level <= 45 ? "Legend" : "Mythic"
            });
        }
    }
    
    /**
     * Setup achievements
     */
    setupAchievements() {
        // Combat achievements
        this.achievements.set("first_victory", {
            name: "First Victory",
            description: "Win your first combat",
            condition: (stats) => stats.wins >= 1,
            rewards: { cardPacks: 1, cardFragments: 5 },
            icon: "🏆"
        });
        
        this.achievements.set("win_streak_5", {
            name: "Winning Streak",
            description: "Win 5 combats in a row",
            condition: (stats) => stats.currentWinStreak >= 5,
            rewards: { advancedCardPacks: 1, cardFragments: 10 },
            icon: "🔥"
        });
        
        this.achievements.set("total_wins_25", {
            name: "Combat Veteran",
            description: "Win 25 total combats",
            condition: (stats) => stats.wins >= 25,
            rewards: { legendaryCardPacks: 1, cardFragments: 25 },
            icon: "⚔️"
        });
        
        // Card achievements
        this.achievements.set("cards_played_100", {
            name: "Card Master",
            description: "Play 100 cards",
            condition: (stats) => stats.cardsPlayed >= 100,
            rewards: { cardFragments: 15 },
            icon: "🃏"
        });
        
        this.achievements.set("collect_all_common", {
            name: "Common Collector",
            description: "Collect all common cards",
            condition: (stats, collection) => this.hasAllCardsOfRarity(collection, "common"),
            rewards: { advancedCardPacks: 2 },
            icon: "📚"
        });
        
        this.achievements.set("collect_legendary", {
            name: "Legendary Hunter",
            description: "Collect your first legendary card",
            condition: (stats, collection) => this.hasCardOfRarity(collection, "legendary"),
            rewards: { legendaryCardPacks: 1, rareCardFragments: 3 },
            icon: "💎"
        });
        
        // Deck achievements
        this.achievements.set("deck_builder", {
            name: "Deck Builder",
            description: "Create 5 different deck configurations",
            condition: (stats) => stats.decksCreated >= 5,
            rewards: { deckSlots: 2, cardFragments: 10 },
            icon: "🏗️"
        });
        
        // Special achievements
        this.achievements.set("perfect_game", {
            name: "Flawless Victory",
            description: "Win a combat without taking damage",
            condition: (stats) => stats.flawlessVictories >= 1,
            rewards: { legendaryCardPacks: 1, cardFragments: 20 },
            icon: "✨"
        });
        
        this.achievements.set("speed_demon", {
            name: "Speed Demon",
            description: "Win a combat in under 5 turns",
            condition: (stats) => stats.fastestVictory <= 5 && stats.fastestVictory > 0,
            rewards: { advancedCardPacks: 1, cardFragments: 15 },
            icon: "⚡"
        });
    }
    
    /**
     * Setup card unlocks by level
     */
    setupCardUnlocks() {
        // Level-based card unlocks
        this.cardUnlocks.set(3, ["power_attack", "shield_wall"]);
        this.cardUnlocks.set(5, ["fireball", "heal"]);
        this.cardUnlocks.set(8, ["critical_strike", "iron_defense"]);
        this.cardUnlocks.set(12, ["lightning_bolt", "strength_potion"]);
        this.cardUnlocks.set(15, ["devastating_blow", "golden_apple"]);
        this.cardUnlocks.set(20, ["time_warp"]);
    }
    
    /**
     * Award experience to player
     */
    awardExperience(player, amount, source = "combat") {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        const oldLevel = playerData.level;
        playerData.experience += amount;
        
        // Check for level up
        const newLevel = this.calculateLevel(playerData.experience);
        if (newLevel > oldLevel) {
            this.handleLevelUp(player, oldLevel, newLevel);
        }
        
        // Update player data
        this.playerDataManager.updatePlayerData(player, playerData);
        
        // Show experience gain
        player.sendMessage(`§a+${amount} XP §7(${source})`);
    }
    
    /**
     * Calculate level from experience
     */
    calculateLevel(experience) {
        let level = 1;
        let totalRequired = 0;
        
        while (level <= 50) {
            const milestone = this.levelMilestones.get(level);
            if (!milestone) break;
            
            totalRequired += milestone.experienceRequired;
            if (experience < totalRequired) {
                return level - 1;
            }
            level++;
        }
        
        return Math.min(50, level - 1);
    }
    
    /**
     * Handle level up
     */
    handleLevelUp(player, oldLevel, newLevel) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        playerData.level = newLevel;
        
        // Award level up rewards
        for (let level = oldLevel + 1; level <= newLevel; level++) {
            this.awardLevelRewards(player, level);
        }
        
        // Check for card unlocks
        this.checkCardUnlocks(player, newLevel);
        
        // Show level up message
        const milestone = this.levelMilestones.get(newLevel);
        const title = milestone ? milestone.title : "Champion";
        
        player.sendMessage(`§6§l✦ LEVEL UP! ✦`);
        player.sendMessage(`§eYou are now level ${newLevel}!`);
        player.sendMessage(`§7Title: §6${title}`);
        
        // Visual effects
        player.dimension.spawnParticle("minecraft:totem_particle", player.location);
        
        // Check achievements
        this.checkAchievements(player);
    }
    
    /**
     * Award level rewards
     */
    awardLevelRewards(player, level) {
        const milestone = this.levelMilestones.get(level);
        if (!milestone) return;
        
        const rewards = milestone.rewards;
        const playerData = this.playerDataManager.getPlayerData(player);
        
        // Award card packs
        if (rewards.cardPacks > 0) {
            this.awardCardPacks(player, "basic", rewards.cardPacks);
        }
        if (rewards.advancedCardPacks > 0) {
            this.awardCardPacks(player, "advanced", rewards.advancedCardPacks);
        }
        if (rewards.legendaryCardPacks > 0) {
            this.awardCardPacks(player, "legendary", rewards.legendaryCardPacks);
        }
        
        // Award card fragments
        if (rewards.cardFragments > 0) {
            this.awardCardFragments(player, "normal", rewards.cardFragments);
        }
        if (rewards.rareCardFragments > 0) {
            this.awardCardFragments(player, "rare", rewards.rareCardFragments);
        }
        
        // Increase max energy
        if (rewards.maxEnergy > 0) {
            playerData.maxEnergy = Math.min(10, playerData.maxEnergy + rewards.maxEnergy);
            player.sendMessage(`§b+${rewards.maxEnergy} Max Energy! (${playerData.maxEnergy})`);
        }
        
        // Award deck slots
        if (rewards.deckSlots > 0) {
            playerData.maxDeckSlots = (playerData.maxDeckSlots || 3) + rewards.deckSlots;
            player.sendMessage(`§d+${rewards.deckSlots} Deck Slots! (${playerData.maxDeckSlots})`);
        }
        
        this.playerDataManager.updatePlayerData(player, playerData);
    }
    
    /**
     * Check for card unlocks
     */
    checkCardUnlocks(player, level) {
        const unlockedCards = this.cardUnlocks.get(level);
        if (!unlockedCards) return;
        
        for (const cardId of unlockedCards) {
            this.playerDataManager.addCardToCollection(player, cardId, 1);
            const card = this.cardManager.getCard(cardId);
            if (card) {
                player.sendMessage(`§a🔓 Unlocked: ${this.cardManager.formatCard(cardId)}!`);
            }
        }
    }
    
    /**
     * Check achievements
     */
    checkAchievements(player) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        const unlockedAchievements = playerData.unlockedAchievements || new Set();
        
        for (const [achievementId, achievement] of this.achievements) {
            if (unlockedAchievements.has(achievementId)) continue;
            
            if (achievement.condition(playerData, playerData.collection)) {
                this.unlockAchievement(player, achievementId);
            }
        }
    }
    
    /**
     * Unlock achievement
     */
    unlockAchievement(player, achievementId) {
        const achievement = this.achievements.get(achievementId);
        if (!achievement) return;
        
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        // Add to unlocked achievements
        if (!playerData.unlockedAchievements) {
            playerData.unlockedAchievements = new Set();
        }
        playerData.unlockedAchievements.add(achievementId);
        
        // Award rewards
        this.awardAchievementRewards(player, achievement.rewards);
        
        // Show achievement message
        player.sendMessage(`§6§l🏆 ACHIEVEMENT UNLOCKED! 🏆`);
        player.sendMessage(`§e${achievement.icon} ${achievement.name}`);
        player.sendMessage(`§7${achievement.description}`);
        
        // Visual effects
        player.dimension.spawnParticle("minecraft:totem_particle", player.location);
        
        this.playerDataManager.updatePlayerData(player, playerData);
    }
    
    /**
     * Award achievement rewards
     */
    awardAchievementRewards(player, rewards) {
        if (rewards.cardPacks) {
            this.awardCardPacks(player, "basic", rewards.cardPacks);
        }
        if (rewards.advancedCardPacks) {
            this.awardCardPacks(player, "advanced", rewards.advancedCardPacks);
        }
        if (rewards.legendaryCardPacks) {
            this.awardCardPacks(player, "legendary", rewards.legendaryCardPacks);
        }
        if (rewards.cardFragments) {
            this.awardCardFragments(player, "normal", rewards.cardFragments);
        }
        if (rewards.rareCardFragments) {
            this.awardCardFragments(player, "rare", rewards.rareCardFragments);
        }
        if (rewards.deckSlots) {
            const playerData = this.playerDataManager.getPlayerData(player);
            if (playerData) {
                playerData.maxDeckSlots = (playerData.maxDeckSlots || 3) + rewards.deckSlots;
                this.playerDataManager.updatePlayerData(player, playerData);
                player.sendMessage(`§d+${rewards.deckSlots} Deck Slots!`);
            }
        }
    }
    
    /**
     * Award card packs
     */
    awardCardPacks(player, type, amount) {
        // In a full implementation, this would give actual items
        // For now, we'll simulate opening the packs immediately
        for (let i = 0; i < amount; i++) {
            this.openCardPack(player, type);
        }
        
        player.sendMessage(`§a+${amount} ${type} card pack${amount > 1 ? 's' : ''}!`);
    }
    
    /**
     * Open a card pack
     */
    openCardPack(player, type) {
        const cardsInPack = type === "legendary" ? 3 : type === "advanced" ? 4 : 5;
        const guaranteedRarity = type === "legendary" ? "legendary" : type === "advanced" ? "rare" : null;
        
        const drawnCards = [];
        
        for (let i = 0; i < cardsInPack; i++) {
            let card;
            
            if (i === 0 && guaranteedRarity) {
                // First card is guaranteed rarity
                const cardsOfRarity = this.cardManager.getCardsByRarity(guaranteedRarity);
                card = cardsOfRarity[Math.floor(Math.random() * cardsOfRarity.length)];
            } else {
                // Random card with weighted rarity
                card = this.cardManager.generateRandomCard();
            }
            
            if (card) {
                drawnCards.push(card);
                this.playerDataManager.addCardToCollection(player, card.id, 1);
            }
        }
        
        // Show pack opening results
        player.sendMessage(`§6📦 Card Pack Opened:`);
        for (const card of drawnCards) {
            const color = this.cardManager.getRarityColor(card.rarity);
            player.sendMessage(`  ${color}${card.name}§r`);
        }
    }
    
    /**
     * Award card fragments
     */
    awardCardFragments(player, type, amount) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        const fragmentKey = type === "rare" ? "rareCardFragments" : "cardFragments";
        playerData[fragmentKey] = (playerData[fragmentKey] || 0) + amount;
        
        this.playerDataManager.updatePlayerData(player, playerData);
        
        const fragmentName = type === "rare" ? "Rare Card Fragments" : "Card Fragments";
        player.sendMessage(`§a+${amount} ${fragmentName}!`);
    }
    
    /**
     * Update combat statistics
     */
    updateCombatStats(player, result, turnsElapsed, damageTaken) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        // Initialize stats if needed
        if (!playerData.combatStats) {
            playerData.combatStats = {
                wins: 0,
                losses: 0,
                currentWinStreak: 0,
                bestWinStreak: 0,
                flawlessVictories: 0,
                fastestVictory: 0,
                totalTurns: 0,
                totalDamageTaken: 0,
                totalDamageDealt: 0
            };
        }
        
        const stats = playerData.combatStats;
        
        // Update basic stats
        if (result === "victory") {
            stats.wins++;
            stats.currentWinStreak++;
            stats.bestWinStreak = Math.max(stats.bestWinStreak, stats.currentWinStreak);
            
            // Check for flawless victory
            if (damageTaken === 0) {
                stats.flawlessVictories++;
            }
            
            // Check for fastest victory
            if (stats.fastestVictory === 0 || turnsElapsed < stats.fastestVictory) {
                stats.fastestVictory = turnsElapsed;
            }
            
            // Award experience based on performance
            let expGain = 50; // Base experience
            if (damageTaken === 0) expGain += 25; // Flawless bonus
            if (turnsElapsed <= 5) expGain += 15; // Speed bonus
            
            this.awardExperience(player, expGain, "victory");
            
        } else {
            stats.losses++;
            stats.currentWinStreak = 0;
            
            // Consolation experience
            this.awardExperience(player, 10, "participation");
        }
        
        stats.totalTurns += turnsElapsed;
        stats.totalDamageTaken += damageTaken;
        
        this.playerDataManager.updatePlayerData(player, playerData);
        
        // Check achievements after updating stats
        this.checkAchievements(player);
    }
    
    /**
     * Helper methods for achievement conditions
     */
    
    hasAllCardsOfRarity(collection, rarity) {
        const cardsOfRarity = this.cardManager.getCardsByRarity(rarity);
        for (const card of cardsOfRarity) {
            if (!collection.has(card.id) || collection.get(card.id) === 0) {
                return false;
            }
        }
        return true;
    }
    
    hasCardOfRarity(collection, rarity) {
        const cardsOfRarity = this.cardManager.getCardsByRarity(rarity);
        for (const card of cardsOfRarity) {
            if (collection.has(card.id) && collection.get(card.id) > 0) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get player progression summary
     */
    getProgressionSummary(player) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return null;
        
        const currentLevel = playerData.level;
        const nextLevel = currentLevel + 1;
        const milestone = this.levelMilestones.get(nextLevel);
        
        let expToNext = 0;
        let expProgress = 0;
        
        if (milestone) {
            const totalExpForNext = this.getTotalExperienceForLevel(nextLevel);
            expToNext = totalExpForNext - playerData.experience;
            expProgress = (playerData.experience / totalExpForNext) * 100;
        }
        
        return {
            level: currentLevel,
            experience: playerData.experience,
            experienceToNext: expToNext,
            progressPercent: expProgress,
            title: this.levelMilestones.get(currentLevel)?.title || "Champion",
            achievements: playerData.unlockedAchievements?.size || 0,
            totalAchievements: this.achievements.size,
            wins: playerData.combatStats?.wins || 0,
            winStreak: playerData.combatStats?.currentWinStreak || 0
        };
    }
    
    /**
     * Get total experience required for a level
     */
    getTotalExperienceForLevel(level) {
        let total = 0;
        for (let i = 1; i <= level; i++) {
            const milestone = this.levelMilestones.get(i);
            if (milestone) {
                total += milestone.experienceRequired;
            }
        }
        return total;
    }
}
