/**
 * TestManager - Testing utilities for the card combat system
 */

import { world, system } from "@minecraft/server";

export class TestManager {
    constructor(combatManager, cardManager, playerDataManager, progressionManager, audioManager = null) {
        this.combatManager = combatManager;
        this.cardManager = cardManager;
        this.playerDataManager = playerDataManager;
        this.progressionManager = progressionManager;
        this.audioManager = audioManager;
        
        this.testResults = new Map();
        this.isRunningTests = false;
    }
    
    /**
     * Run all tests
     */
    async runAllTests(player) {
        if (this.isRunningTests) {
            player.sendMessage("§cTests are already running!");
            return;
        }
        
        this.isRunningTests = true;
        player.sendMessage("§6Starting Card Combat System Tests...");
        
        const testSuites = [
            { name: "Card System", tests: this.runCardSystemTests.bind(this) },
            { name: "Combat System", tests: this.runCombatSystemTests.bind(this) },
            { name: "Player Data", tests: this.runPlayerDataTests.bind(this) },
            { name: "Progression", tests: this.runProgressionTests.bind(this) },
            { name: "Audio System", tests: this.runAudioSystemTests.bind(this) },
            { name: "Integration", tests: this.runIntegrationTests.bind(this) }
        ];
        
        let totalTests = 0;
        let passedTests = 0;
        
        for (const suite of testSuites) {
            player.sendMessage(`§e\n--- Testing ${suite.name} ---`);
            const results = await suite.tests(player);
            
            for (const [testName, result] of results) {
                totalTests++;
                if (result.passed) {
                    passedTests++;
                    player.sendMessage(`§a✓ ${testName}`);
                } else {
                    player.sendMessage(`§c✗ ${testName}: ${result.error}`);
                }
            }
        }
        
        // Summary
        const successRate = Math.round((passedTests / totalTests) * 100);
        player.sendMessage(`§6\n=== Test Summary ===`);
        player.sendMessage(`§7Total Tests: ${totalTests}`);
        player.sendMessage(`§aPassed: ${passedTests}`);
        player.sendMessage(`§cFailed: ${totalTests - passedTests}`);
        player.sendMessage(`§eSuccess Rate: ${successRate}%`);
        
        if (successRate >= 90) {
            player.sendMessage("§a🎉 System is ready for use!");
        } else if (successRate >= 70) {
            player.sendMessage("§eSystem has some issues but is functional");
        } else {
            player.sendMessage("§cSystem has critical issues that need fixing");
        }
        
        this.isRunningTests = false;
    }
    
    /**
     * Test card system
     */
    async runCardSystemTests(player) {
        const results = new Map();
        
        // Test card database
        try {
            const allCards = this.cardManager.getAllCards();
            results.set("Card Database Loaded", {
                passed: allCards.length > 0,
                error: allCards.length === 0 ? "No cards found" : null
            });
        } catch (error) {
            results.set("Card Database Loaded", {
                passed: false,
                error: error.message
            });
        }
        
        // Test card types
        try {
            const attackCards = this.cardManager.getCardsByType("attack");
            const defenseCards = this.cardManager.getCardsByType("defense");
            const spellCards = this.cardManager.getCardsByType("spell");
            const itemCards = this.cardManager.getCardsByType("item");
            
            results.set("Card Types Available", {
                passed: attackCards.length > 0 && defenseCards.length > 0 && 
                       spellCards.length > 0 && itemCards.length > 0,
                error: "Missing cards for some types"
            });
        } catch (error) {
            results.set("Card Types Available", {
                passed: false,
                error: error.message
            });
        }
        
        // Test card rarities
        try {
            const commonCards = this.cardManager.getCardsByRarity("common");
            const uncommonCards = this.cardManager.getCardsByRarity("uncommon");
            const rareCards = this.cardManager.getCardsByRarity("rare");
            const legendaryCards = this.cardManager.getCardsByRarity("legendary");
            
            results.set("Card Rarities Available", {
                passed: commonCards.length > 0 && uncommonCards.length > 0 && 
                       rareCards.length > 0 && legendaryCards.length > 0,
                error: "Missing cards for some rarities"
            });
        } catch (error) {
            results.set("Card Rarities Available", {
                passed: false,
                error: error.message
            });
        }
        
        // Test starter deck creation
        try {
            const starterDeck = this.cardManager.createStarterDeck();
            results.set("Starter Deck Creation", {
                passed: starterDeck.length >= 10,
                error: starterDeck.length < 10 ? "Starter deck too small" : null
            });
        } catch (error) {
            results.set("Starter Deck Creation", {
                passed: false,
                error: error.message
            });
        }
        
        // Test deck validation
        try {
            const testDeck = ["basic_strike", "basic_strike", "basic_block"];
            const validation = this.cardManager.validateDeck(testDeck);
            results.set("Deck Validation", {
                passed: validation.hasOwnProperty("isValid"),
                error: !validation.hasOwnProperty("isValid") ? "Validation function broken" : null
            });
        } catch (error) {
            results.set("Deck Validation", {
                passed: false,
                error: error.message
            });
        }
        
        return results;
    }
    
    /**
     * Test combat system
     */
    async runCombatSystemTests(player) {
        const results = new Map();
        
        // Test combat manager initialization
        try {
            results.set("Combat Manager Initialized", {
                passed: this.combatManager !== null && this.combatManager !== undefined,
                error: "Combat manager not found"
            });
        } catch (error) {
            results.set("Combat Manager Initialized", {
                passed: false,
                error: error.message
            });
        }
        
        // Test combat detection
        try {
            const shouldStart = this.combatManager.shouldStartCombat(player, { damagingEntity: player });
            results.set("Combat Detection Logic", {
                passed: typeof shouldStart === "boolean",
                error: "Combat detection not returning boolean"
            });
        } catch (error) {
            results.set("Combat Detection Logic", {
                passed: false,
                error: error.message
            });
        }
        
        // Test turn order calculation
        try {
            const participants = [player];
            const turnOrder = this.combatManager.determineTurnOrder(participants);
            results.set("Turn Order Calculation", {
                passed: Array.isArray(turnOrder) && turnOrder.length === participants.length,
                error: "Turn order calculation failed"
            });
        } catch (error) {
            results.set("Turn Order Calculation", {
                passed: false,
                error: error.message
            });
        }
        
        // Test AI damage calculation
        try {
            const damage = this.combatManager.calculateAIDamage({ typeId: "minecraft:zombie" });
            results.set("AI Damage Calculation", {
                passed: typeof damage === "number" && damage > 0,
                error: "AI damage calculation failed"
            });
        } catch (error) {
            results.set("AI Damage Calculation", {
                passed: false,
                error: error.message
            });
        }
        
        return results;
    }
    
    /**
     * Test player data system
     */
    async runPlayerDataTests(player) {
        const results = new Map();
        
        // Test player initialization
        try {
            this.playerDataManager.initializePlayer(player);
            const playerData = this.playerDataManager.getPlayerData(player);
            results.set("Player Data Initialization", {
                passed: playerData !== null && playerData !== undefined,
                error: "Player data not created"
            });
        } catch (error) {
            results.set("Player Data Initialization", {
                passed: false,
                error: error.message
            });
        }
        
        // Test card collection
        try {
            this.playerDataManager.addCardToCollection(player, "basic_strike", 1);
            const playerData = this.playerDataManager.getPlayerData(player);
            const hasCard = playerData.collection.has("basic_strike");
            results.set("Card Collection Management", {
                passed: hasCard,
                error: "Card not added to collection"
            });
        } catch (error) {
            results.set("Card Collection Management", {
                passed: false,
                error: error.message
            });
        }
        
        // Test deck management
        try {
            const playerData = this.playerDataManager.getPlayerData(player);
            const deckSize = playerData.activeDeck.length;
            results.set("Deck Management", {
                passed: deckSize > 0,
                error: "No active deck found"
            });
        } catch (error) {
            results.set("Deck Management", {
                passed: false,
                error: error.message
            });
        }
        
        // Test hand drawing
        try {
            this.playerDataManager.drawInitialHand(player);
            const playerData = this.playerDataManager.getPlayerData(player);
            const handSize = playerData.currentHand.length;
            results.set("Hand Drawing", {
                passed: handSize > 0,
                error: "No cards in hand"
            });
        } catch (error) {
            results.set("Hand Drawing", {
                passed: false,
                error: error.message
            });
        }
        
        return results;
    }
    
    /**
     * Test progression system
     */
    async runProgressionTests(player) {
        const results = new Map();
        
        // Test experience awarding
        try {
            const playerData = this.playerDataManager.getPlayerData(player);
            const oldExp = playerData.experience;
            this.progressionManager.awardExperience(player, 50, "test");
            const newExp = this.playerDataManager.getPlayerData(player).experience;
            results.set("Experience Awarding", {
                passed: newExp > oldExp,
                error: "Experience not awarded"
            });
        } catch (error) {
            results.set("Experience Awarding", {
                passed: false,
                error: error.message
            });
        }
        
        // Test level calculation
        try {
            const level = this.progressionManager.calculateLevel(1000);
            results.set("Level Calculation", {
                passed: typeof level === "number" && level > 0,
                error: "Level calculation failed"
            });
        } catch (error) {
            results.set("Level Calculation", {
                passed: false,
                error: error.message
            });
        }
        
        // Test achievement checking
        try {
            this.progressionManager.checkAchievements(player);
            results.set("Achievement System", {
                passed: true,
                error: null
            });
        } catch (error) {
            results.set("Achievement System", {
                passed: false,
                error: error.message
            });
        }
        
        return results;
    }

    /**
     * Test audio system
     */
    async runAudioSystemTests(player) {
        const results = new Map();

        // Test audio manager initialization
        try {
            results.set("Audio Manager Initialized", {
                passed: this.audioManager !== null && this.audioManager !== undefined,
                error: "Audio manager not found"
            });
        } catch (error) {
            results.set("Audio Manager Initialized", {
                passed: false,
                error: error.message
            });
        }

        // Test sound path definitions
        try {
            const soundPaths = this.audioManager?.soundPaths;
            const requiredSounds = ['card_play', 'card_draw', 'combat_start', 'combat_end', 'attack_hit'];
            let allSoundsFound = true;

            for (const soundKey of requiredSounds) {
                if (!soundPaths || !soundPaths[soundKey]) {
                    allSoundsFound = false;
                    break;
                }
            }

            results.set("Sound Definitions Complete", {
                passed: allSoundsFound,
                error: allSoundsFound ? null : "Missing sound definitions"
            });
        } catch (error) {
            results.set("Sound Definitions Complete", {
                passed: false,
                error: error.message
            });
        }

        // Test volume calculation
        try {
            if (this.audioManager) {
                const volume = this.audioManager.calculateVolume('card_play', 1.0);
                results.set("Volume Calculation", {
                    passed: typeof volume === "number" && volume >= 0,
                    error: "Volume calculation failed"
                });
            } else {
                results.set("Volume Calculation", {
                    passed: false,
                    error: "Audio manager not available"
                });
            }
        } catch (error) {
            results.set("Volume Calculation", {
                passed: false,
                error: error.message
            });
        }

        // Test sound variant selection
        try {
            if (this.audioManager) {
                const variant = this.audioManager.selectSoundVariant('card_play', ['sound1', 'sound2', 'sound3']);
                results.set("Sound Variant Selection", {
                    passed: typeof variant === "string" && variant.length > 0,
                    error: "Sound variant selection failed"
                });
            } else {
                results.set("Sound Variant Selection", {
                    passed: false,
                    error: "Audio manager not available"
                });
            }
        } catch (error) {
            results.set("Sound Variant Selection", {
                passed: false,
                error: error.message
            });
        }

        // Test cooldown system
        try {
            if (this.audioManager) {
                this.audioManager.setCooldown('test_sound', player, 1000);
                const isOnCooldown = this.audioManager.isOnCooldown('test_sound', player);
                results.set("Sound Cooldown System", {
                    passed: isOnCooldown === true,
                    error: "Cooldown system not working"
                });
            } else {
                results.set("Sound Cooldown System", {
                    passed: false,
                    error: "Audio manager not available"
                });
            }
        } catch (error) {
            results.set("Sound Cooldown System", {
                passed: false,
                error: error.message
            });
        }

        return results;
    }

    /**
     * Test system integration
     */
    async runIntegrationTests(player) {
        const results = new Map();
        
        // Test card playing integration
        try {
            const playerData = this.playerDataManager.getPlayerData(player);
            if (playerData.currentHand.length > 0) {
                const cardId = playerData.currentHand[0];
                const oldEnergy = playerData.currentEnergy;
                const success = this.playerDataManager.playCard(player, cardId);
                const newEnergy = this.playerDataManager.getPlayerData(player).currentEnergy;
                
                results.set("Card Playing Integration", {
                    passed: success && newEnergy <= oldEnergy,
                    error: "Card playing integration failed"
                });
            } else {
                results.set("Card Playing Integration", {
                    passed: false,
                    error: "No cards in hand to test"
                });
            }
        } catch (error) {
            results.set("Card Playing Integration", {
                passed: false,
                error: error.message
            });
        }
        
        // Test combat stats integration
        try {
            this.progressionManager.updateCombatStats(player, "victory", 5, 0);
            const playerData = this.playerDataManager.getPlayerData(player);
            const hasStats = playerData.combatStats && playerData.combatStats.wins > 0;
            results.set("Combat Stats Integration", {
                passed: hasStats,
                error: "Combat stats not updated"
            });
        } catch (error) {
            results.set("Combat Stats Integration", {
                passed: false,
                error: error.message
            });
        }
        
        // Test UI integration
        try {
            // This is a basic test - in a full implementation, we'd test actual UI
            results.set("UI Integration", {
                passed: true,
                error: null
            });
        } catch (error) {
            results.set("UI Integration", {
                passed: false,
                error: error.message
            });
        }
        
        return results;
    }
    
    /**
     * Create test scenario
     */
    createTestScenario(player, scenarioName) {
        switch (scenarioName) {
            case "basic_combat":
                this.createBasicCombatScenario(player);
                break;
            case "card_collection":
                this.createCardCollectionScenario(player);
                break;
            case "progression":
                this.createProgressionScenario(player);
                break;
            default:
                player.sendMessage("§cUnknown test scenario: " + scenarioName);
        }
    }
    
    /**
     * Create basic combat test scenario
     */
    createBasicCombatScenario(player) {
        // Reset player data
        this.playerDataManager.resetPlayer(player);
        
        // Give basic cards
        this.playerDataManager.addCardToCollection(player, "basic_strike", 3);
        this.playerDataManager.addCardToCollection(player, "basic_block", 2);
        this.playerDataManager.addCardToCollection(player, "heal", 1);
        
        // Set up deck
        const testDeck = ["basic_strike", "basic_strike", "basic_block", "heal"];
        const playerData = this.playerDataManager.getPlayerData(player);
        playerData.activeDeck = testDeck;
        this.playerDataManager.updatePlayerData(player, playerData);
        
        // Spawn test enemy
        player.runCommand("summon zombie ~ ~1 ~3 test_zombie");
        
        player.sendMessage("§aBasic combat scenario created!");
        player.sendMessage("§7Attack the zombie to start combat");
    }
    
    /**
     * Create card collection test scenario
     */
    createCardCollectionScenario(player) {
        // Give various card packs
        player.runCommand("give @s card_combat:basic_card_pack 3");
        player.runCommand("give @s card_combat:advanced_card_pack 2");
        player.runCommand("give @s card_combat:legendary_card_pack 1");
        player.runCommand("give @s card_combat:card_fragment 20");
        player.runCommand("give @s card_combat:deck_builder 1");
        
        player.sendMessage("§aCard collection scenario created!");
        player.sendMessage("§7Use the items to test card collection");
    }
    
    /**
     * Create progression test scenario
     */
    createProgressionScenario(player) {
        // Award lots of experience
        this.progressionManager.awardExperience(player, 500, "test");
        
        // Simulate combat victories
        for (let i = 0; i < 10; i++) {
            this.progressionManager.updateCombatStats(player, "victory", 3, 0);
        }
        
        player.sendMessage("§aProgression scenario created!");
        player.sendMessage("§7Check your level and achievements");
    }
    
    /**
     * Performance test
     */
    runPerformanceTest(player, iterations = 100) {
        player.sendMessage("§6Running performance test...");
        
        const startTime = Date.now();
        
        // Test card operations
        for (let i = 0; i < iterations; i++) {
            this.cardManager.generateRandomCard();
            this.cardManager.createStarterDeck();
        }
        
        // Test player data operations
        for (let i = 0; i < iterations; i++) {
            const playerData = this.playerDataManager.getPlayerData(player);
            this.playerDataManager.drawCard(player);
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        player.sendMessage(`§aPerformance test completed in ${duration}ms`);
        player.sendMessage(`§7Average: ${(duration / iterations).toFixed(2)}ms per operation`);
        
        if (duration < 1000) {
            player.sendMessage("§aPerformance: Excellent");
        } else if (duration < 3000) {
            player.sendMessage("§ePerformance: Good");
        } else {
            player.sendMessage("§cPerformance: Needs optimization");
        }
    }
    
    /**
     * Memory usage test
     */
    checkMemoryUsage(player) {
        const playerCount = world.getAllPlayers().length;
        const activeData = this.playerDataManager.playerData.size;
        const activeCombats = this.combatManager.activeCombats.size;
        
        player.sendMessage("§6=== Memory Usage ===");
        player.sendMessage(`§7Players: ${playerCount}`);
        player.sendMessage(`§7Active Player Data: ${activeData}`);
        player.sendMessage(`§7Active Combats: ${activeCombats}`);
        
        // Check for memory leaks
        if (activeData > playerCount * 2) {
            player.sendMessage("§cWarning: Possible memory leak in player data");
        }
        
        if (activeCombats > playerCount) {
            player.sendMessage("§cWarning: Too many active combats");
        }
    }
}
