{"level_progression_test": {"description": "Test ability unlocks at each level", "steps": ["Create new player with warrior class", "Set player to level 5 - should unlock Battle Hardened passive", "Set player to level 10 - should unlock <PERSON><PERSON><PERSON><PERSON>'s Wrath ultimate + card", "Set player to level 15 - should unlock Unstoppable Force advanced passive", "Set player to level 20 - should unlock Avatar of War master ability + card"], "expected_results": ["Battle Hardened passive available at level 5", "<PERSON><PERSON><PERSON><PERSON>'s Wrath ultimate and card available at level 10", "Unstoppable Force passive available at level 15", "Avatar of War master ability and card available at level 20"]}, "passive_ability_test": {"description": "Test passive abilities trigger correctly", "steps": ["Start combat with level 5+ warrior", "Begin turn - should gain 1 block from Battle Hardened", "Start combat with level 5+ mage at max energy", "Begin turn - should draw extra card from Arcane Intellect", "Start combat with level 5+ paladin", "End turn - should heal 2 HP from <PERSON> Grace"], "expected_results": ["Warrior gains 1 block at turn start", "<PERSON><PERSON> draws extra card when at max energy", "<PERSON><PERSON><PERSON> heals 2 HP at turn end"]}, "ultimate_ability_test": {"description": "Test ultimate abilities work in combat", "steps": ["Start combat with level 10+ warrior", "Use /class ultimate or play <PERSON><PERSON><PERSON><PERSON>'s <PERSON> card", "Verify energy cost is consumed (6 energy)", "Verify berserker status effect is applied", "Attack enemy - should deal double damage and heal 25%", "Try to use ultimate again - should fail (once per combat)"], "expected_results": ["Ultimate consumes 6 energy", "<PERSON><PERSON><PERSON><PERSON>'s Wrath status effect applied for 3 turns", "Double damage and healing on attacks", "Cannot use ultimate again in same combat"]}, "card_integration_test": {"description": "Test ultimate cards are properly integrated", "steps": ["Check level 10 warrior deck contains berserker_wrath card", "Check level 20 warrior deck contains avatar_of_war card", "Play ultimate card in combat", "Verify same effect as direct ultimate usage", "Verify card respects once-per-combat limit"], "expected_results": ["Ultimate cards added to deck at appropriate levels", "Cards trigger same effects as direct ultimate usage", "Cards respect combat limitations"]}, "class_specialization_test": {"description": "Test each class has unique abilities", "classes": {"warrior": {"passive_5": "Battle Hardened - +1 block per turn", "ultimate_10": "<PERSON><PERSON><PERSON><PERSON>'s Wrath - double damage + healing", "passive_15": "Unstoppable Force - debuff immunity", "master_20": "Avatar of War - enhanced berserker + team buff"}, "mage": {"passive_5": "Arcane Intellect - extra card at max energy", "ultimate_10": "Arcane Ascendance - energy + cards + free spells", "passive_15": "Spell Mastery - spells cost 1 less", "master_20": "Archmage Transcendence - enhanced ascendance"}, "rogue": {"passive_5": "Assassinate - 10% instant kill low HP enemies", "ultimate_10": "Shadow Clone - clone copies 3 attacks", "passive_15": "Shadow Step - 25% dodge chance", "master_20": "Master Assassin - 3 clones, 5 attacks"}, "paladin": {"passive_5": "Divine Grace - heal 2 HP per turn", "ultimate_10": "Divine Intervention - heal all + immunity", "passive_15": "<PERSON>ra of Protection - team gets +1 block", "master_20": "Divine Avatar - enhanced intervention"}, "necromancer": {"passive_5": "Death Magic - +1 energy on creature death", "ultimate_10": "Army of the Dead - summon 3 skeletons", "passive_15": "Soul Harvest - +1 max energy per kill (max +3)", "master_20": "Lich Lord - 5 skeletons, +2 energy per kill"}}}}