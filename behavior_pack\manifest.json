{"format_version": 2, "header": {"name": "Card Combat System - Behavior Pack", "description": "Turn-based card combat system for Minecraft Bedrock Edition v1.21.90", "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "version": [1, 1, 0], "min_engine_version": [1, 21, 90]}, "modules": [{"description": "Card Combat System Scripts", "type": "script", "language": "javascript", "uuid": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "version": [1, 1, 0], "entry": "scripts/main.js"}, {"description": "Card Combat System Data", "type": "data", "uuid": "c3d4e5f6-g7h8-9012-cdef-************", "version": [1, 1, 0]}], "capabilities": ["script_eval"], "dependencies": [{"module_name": "@minecraft/server", "version": "2.2.0-beta"}, {"module_name": "@minecraft/server-ui", "version": "2.0.0-beta"}, {"uuid": "d4e5f6g7-h8i9-0123-defg-************", "version": [1, 1, 0]}]}