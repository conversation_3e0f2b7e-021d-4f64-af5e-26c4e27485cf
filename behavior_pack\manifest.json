{"format_version": 2, "header": {"name": "Card Combat System - Behavior Pack", "description": "Turn-based card combat system for Minecraft Bedrock Edition v1.21.90", "uuid": "a1b2c3d4-e5f6-4890-abcd-ef1234567890", "version": [1, 1, 0], "min_engine_version": [1, 21, 90]}, "modules": [{"description": "Card Combat System Scripts", "type": "script", "language": "javascript", "uuid": "b2c3d4e5-f6a7-4901-bcde-f23456789012", "version": [1, 1, 0], "entry": "scripts/main.js"}, {"description": "Card Combat System Data", "type": "data", "uuid": "c3d4e5f6-a7b8-4012-cdef-************", "version": [1, 1, 0]}], "capabilities": ["script_eval"], "dependencies": [{"module_name": "@minecraft/server", "version": "2.1.0-beta"}, {"module_name": "@minecraft/server-ui", "version": "2.1.0-beta"}, {"uuid": "e4e5f6a7-b8c9-4123-defa-************", "version": [1, 1, 0]}]}