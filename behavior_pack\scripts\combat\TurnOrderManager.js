/**
 * TurnOrderManager - Advanced turn order and initiative system
 */

export class TurnOrderManager {
    constructor(playerDataManager, statusEffectManager) {
        this.playerDataManager = playerDataManager;
        this.statusEffectManager = statusEffectManager;
        
        // Turn order algorithms
        this.algorithms = {
            SIMPLE: "simple",
            INITIATIVE: "initiative",
            SPEED_BASED: "speed_based",
            CARD_BASED: "card_based"
        };
        
        // Default algorithm
        this.currentAlgorithm = this.algorithms.INITIATIVE;
    }
    
    /**
     * Calculate turn order for combat participants
     */
    calculateTurnOrder(participants, algorithm = null) {
        const algo = algorithm || this.currentAlgorithm;
        
        switch (algo) {
            case this.algorithms.SIMPLE:
                return this.calculateSimpleTurnOrder(participants);
            case this.algorithms.INITIATIVE:
                return this.calculateInitiativeTurnOrder(participants);
            case this.algorithms.SPEED_BASED:
                return this.calculateSpeedBasedTurnOrder(participants);
            case this.algorithms.CARD_BASED:
                return this.calculateCardBasedTurnOrder(participants);
            default:
                return this.calculateInitiativeTurnOrder(participants);
        }
    }
    
    /**
     * Simple turn order - players first, then by entity type
     */
    calculateSimpleTurnOrder(participants) {
        return participants.sort((a, b) => {
            // Players always go first
            if (a.typeId === "minecraft:player" && b.typeId !== "minecraft:player") {
                return -1;
            }
            if (b.typeId === "minecraft:player" && a.typeId !== "minecraft:player") {
                return 1;
            }
            
            // Among non-players, sort by entity type
            if (a.typeId !== "minecraft:player" && b.typeId !== "minecraft:player") {
                return a.typeId.localeCompare(b.typeId);
            }
            
            return 0;
        });
    }
    
    /**
     * Initiative-based turn order with dice rolls
     */
    calculateInitiativeTurnOrder(participants) {
        const initiativeRolls = participants.map(participant => ({
            participant,
            initiative: this.rollInitiative(participant),
            speedModifier: this.getSpeedModifier(participant),
            tieBreaker: Math.random()
        }));
        
        // Sort by total initiative (higher goes first)
        initiativeRolls.sort((a, b) => {
            const totalA = a.initiative + a.speedModifier;
            const totalB = b.initiative + b.speedModifier;
            
            if (totalA !== totalB) {
                return totalB - totalA; // Higher initiative goes first
            }
            
            // Tie breaker
            return b.tieBreaker - a.tieBreaker;
        });
        
        return initiativeRolls.map(roll => roll.participant);
    }
    
    /**
     * Speed-based turn order using entity attributes
     */
    calculateSpeedBasedTurnOrder(participants) {
        const speedData = participants.map(participant => ({
            participant,
            speed: this.calculateEntitySpeed(participant),
            randomFactor: Math.random() * 0.1 // Small random factor
        }));
        
        speedData.sort((a, b) => {
            const totalSpeedA = a.speed + a.randomFactor;
            const totalSpeedB = b.speed + b.randomFactor;
            return totalSpeedB - totalSpeedA; // Higher speed goes first
        });
        
        return speedData.map(data => data.participant);
    }
    
    /**
     * Card-based turn order using deck composition
     */
    calculateCardBasedTurnOrder(participants) {
        const cardData = participants.map(participant => ({
            participant,
            priority: this.calculateCardPriority(participant),
            initiative: this.rollInitiative(participant)
        }));
        
        cardData.sort((a, b) => {
            // First sort by card priority
            if (a.priority !== b.priority) {
                return b.priority - a.priority;
            }
            
            // Then by initiative
            return b.initiative - a.initiative;
        });
        
        return cardData.map(data => data.participant);
    }
    
    /**
     * Roll initiative for a participant
     */
    rollInitiative(participant) {
        let baseRoll = Math.floor(Math.random() * 20) + 1; // 1d20
        
        // Add modifiers based on entity type
        if (participant.typeId === "minecraft:player") {
            baseRoll += 2; // Players get +2 initiative
        }
        
        // Add level-based modifier for players
        const playerData = this.playerDataManager.getPlayerData(participant);
        if (playerData) {
            baseRoll += Math.floor(playerData.level / 5); // +1 per 5 levels
        }
        
        // Add equipment modifiers
        baseRoll += this.getEquipmentInitiativeBonus(participant);
        
        return baseRoll;
    }
    
    /**
     * Get speed modifier from status effects
     */
    getSpeedModifier(participant) {
        let modifier = 0;
        
        // Check for speed-affecting status effects
        if (this.statusEffectManager.hasStatusEffect(participant, "speed")) {
            const speedData = this.statusEffectManager.getStatusEffectData(participant, "speed");
            modifier += speedData.amount * 2;
        }
        
        if (this.statusEffectManager.hasStatusEffect(participant, "slow")) {
            const slowData = this.statusEffectManager.getStatusEffectData(participant, "slow");
            modifier -= slowData.amount * 2;
        }
        
        if (this.statusEffectManager.hasStatusEffect(participant, "stun")) {
            modifier -= 10; // Stunned entities go last
        }
        
        return modifier;
    }
    
    /**
     * Calculate entity speed based on type and attributes
     */
    calculateEntitySpeed(participant) {
        let speed = 10; // Base speed
        
        // Entity type modifiers
        const speedByType = {
            "minecraft:player": 12,
            "minecraft:zombie": 8,
            "minecraft:skeleton": 10,
            "minecraft:spider": 14,
            "minecraft:creeper": 9,
            "minecraft:enderman": 16,
            "card_combat:card_duelist": 11
        };
        
        speed = speedByType[participant.typeId] || speed;
        
        // Add movement component modifier
        const movement = participant.getComponent("movement");
        if (movement) {
            speed += Math.floor(movement.value * 10);
        }
        
        // Add status effect modifiers
        speed += this.getSpeedModifier(participant);
        
        return Math.max(1, speed);
    }
    
    /**
     * Calculate card priority for turn order
     */
    calculateCardPriority(participant) {
        const playerData = this.playerDataManager.getPlayerData(participant);
        if (!playerData) {
            return this.getAICardPriority(participant);
        }
        
        let priority = 0;
        
        // Check for speed cards in hand
        for (const cardId of playerData.currentHand) {
            const card = world.cardManager?.getCard(cardId);
            if (card) {
                if (card.effects && card.effects.includes("quick")) {
                    priority += 3;
                }
                if (card.cost === 0) {
                    priority += 1; // Free cards give slight priority
                }
            }
        }
        
        // Check for speed-enhancing items
        if (playerData.statusEffects.has("haste")) {
            priority += 5;
        }
        
        return priority;
    }
    
    /**
     * Get AI card priority
     */
    getAICardPriority(participant) {
        let priority = 0;
        
        // AI entities have different base priorities
        if (participant.hasTag("fast_ai")) {
            priority += 3;
        }
        if (participant.hasTag("slow_ai")) {
            priority -= 2;
        }
        
        // Card duelists have higher priority
        if (participant.typeId === "card_combat:card_duelist") {
            priority += 2;
        }
        
        return priority;
    }
    
    /**
     * Get equipment initiative bonus
     */
    getEquipmentInitiativeBonus(participant) {
        let bonus = 0;
        
        // Check for speed-enhancing equipment tags
        if (participant.hasTag("light_armor")) {
            bonus += 1;
        }
        if (participant.hasTag("heavy_armor")) {
            bonus -= 1;
        }
        if (participant.hasTag("speed_boots")) {
            bonus += 2;
        }
        if (participant.hasTag("agility_ring")) {
            bonus += 1;
        }
        
        return bonus;
    }
    
    /**
     * Recalculate turn order mid-combat (for effects that change speed)
     */
    recalculateTurnOrder(currentTurnOrder, currentTurnIndex) {
        const remainingParticipants = currentTurnOrder.slice(currentTurnIndex + 1);
        const completedParticipants = currentTurnOrder.slice(0, currentTurnIndex + 1);
        
        // Recalculate order for remaining participants
        const newRemainingOrder = this.calculateTurnOrder(remainingParticipants);
        
        // Combine completed and new remaining order
        return [...completedParticipants, ...newRemainingOrder];
    }
    
    /**
     * Handle turn order changes from effects
     */
    handleTurnOrderEffect(combatSession, effect, participant) {
        switch (effect) {
            case "extra_turn":
                this.grantExtraTurn(combatSession, participant);
                break;
            case "skip_turn":
                this.skipTurn(combatSession, participant);
                break;
            case "speed_boost":
                this.applySpeedBoost(combatSession, participant);
                break;
            case "time_warp":
                this.applyTimeWarp(combatSession);
                break;
        }
    }
    
    /**
     * Grant an extra turn to a participant
     */
    grantExtraTurn(combatSession, participant) {
        const currentIndex = combatSession.currentTurn;
        
        // Insert the participant right after the current turn
        combatSession.turnOrder.splice(currentIndex + 1, 0, participant);
        
        // Notify participants
        this.notifyTurnOrderChange(combatSession, `${participant.nameTag || participant.typeId} gets an extra turn!`);
    }
    
    /**
     * Skip a participant's turn
     */
    skipTurn(combatSession, participant) {
        const participantIndex = combatSession.turnOrder.indexOf(participant);
        
        if (participantIndex !== -1 && participantIndex > combatSession.currentTurn) {
            // Remove participant from this round
            combatSession.turnOrder.splice(participantIndex, 1);
            
            // Adjust current turn index if needed
            if (participantIndex <= combatSession.currentTurn) {
                combatSession.currentTurn--;
            }
            
            this.notifyTurnOrderChange(combatSession, `${participant.nameTag || participant.typeId} skips their turn!`);
        }
    }
    
    /**
     * Apply speed boost effect
     */
    applySpeedBoost(combatSession, participant) {
        // Move participant earlier in turn order if possible
        const participantIndex = combatSession.turnOrder.indexOf(participant);
        const currentIndex = combatSession.currentTurn;
        
        if (participantIndex > currentIndex + 1) {
            // Remove from current position
            combatSession.turnOrder.splice(participantIndex, 1);
            
            // Insert right after current turn
            combatSession.turnOrder.splice(currentIndex + 1, 0, participant);
            
            this.notifyTurnOrderChange(combatSession, `${participant.nameTag || participant.typeId} moves faster!`);
        }
    }
    
    /**
     * Apply time warp effect (reverses turn order)
     */
    applyTimeWarp(combatSession) {
        const currentIndex = combatSession.currentTurn;
        const remainingParticipants = combatSession.turnOrder.slice(currentIndex + 1);
        
        // Reverse the remaining turn order
        remainingParticipants.reverse();
        
        // Update turn order
        combatSession.turnOrder = [
            ...combatSession.turnOrder.slice(0, currentIndex + 1),
            ...remainingParticipants
        ];
        
        this.notifyTurnOrderChange(combatSession, "Time warps around the battlefield!");
    }
    
    /**
     * Get turn order display for UI
     */
    getTurnOrderDisplay(combatSession) {
        const display = [];
        
        for (let i = 0; i < combatSession.turnOrder.length; i++) {
            const participant = combatSession.turnOrder[i];
            const isCurrent = i === combatSession.currentTurn;
            const isNext = i === combatSession.currentTurn + 1;
            
            let indicator = "  ";
            if (isCurrent) {
                indicator = "►";
            } else if (isNext) {
                indicator = "→";
            }
            
            const name = participant.nameTag || participant.typeId;
            const health = participant.getComponent("health");
            const healthText = health ? `${health.currentValue}/${health.defaultValue}` : "?/?";
            
            display.push(`${indicator} ${name} (${healthText})`);
        }
        
        return display;
    }
    
    /**
     * Validate turn order
     */
    validateTurnOrder(turnOrder) {
        // Check for duplicates
        const seen = new Set();
        for (const participant of turnOrder) {
            if (seen.has(participant.id)) {
                return false;
            }
            seen.add(participant.id);
        }
        
        // Check that all participants are valid
        for (const participant of turnOrder) {
            if (!participant.isValid()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Clean up turn order (remove invalid participants)
     */
    cleanupTurnOrder(combatSession) {
        const validParticipants = combatSession.turnOrder.filter(p => p.isValid());
        
        if (validParticipants.length !== combatSession.turnOrder.length) {
            combatSession.turnOrder = validParticipants;
            
            // Adjust current turn index
            if (combatSession.currentTurn >= validParticipants.length) {
                combatSession.currentTurn = 0;
            }
            
            return true; // Turn order was modified
        }
        
        return false; // No changes needed
    }
    
    /**
     * Notify participants of turn order changes
     */
    notifyTurnOrderChange(combatSession, message) {
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player" && participant.isValid()) {
                participant.sendMessage(`§6[Turn Order] §r${message}`);
            }
        }
    }
    
    /**
     * Set turn order algorithm
     */
    setTurnOrderAlgorithm(algorithm) {
        if (Object.values(this.algorithms).includes(algorithm)) {
            this.currentAlgorithm = algorithm;
            return true;
        }
        return false;
    }
    
    /**
     * Get current algorithm
     */
    getCurrentAlgorithm() {
        return this.currentAlgorithm;
    }
}
