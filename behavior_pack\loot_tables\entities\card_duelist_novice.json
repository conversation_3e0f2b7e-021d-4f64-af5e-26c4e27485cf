{"pools": [{"rolls": 1, "entries": [{"type": "item", "name": "card_combat:basic_card_pack", "weight": 60, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}, {"type": "item", "name": "emerald", "weight": 30, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "experience_bottle", "weight": 10, "functions": [{"function": "set_count", "count": 1}]}]}, {"rolls": {"min": 0, "max": 1}, "entries": [{"type": "item", "name": "card_combat:card_fragment", "weight": 40, "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}]}], "conditions": [{"condition": "killed_by_player"}]}]}