/**
 * CardManager - <PERSON>les card definitions, deck management, and card logic
 */

export class CardManager {
    constructor() {
        this.cardDatabase = new Map();
        this.cardTypes = {
            ATTACK: "attack",
            DEFENSE: "defense",
            SPELL: "spell",
            ITEM: "item"
        };
        
        this.cardRarities = {
            COMMON: "common",
            UNCOMMON: "uncommon", 
            RARE: "rare",
            LEGENDARY: "legendary"
        };
        
        this.initializeCards();
    }
    
    /**
     * Initialize the card database with predefined cards
     */
    initializeCards() {
        // Attack Cards
        this.registerCard({
            id: "basic_strike",
            name: "Basic Strike",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.COMMON,
            cost: 1,
            damage: 3,
            description: "Deal 3 damage to target enemy",
            effects: []
        });
        
        this.registerCard({
            id: "power_attack",
            name: "Power Attack",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            damage: 6,
            description: "Deal 6 damage to target enemy",
            effects: []
        });
        
        this.registerCard({
            id: "critical_strike",
            name: "Critical Strike",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.RARE,
            cost: 3,
            damage: 8,
            description: "Deal 8 damage. Draw a card.",
            effects: ["draw_card"]
        });
        
        this.registerCard({
            id: "devastating_blow",
            name: "Devastating Blow",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.LEGENDARY,
            cost: 4,
            damage: 12,
            description: "Deal 12 damage to target enemy",
            effects: []
        });
        
        // Defense Cards
        this.registerCard({
            id: "basic_block",
            name: "Basic Block",
            type: this.cardTypes.DEFENSE,
            rarity: this.cardRarities.COMMON,
            cost: 1,
            block: 5,
            description: "Gain 5 block",
            effects: []
        });
        
        this.registerCard({
            id: "shield_wall",
            name: "Shield Wall",
            type: this.cardTypes.DEFENSE,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            block: 10,
            description: "Gain 10 block",
            effects: []
        });
        
        this.registerCard({
            id: "iron_defense",
            name: "Iron Defense",
            type: this.cardTypes.DEFENSE,
            rarity: this.cardRarities.RARE,
            cost: 2,
            block: 8,
            description: "Gain 8 block. Draw a card.",
            effects: ["draw_card"]
        });
        
        // Spell Cards
        this.registerCard({
            id: "heal",
            name: "Heal",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.COMMON,
            cost: 2,
            healing: 6,
            description: "Restore 6 health",
            effects: []
        });
        
        this.registerCard({
            id: "fireball",
            name: "Fireball",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.UNCOMMON,
            cost: 3,
            damage: 7,
            description: "Deal 7 fire damage to target enemy",
            effects: ["fire_damage"]
        });
        
        this.registerCard({
            id: "lightning_bolt",
            name: "Lightning Bolt",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 2,
            damage: 5,
            description: "Deal 5 damage to all enemies",
            effects: ["area_damage"]
        });
        
        this.registerCard({
            id: "time_warp",
            name: "Time Warp",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.LEGENDARY,
            cost: 4,
            description: "Take an extra turn",
            effects: ["extra_turn"]
        });
        
        // Item Cards
        this.registerCard({
            id: "health_potion",
            name: "Health Potion",
            type: this.cardTypes.ITEM,
            rarity: this.cardRarities.COMMON,
            cost: 1,
            healing: 4,
            description: "Restore 4 health",
            effects: []
        });
        
        this.registerCard({
            id: "strength_potion",
            name: "Strength Potion",
            type: this.cardTypes.ITEM,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            description: "Next attack deals +3 damage",
            effects: ["strength_buff"]
        });
        
        this.registerCard({
            id: "golden_apple",
            name: "Golden Apple",
            type: this.cardTypes.ITEM,
            rarity: this.cardRarities.RARE,
            cost: 3,
            healing: 8,
            description: "Restore 8 health and gain 5 block",
            effects: ["heal_and_block"]
        });

        // Warrior Class Cards
        this.registerCard({
            id: "berserker_rage",
            name: "Berserker Rage",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 3,
            description: "Double attack damage for 3 turns",
            effects: ["berserker_rage"],
            class: "warrior"
        });

        this.registerCard({
            id: "shield_slam",
            name: "Shield Slam",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            damage: 0, // Damage calculated from block
            description: "Deal damage equal to your current block",
            effects: ["damage_from_block"],
            class: "warrior"
        });

        this.registerCard({
            id: "battle_cry",
            name: "Battle Cry",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.COMMON,
            cost: 1,
            description: "Draw 2 cards and gain 1 energy",
            effects: ["draw_cards", "gain_energy"],
            class: "warrior"
        });

        this.registerCard({
            id: "armor_up",
            name: "Armor Up",
            type: this.cardTypes.DEFENSE,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            block: 8,
            description: "Gain 8 block and reduce damage by 1 this turn",
            effects: ["armor_buff"],
            class: "warrior"
        });

        // Mage Class Cards
        this.registerCard({
            id: "meteor",
            name: "Meteor",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 4,
            damage: 15,
            description: "Deal 15 damage to target and 5 to all others",
            effects: ["aoe_damage"],
            class: "mage"
        });

        this.registerCard({
            id: "ice_shard",
            name: "Ice Shard",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            damage: 6,
            description: "Deal 6 damage and freeze target for 1 turn",
            effects: ["freeze"],
            class: "mage"
        });

        this.registerCard({
            id: "mana_burn",
            name: "Mana Burn",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            description: "Remove 2 energy from target, deal damage equal to energy removed",
            effects: ["energy_drain"],
            class: "mage"
        });

        this.registerCard({
            id: "arcane_missiles",
            name: "Arcane Missiles",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 3,
            damage: 3,
            description: "Deal 3 damage 4 times to random targets",
            effects: ["multi_hit"],
            class: "mage"
        });

        // Rogue Class Cards
        this.registerCard({
            id: "backstab",
            name: "Backstab",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.UNCOMMON,
            cost: 3,
            damage: 12,
            description: "Deal 12 damage. Costs 0 if target hasn't acted",
            effects: ["first_strike_bonus"],
            class: "rogue"
        });

        this.registerCard({
            id: "stealth",
            name: "Stealth",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 2,
            description: "Become untargetable for 2 turns, +50% crit chance",
            effects: ["stealth_mode"],
            class: "rogue"
        });

        this.registerCard({
            id: "poison_cloud",
            name: "Poison Cloud",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 3,
            description: "All enemies take 3 poison damage for 3 turns",
            effects: ["aoe_poison"],
            class: "rogue"
        });

        this.registerCard({
            id: "dual_strike",
            name: "Dual Strike",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.COMMON,
            cost: 2,
            damage: 4,
            description: "Attack twice for 4 damage each",
            effects: ["double_attack"],
            class: "rogue"
        });

        // Paladin Class Cards
        this.registerCard({
            id: "divine_strike",
            name: "Divine Strike",
            type: this.cardTypes.ATTACK,
            rarity: this.cardRarities.UNCOMMON,
            cost: 3,
            damage: 8,
            description: "Deal 8 damage and heal for half damage dealt",
            effects: ["lifesteal"],
            class: "paladin"
        });

        this.registerCard({
            id: "consecration",
            name: "Consecration",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 4,
            damage: 4,
            healing: 4,
            description: "Deal 4 damage to all enemies, heal all allies for 4",
            effects: ["aoe_damage_heal"],
            class: "paladin"
        });

        this.registerCard({
            id: "lay_on_hands",
            name: "Lay on Hands",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 4,
            description: "Fully heal target and remove all debuffs",
            effects: ["full_heal", "cleanse"],
            class: "paladin"
        });

        this.registerCard({
            id: "sanctuary",
            name: "Sanctuary",
            type: this.cardTypes.DEFENSE,
            rarity: this.cardRarities.UNCOMMON,
            cost: 3,
            block: 6,
            description: "All allies gain 6 block and immunity to debuffs",
            effects: ["aoe_block", "immunity"],
            class: "paladin"
        });

        // Necromancer Class Cards
        this.registerCard({
            id: "raise_skeleton",
            name: "Raise Skeleton",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.UNCOMMON,
            cost: 3,
            description: "Summon a skeleton minion with 8 health",
            effects: ["summon_skeleton"],
            class: "necromancer"
        });

        this.registerCard({
            id: "death_coil",
            name: "Death Coil",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.UNCOMMON,
            cost: 2,
            damage: 6,
            healing: 6,
            description: "Deal 6 damage to enemy or heal ally for 6",
            effects: ["damage_or_heal"],
            class: "necromancer"
        });

        this.registerCard({
            id: "bone_armor",
            name: "Bone Armor",
            type: this.cardTypes.DEFENSE,
            rarity: this.cardRarities.RARE,
            cost: 2,
            description: "Gain 5 block for each enemy killed this combat",
            effects: ["block_from_deaths"],
            class: "necromancer"
        });

        this.registerCard({
            id: "soul_burn",
            name: "Soul Burn",
            type: this.cardTypes.SPELL,
            rarity: this.cardRarities.RARE,
            cost: 3,
            description: "Deal damage equal to target's missing health",
            effects: ["execute_damage"],
            class: "necromancer"
        });

        // Ultimate Cards
        this.loadUltimateCards();
    }

    /**
     * Load ultimate cards from UltimateCards module
     */
    loadUltimateCards() {
        // Import and register ultimate cards
        const ultimateCards = {
            // Warrior Ultimates
            berserker_wrath: {
                id: "berserker_wrath",
                name: "Berserker's Wrath",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 6,
                description: "For 3 turns: double attack damage, heal for 25% of damage dealt",
                effects: [{ type: "ultimate_ability", abilityId: "ultimate10" }],
                class: "warrior",
                levelRequirement: 10
            },

            avatar_of_war: {
                id: "avatar_of_war",
                name: "Avatar of War",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 8,
                description: "Enhanced Berserker's Wrath: +50% damage to all allies, immunity to damage for 1 turn",
                effects: [{ type: "ultimate_ability", abilityId: "master20" }],
                class: "warrior",
                levelRequirement: 20
            },

            // Mage Ultimates
            arcane_ascendance: {
                id: "arcane_ascendance",
                name: "Arcane Ascendance",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 5,
                description: "Gain 3 energy, draw 3 cards, next 3 spells cost 0 energy",
                effects: [{ type: "ultimate_ability", abilityId: "ultimate10" }],
                class: "mage",
                levelRequirement: 10
            },

            archmage_transcendence: {
                id: "archmage_transcendence",
                name: "Archmage Transcendence",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 6,
                description: "Enhanced Arcane Ascendance: +5 energy, draw 5 cards, next 5 spells free",
                effects: [{ type: "ultimate_ability", abilityId: "master20" }],
                class: "mage",
                levelRequirement: 20
            },

            // Rogue Ultimates
            shadow_clone: {
                id: "shadow_clone",
                name: "Shadow Clone",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 4,
                description: "Create a shadow clone that copies your next 3 attacks with 75% damage",
                effects: [{ type: "ultimate_ability", abilityId: "ultimate10" }],
                class: "rogue",
                levelRequirement: 10
            },

            master_assassin: {
                id: "master_assassin",
                name: "Master Assassin",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 6,
                description: "Enhanced Shadow Clone: 3 clones, 100% damage, lasts 5 attacks",
                effects: [{ type: "ultimate_ability", abilityId: "master20" }],
                class: "rogue",
                levelRequirement: 20
            },

            // Paladin Ultimates
            divine_intervention: {
                id: "divine_intervention",
                name: "Divine Intervention",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 6,
                description: "Fully heal all allies, grant immunity for 1 turn, cleanse debuffs",
                effects: [{ type: "ultimate_ability", abilityId: "ultimate10" }],
                class: "paladin",
                levelRequirement: 10
            },

            divine_avatar: {
                id: "divine_avatar",
                name: "Divine Avatar",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 8,
                description: "Enhanced Divine Intervention: immunity for 2 turns, +10 max health permanently",
                effects: [{ type: "ultimate_ability", abilityId: "master20" }],
                class: "paladin",
                levelRequirement: 20
            },

            // Necromancer Ultimates
            army_of_the_dead: {
                id: "army_of_the_dead",
                name: "Army of the Dead",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 5,
                description: "Summon 3 skeleton warriors, gain energy equal to enemies killed",
                effects: [{ type: "ultimate_ability", abilityId: "ultimate10" }],
                class: "necromancer",
                levelRequirement: 10
            },

            lich_lord: {
                id: "lich_lord",
                name: "Lich Lord",
                type: "ultimate",
                rarity: this.cardRarities.LEGENDARY,
                cost: 7,
                description: "Enhanced Army: 5 skeletons, gain 2 energy per kill, skeletons have 15 HP",
                effects: [{ type: "ultimate_ability", abilityId: "master20" }],
                class: "necromancer",
                levelRequirement: 20
            }
        };

        // Register all ultimate cards
        for (const card of Object.values(ultimateCards)) {
            this.registerCard(card);
        }
    }
    
    /**
     * Register a card in the database
     */
    registerCard(cardData) {
        this.cardDatabase.set(cardData.id, cardData);
    }
    
    /**
     * Get card by ID
     */
    getCard(cardId) {
        return this.cardDatabase.get(cardId);
    }
    
    /**
     * Get all cards of a specific type
     */
    getCardsByType(type) {
        return Array.from(this.cardDatabase.values()).filter(card => card.type === type);
    }
    
    /**
     * Get all cards of a specific rarity
     */
    getCardsByRarity(rarity) {
        return Array.from(this.cardDatabase.values()).filter(card => card.rarity === rarity);
    }
    
    /**
     * Get all available cards
     */
    getAllCards() {
        return Array.from(this.cardDatabase.values());
    }
    
    /**
     * Create a starter deck for new players
     */
    createStarterDeck() {
        return [
            "basic_strike", "basic_strike", "basic_strike",
            "power_attack", "power_attack",
            "basic_block", "basic_block", "basic_block",
            "shield_wall",
            "heal", "heal",
            "health_potion", "health_potion"
        ];
    }
    
    /**
     * Generate a random card based on rarity weights
     */
    generateRandomCard() {
        const rarityWeights = {
            [this.cardRarities.COMMON]: 60,
            [this.cardRarities.UNCOMMON]: 25,
            [this.cardRarities.RARE]: 12,
            [this.cardRarities.LEGENDARY]: 3
        };
        
        const totalWeight = Object.values(rarityWeights).reduce((sum, weight) => sum + weight, 0);
        const random = Math.random() * totalWeight;
        
        let currentWeight = 0;
        let selectedRarity = this.cardRarities.COMMON;
        
        for (const [rarity, weight] of Object.entries(rarityWeights)) {
            currentWeight += weight;
            if (random <= currentWeight) {
                selectedRarity = rarity;
                break;
            }
        }
        
        const cardsOfRarity = this.getCardsByRarity(selectedRarity);
        if (cardsOfRarity.length === 0) {
            return this.getCardsByRarity(this.cardRarities.COMMON)[0];
        }
        
        return cardsOfRarity[Math.floor(Math.random() * cardsOfRarity.length)];
    }
    
    /**
     * Validate if a deck is legal
     */
    validateDeck(deck) {
        const errors = [];
        
        // Check deck size
        if (deck.length < 10) {
            errors.push("Deck must contain at least 10 cards");
        }
        if (deck.length > 30) {
            errors.push("Deck cannot contain more than 30 cards");
        }
        
        // Check card limits (max 3 of any card except basic cards)
        const cardCounts = {};
        for (const cardId of deck) {
            cardCounts[cardId] = (cardCounts[cardId] || 0) + 1;
        }
        
        for (const [cardId, count] of Object.entries(cardCounts)) {
            const card = this.getCard(cardId);
            if (!card) {
                errors.push(`Unknown card: ${cardId}`);
                continue;
            }
            
            // Basic cards can have unlimited copies
            if (card.rarity !== this.cardRarities.COMMON && count > 3) {
                errors.push(`Too many copies of ${card.name} (max 3)`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * Shuffle a deck
     */
    shuffleDeck(deck) {
        const shuffled = [...deck];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    /**
     * Calculate card play cost
     */
    getCardCost(cardId) {
        const card = this.getCard(cardId);
        return card ? card.cost : 0;
    }
    
    /**
     * Get card rarity color
     */
    getRarityColor(rarity) {
        const colors = {
            [this.cardRarities.COMMON]: "§f",
            [this.cardRarities.UNCOMMON]: "§a",
            [this.cardRarities.RARE]: "§9",
            [this.cardRarities.LEGENDARY]: "§6"
        };
        return colors[rarity] || "§f";
    }
    
    /**
     * Format card for display
     */
    formatCard(cardId) {
        const card = this.getCard(cardId);
        if (!card) return "Unknown Card";
        
        const color = this.getRarityColor(card.rarity);
        return `${color}${card.name}§r (${card.cost})`;
    }
    
    /**
     * Get card tooltip
     */
    getCardTooltip(cardId) {
        const card = this.getCard(cardId);
        if (!card) return ["Unknown Card"];
        
        const tooltip = [
            `${this.getRarityColor(card.rarity)}${card.name}§r`,
            `§7Type: ${card.type}`,
            `§7Cost: ${card.cost}`,
            `§7${card.description}`
        ];
        
        if (card.damage) {
            tooltip.push(`§cDamage: ${card.damage}`);
        }
        if (card.block) {
            tooltip.push(`§9Block: ${card.block}`);
        }
        if (card.healing) {
            tooltip.push(`§aHealing: ${card.healing}`);
        }
        
        return tooltip;
    }
}
