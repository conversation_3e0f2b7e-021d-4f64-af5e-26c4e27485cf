#!/usr/bin/env python3
"""
Audio Asset Generator for Card Combat System
Generates placeholder audio files and provides download links for real assets
"""

import os
import wave
import struct
import math
import random
from pathlib import Path

class AudioGenerator:
    def __init__(self, sample_rate=44100):
        self.sample_rate = sample_rate
        self.output_dir = Path("resource_pack/sounds/combat")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_tone(self, frequency, duration, volume=0.5, fade_in=0.1, fade_out=0.1):
        """Generate a sine wave tone with fade in/out"""
        frames = int(duration * self.sample_rate)
        fade_in_frames = int(fade_in * self.sample_rate)
        fade_out_frames = int(fade_out * self.sample_rate)
        
        audio_data = []
        for i in range(frames):
            # Generate sine wave
            sample = math.sin(2 * math.pi * frequency * i / self.sample_rate)
            
            # Apply fade in
            if i < fade_in_frames:
                sample *= i / fade_in_frames
            
            # Apply fade out
            if i > frames - fade_out_frames:
                sample *= (frames - i) / fade_out_frames
            
            # Apply volume and convert to 16-bit
            sample = int(sample * volume * 32767)
            audio_data.append(sample)
        
        return audio_data
    
    def generate_noise(self, duration, volume=0.3, filter_freq=None):
        """Generate filtered noise"""
        frames = int(duration * self.sample_rate)
        audio_data = []
        
        for i in range(frames):
            # Generate white noise
            sample = random.uniform(-1, 1)
            
            # Simple low-pass filter if specified
            if filter_freq:
                # Basic exponential smoothing
                if i > 0:
                    alpha = 2 * math.pi * filter_freq / self.sample_rate
                    sample = alpha * sample + (1 - alpha) * (audio_data[-1] / 32767)
            
            # Apply volume and convert to 16-bit
            sample = int(sample * volume * 32767)
            audio_data.append(sample)
        
        return audio_data
    
    def generate_sweep(self, start_freq, end_freq, duration, volume=0.5):
        """Generate frequency sweep"""
        frames = int(duration * self.sample_rate)
        audio_data = []
        
        for i in range(frames):
            # Linear frequency interpolation
            progress = i / frames
            frequency = start_freq + (end_freq - start_freq) * progress
            
            # Generate sine wave
            sample = math.sin(2 * math.pi * frequency * i / self.sample_rate)
            
            # Apply volume and convert to 16-bit
            sample = int(sample * volume * 32767)
            audio_data.append(sample)
        
        return audio_data
    
    def save_wav(self, audio_data, filename):
        """Save audio data as WAV file"""
        filepath = self.output_dir / f"{filename}.wav"
        
        with wave.open(str(filepath), 'w') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(self.sample_rate)
            
            # Pack audio data as 16-bit signed integers
            packed_data = struct.pack('<' + 'h' * len(audio_data), *audio_data)
            wav_file.writeframes(packed_data)
        
        print(f"Generated: {filepath}")
        return filepath
    
    def generate_card_sounds(self):
        """Generate card-related sounds"""
        print("Generating card sounds...")
        
        # Card play sounds (3 variations)
        # card_play1: Quick paper slide with magical shimmer
        tone1 = self.generate_tone(800, 0.2, 0.3)
        tone2 = self.generate_tone(1200, 0.15, 0.2)
        shimmer = self.generate_sweep(2000, 3000, 0.05, 0.1)
        card_play1 = tone1 + tone2 + shimmer
        self.save_wav(card_play1, "card_play1")
        
        # card_play2: Higher pitch variation
        tone1 = self.generate_tone(1000, 0.2, 0.3)
        tone2 = self.generate_tone(1400, 0.15, 0.2)
        shimmer = self.generate_sweep(2200, 3200, 0.05, 0.1)
        card_play2 = tone1 + tone2 + shimmer
        self.save_wav(card_play2, "card_play2")
        
        # card_play3: Quick and responsive
        tone1 = self.generate_tone(900, 0.15, 0.4)
        shimmer = self.generate_sweep(2500, 3500, 0.03, 0.15)
        card_play3 = tone1 + shimmer
        self.save_wav(card_play3, "card_play3")
        
        # card_draw: Smooth sliding sound
        slide = self.generate_sweep(400, 600, 0.4, 0.3)
        gentle = self.generate_tone(1000, 0.2, 0.1)
        card_draw = slide + gentle
        self.save_wav(card_draw, "card_draw")
    
    def generate_combat_sounds(self):
        """Generate combat event sounds"""
        print("Generating combat sounds...")
        
        # combat_start: Epic buildup
        low_tone = self.generate_tone(100, 1.0, 0.4)
        mid_tone = self.generate_tone(200, 1.5, 0.3)
        high_sweep = self.generate_sweep(400, 800, 0.5, 0.2)
        combat_start = low_tone + mid_tone + high_sweep
        self.save_wav(combat_start, "combat_start")
        
        # combat_end: Resolution chord
        chord1 = self.generate_tone(200, 1.0, 0.3)
        chord2 = self.generate_tone(300, 1.0, 0.2)
        chord3 = self.generate_tone(400, 1.0, 0.1)
        resolution = self.generate_tone(250, 0.8, 0.2)
        combat_end = [sum(x) // 3 for x in zip(chord1, chord2, chord3)] + resolution
        self.save_wav(combat_end, "combat_end")
        
        # turn_start: Gentle chime
        chime1 = self.generate_tone(800, 0.3, 0.3)
        chime2 = self.generate_tone(1000, 0.3, 0.2)
        chime3 = self.generate_tone(1200, 0.2, 0.1)
        turn_start = chime1 + chime2 + chime3
        self.save_wav(turn_start, "turn_start")
    
    def generate_action_sounds(self):
        """Generate action sounds"""
        print("Generating action sounds...")
        
        # Attack hits (3 variations)
        # attack_hit1: Satisfying impact
        impact = self.generate_noise(0.1, 0.6, 500)
        ring = self.generate_tone(1500, 0.2, 0.2)
        attack_hit1 = impact + ring
        self.save_wav(attack_hit1, "attack_hit1")
        
        # attack_hit2: Sharper focus
        impact = self.generate_noise(0.08, 0.7, 800)
        ring = self.generate_tone(1800, 0.15, 0.25)
        attack_hit2 = impact + ring
        self.save_wav(attack_hit2, "attack_hit2")
        
        # attack_hit3: Heaviest impact
        impact = self.generate_noise(0.15, 0.8, 300)
        ring = self.generate_tone(1200, 0.3, 0.3)
        attack_hit3 = impact + ring
        self.save_wav(attack_hit3, "attack_hit3")
        
        # block_activate: Crystalline barrier
        crystal = self.generate_sweep(1000, 2000, 0.3, 0.3)
        barrier = self.generate_tone(1500, 0.4, 0.2)
        block_activate = crystal + barrier
        self.save_wav(block_activate, "block_activate")
        
        # Spell casting sounds
        # spell_cast1: Arcane energy buildup
        buildup = self.generate_sweep(200, 1000, 0.6, 0.3)
        release = self.generate_sweep(1000, 2000, 0.3, 0.4)
        sparkle = self.generate_noise(0.3, 0.2, 2000)
        spell_cast1 = buildup + release + sparkle
        self.save_wav(spell_cast1, "spell_cast1")
        
        # spell_cast2: Focused magic
        focus = self.generate_tone(800, 0.4, 0.4)
        burst = self.generate_sweep(800, 1600, 0.3, 0.3)
        echo = self.generate_tone(1200, 0.3, 0.1)
        spell_cast2 = focus + burst + echo
        self.save_wav(spell_cast2, "spell_cast2")
        
        # heal: Warm restoration
        warm1 = self.generate_tone(400, 0.5, 0.3)
        warm2 = self.generate_tone(600, 0.5, 0.2)
        warm3 = self.generate_tone(800, 0.5, 0.1)
        glow = self.generate_sweep(800, 1200, 0.5, 0.15)
        heal = [sum(x) // 3 for x in zip(warm1, warm2, warm3)] + glow
        self.save_wav(heal, "heal")
    
    def generate_ui_sounds(self):
        """Generate UI sounds"""
        print("Generating UI sounds...")
        
        # pack_open: Exciting revelation
        anticipation = self.generate_sweep(200, 800, 0.8, 0.3)
        burst = self.generate_noise(0.2, 0.4, 1000)
        sparkles = self.generate_sweep(1000, 2000, 0.8, 0.2)
        pack_open = anticipation + burst + sparkles
        self.save_wav(pack_open, "pack_open")
        
        # rare_card: Prestigious feeling
        fanfare1 = self.generate_tone(400, 0.8, 0.3)
        fanfare2 = self.generate_tone(600, 0.8, 0.2)
        fanfare3 = self.generate_tone(800, 0.6, 0.1)
        rare_card = [sum(x) // 3 for x in zip(fanfare1, fanfare2, fanfare3)]
        self.save_wav(rare_card, "rare_card")
        
        # legendary_card: Epic achievement
        epic1 = self.generate_tone(200, 1.0, 0.4)
        epic2 = self.generate_tone(400, 1.0, 0.3)
        epic3 = self.generate_tone(600, 1.0, 0.2)
        epic4 = self.generate_tone(800, 1.0, 0.1)
        crescendo = self.generate_sweep(800, 1600, 1.0, 0.3)
        legendary_card = [sum(x) // 4 for x in zip(epic1, epic2, epic3, epic4)] + crescendo
        self.save_wav(legendary_card, "legendary_card")
        
        # UI feedback sounds
        # ui_hover: Subtle feedback
        hover = self.generate_tone(1200, 0.1, 0.1)
        self.save_wav(hover, "ui_hover")
        
        # ui_click: Confirmation
        click = self.generate_tone(800, 0.15, 0.3)
        confirm = self.generate_tone(1000, 0.1, 0.2)
        ui_click = click + confirm
        self.save_wav(ui_click, "ui_click")
        
        # deck_shuffle: Card movement
        shuffle1 = self.generate_noise(0.5, 0.2, 400)
        shuffle2 = self.generate_noise(0.5, 0.15, 600)
        magic_end = self.generate_tone(1000, 0.5, 0.1)
        deck_shuffle = shuffle1 + shuffle2 + magic_end
        self.save_wav(deck_shuffle, "deck_shuffle")
        
        # energy_gain: Empowering progression
        rise = self.generate_sweep(400, 1200, 0.6, 0.3)
        power = self.generate_tone(1000, 0.2, 0.2)
        energy_gain = rise + power
        self.save_wav(energy_gain, "energy_gain")
        
        # status_effect: Mystical application
        mystical = self.generate_sweep(600, 1800, 0.5, 0.2)
        ethereal = self.generate_tone(1200, 0.5, 0.15)
        status_effect = mystical + ethereal
        self.save_wav(status_effect, "status_effect")
    
    def generate_all(self):
        """Generate all audio assets"""
        print("Starting audio generation...")
        print(f"Output directory: {self.output_dir}")
        
        self.generate_card_sounds()
        self.generate_combat_sounds()
        self.generate_action_sounds()
        self.generate_ui_sounds()
        
        print("\n✅ All audio assets generated!")
        print(f"📁 Files saved to: {self.output_dir}")
        print("\n🔄 Next steps:")
        print("1. Convert WAV files to OGG format using Audacity or online converter")
        print("2. Place OGG files in resource_pack/sounds/combat/ directory")
        print("3. Test with: !combat testsounds")

def main():
    """Main function"""
    generator = AudioGenerator()
    generator.generate_all()

if __name__ == "__main__":
    main()
