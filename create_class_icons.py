#!/usr/bin/env python3
"""
Create class icon textures for the Card Combat System
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_class_icon(class_name, color, symbol, output_path):
    """Create a 32x32 class icon with colored background and symbol"""
    
    # Create 32x32 image
    img = Image.new('RGBA', (32, 32), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw colored circle background
    draw.ellipse([2, 2, 30, 30], fill=color, outline=(255, 255, 255, 255), width=2)
    
    # Try to load a font, fallback to default
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # Draw symbol in center
    bbox = draw.textbbox((0, 0), symbol, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (32 - text_width) // 2
    y = (32 - text_height) // 2 - 2
    
    draw.text((x, y), symbol, fill=(255, 255, 255, 255), font=font)
    
    # Save the image
    img.save(output_path, 'PNG')
    print(f"Created {class_name} icon: {output_path}")

def main():
    # Create output directory
    output_dir = "resource_pack/textures/ui/class_icons"
    os.makedirs(output_dir, exist_ok=True)
    
    # Class definitions: (name, color, symbol)
    classes = [
        ("warrior", (200, 50, 50, 255), "⚔"),      # Red - Sword
        ("mage", (50, 100, 200, 255), "✦"),        # Blue - Star
        ("rogue", (100, 50, 150, 255), "🗡"),      # Purple - Dagger
        ("paladin", (255, 215, 0, 255), "✚"),      # Gold - Cross
        ("necromancer", (50, 50, 50, 255), "☠"),   # Dark - Skull
    ]
    
    # Create icons
    for class_name, color, symbol in classes:
        output_path = os.path.join(output_dir, f"{class_name}.png")
        create_class_icon(class_name, color, symbol, output_path)
    
    print(f"\n✅ Created {len(classes)} class icons!")

if __name__ == "__main__":
    main()
