/**
 * CombatUIController - Advanced UI controller for combat interface
 */

import { ActionFormData, ModalFormData } from "@minecraft/server-ui";
import { world, system } from "@minecraft/server";

export class CombatUIController {
    constructor(cardManager, playerDataManager) {
        this.cardManager = cardManager;
        this.playerDataManager = playerDataManager;
        
        // UI state tracking
        this.activeUIs = new Map();
        this.uiUpdateIntervals = new Map();
        
        // Animation settings
        this.animationDuration = 20; // ticks
        this.cardHoverScale = 1.1;
    }
    
    /**
     * Show enhanced combat UI with real-time updates
     */
    async showEnhancedCombatUI(player, combatSession) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        // Create main combat form
        const form = new ActionFormData()
            .title(this.formatTitle("⚔ COMBAT TURN ⚔", "gold"))
            .body(this.buildEnhancedCombatInfo(player, combatSession, playerData));
        
        // Add card buttons with enhanced formatting
        for (let i = 0; i < playerData.currentHand.length; i++) {
            const cardId = playerData.currentHand[i];
            const card = this.cardManager.getCard(cardId);
            if (card) {
                const canPlay = playerData.currentEnergy >= card.cost;
                const cardButton = this.formatCardButton(card, canPlay, i);
                form.button(cardButton.text, cardButton.icon);
            }
        }
        
        // Add action buttons
        form.button(this.formatActionButton("End Turn", "green"), "textures/ui/check");
        form.button(this.formatActionButton("View Deck", "blue"), "textures/items/book_normal");
        form.button(this.formatActionButton("Surrender", "red"), "textures/ui/cancel");
        
        try {
            // Store UI state
            this.activeUIs.set(player.id, {
                type: "combat",
                combatSession,
                timestamp: Date.now()
            });
            
            const response = await form.show(player);
            if (response.canceled) {
                this.handleCombatUICancel(player, combatSession);
                return;
            }
            
            this.handleEnhancedCombatUIResponse(player, combatSession, response.selection, playerData);
            
        } catch (error) {
            console.error("Error showing enhanced combat UI:", error);
            this.fallbackCombatUI(player, combatSession);
        } finally {
            this.activeUIs.delete(player.id);
        }
    }
    
    /**
     * Handle enhanced combat UI responses
     */
    handleEnhancedCombatUIResponse(player, combatSession, selection, playerData) {
        const handSize = playerData.currentHand.length;
        
        if (selection < handSize) {
            // Card selected
            const cardId = playerData.currentHand[selection];
            this.handleCardSelection(player, combatSession, cardId, selection);
        } else {
            // Action button selected
            const actionIndex = selection - handSize;
            switch (actionIndex) {
                case 0: // End Turn
                    this.handleEndTurn(player, combatSession);
                    break;
                case 1: // View Deck
                    this.showDeckViewer(player, combatSession);
                    break;
                case 2: // Surrender
                    this.handleSurrender(player, combatSession);
                    break;
            }
        }
    }
    
    /**
     * Handle card selection with enhanced feedback
     */
    async handleCardSelection(player, combatSession, cardId, cardIndex) {
        const card = this.cardManager.getCard(cardId);
        const playerData = this.playerDataManager.getPlayerData(player);
        
        if (!card || !playerData) return;
        
        // Check if card can be played
        if (playerData.currentEnergy < card.cost) {
            this.showErrorMessage(player, "Not enough energy!", "red");
            this.showEnhancedCombatUI(player, combatSession);
            return;
        }
        
        // Show card confirmation with details
        const confirmForm = new ModalFormData()
            .title(this.formatTitle(`Play ${card.name}?`, "yellow"))
            .toggle(`Confirm playing ${card.name} for ${card.cost} energy`, false);
        
        try {
            const response = await confirmForm.show(player);
            if (response.canceled || !response.formValues[0]) {
                this.showEnhancedCombatUI(player, combatSession);
                return;
            }
            
            // Play the card with animation
            await this.playCardWithAnimation(player, combatSession, cardId);
            
        } catch (error) {
            console.error("Error in card selection:", error);
            this.showEnhancedCombatUI(player, combatSession);
        }
    }
    
    /**
     * Play card with visual feedback and animation
     */
    async playCardWithAnimation(player, combatSession, cardId) {
        const card = this.cardManager.getCard(cardId);
        if (!card) return;
        
        // Show playing animation
        this.showStatusMessage(player, `Playing ${card.name}...`, "yellow");
        
        // Remove card from hand and spend energy
        const success = this.playerDataManager.playCard(player, cardId);
        if (!success) {
            this.showErrorMessage(player, "Failed to play card!", "red");
            return;
        }
        
        // Apply card effects with visual feedback
        await this.applyCardEffectsWithFeedback(player, combatSession, card);
        
        // Show updated UI after delay
        system.runTimeout(() => {
            this.showEnhancedCombatUI(player, combatSession);
        }, 40); // 2 second delay
    }
    
    /**
     * Apply card effects with enhanced visual feedback
     */
    async applyCardEffectsWithFeedback(player, combatSession, card) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        // Get target if needed
        let target = null;
        if (card.type === "attack" || (card.type === "spell" && card.damage)) {
            target = await this.selectTargetWithPreview(player, combatSession, card);
            if (!target) {
                this.showErrorMessage(player, "No target selected!", "red");
                return;
            }
        }
        
        // Apply effects with visual feedback
        switch (card.type) {
            case "attack":
                await this.applyAttackWithFeedback(player, target, card);
                break;
            case "defense":
                await this.applyDefenseWithFeedback(player, card);
                break;
            case "spell":
                await this.applySpellWithFeedback(player, combatSession, card, target);
                break;
            case "item":
                await this.applyItemWithFeedback(player, card);
                break;
        }
        
        // Apply additional effects
        for (const effect of card.effects || []) {
            await this.applySpecialEffectWithFeedback(player, combatSession, effect);
        }
        
        // Update player data
        this.playerDataManager.updatePlayerData(player, playerData);
    }
    
    /**
     * Select target with damage preview
     */
    async selectTargetWithPreview(player, combatSession, card) {
        const enemies = combatSession.participants.filter(p => p !== player && p.isValid());
        
        if (enemies.length === 0) return null;
        if (enemies.length === 1) return enemies[0];
        
        // Show target selection with damage preview
        const form = new ActionFormData()
            .title(this.formatTitle("Select Target", "red"))
            .body(`${card.name} will deal ${card.damage || 0} damage`);
        
        for (const enemy of enemies) {
            const health = enemy.getComponent("health");
            const healthText = health ? `${health.currentValue}/${health.defaultValue}` : "?/?";
            const damagePreview = card.damage ? ` → ${Math.max(0, health.currentValue - card.damage)}` : "";
            
            form.button(
                `${enemy.nameTag || enemy.typeId}\n§7HP: ${healthText}${damagePreview}`,
                "textures/ui/crosshair"
            );
        }
        
        try {
            const response = await form.show(player);
            if (response.canceled || response.selection === undefined) {
                return null;
            }
            return enemies[response.selection];
        } catch (error) {
            console.error("Error selecting target:", error);
            return null;
        }
    }
    
    /**
     * Apply attack with enhanced feedback
     */
    async applyAttackWithFeedback(player, target, card) {
        if (!target || !card.damage) return;
        
        // Calculate final damage
        const baseDamage = card.damage;
        const modifiers = this.calculateDamageModifiers(player, target);
        const finalDamage = Math.max(1, baseDamage + modifiers);
        
        // Show damage calculation
        this.showDamageCalculation(player, baseDamage, modifiers, finalDamage);
        
        // Apply damage
        this.dealDamageWithFeedback(target, finalDamage, player);
        
        // Show result
        const targetName = target.nameTag || target.typeId;
        this.showStatusMessage(player, `Dealt ${finalDamage} damage to ${targetName}!`, "red");
    }
    
    /**
     * Apply defense with feedback
     */
    async applyDefenseWithFeedback(player, card) {
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData || !card.block) return;
        
        playerData.block += card.block;
        this.showStatusMessage(player, `Gained ${card.block} block!`, "blue");
        
        // Visual effect
        player.dimension.spawnParticle("minecraft:villager_happy", player.location);
    }
    
    /**
     * Show damage calculation breakdown
     */
    showDamageCalculation(player, baseDamage, modifiers, finalDamage) {
        if (modifiers === 0) {
            player.sendMessage(`§7Damage: ${baseDamage}`);
        } else {
            const modifierText = modifiers > 0 ? `+${modifiers}` : `${modifiers}`;
            player.sendMessage(`§7Damage: ${baseDamage} ${modifierText} = §c${finalDamage}`);
        }
    }
    
    /**
     * Calculate damage modifiers
     */
    calculateDamageModifiers(attacker, target) {
        let modifiers = 0;
        
        // Get attacker modifiers
        const attackerData = this.playerDataManager.getPlayerData(attacker);
        if (attackerData) {
            const strength = attackerData.statusEffects.get("strength");
            if (strength) {
                modifiers += strength.amount;
            }
            
            const weakness = attackerData.statusEffects.get("weakness");
            if (weakness) {
                modifiers -= weakness.amount;
            }
        }
        
        return modifiers;
    }
    
    /**
     * Deal damage with visual feedback
     */
    dealDamageWithFeedback(target, damage, source) {
        const health = target.getComponent("health");
        if (!health) return;
        
        const oldHealth = health.currentValue;
        health.setCurrentValue(Math.max(0, oldHealth - damage));
        const newHealth = health.currentValue;
        
        // Visual effects
        target.dimension.spawnParticle("minecraft:critical_hit_emitter", target.location);
        
        // Damage numbers (simulated with particles)
        for (let i = 0; i < damage; i++) {
            system.runTimeout(() => {
                target.dimension.spawnParticle("minecraft:lava_particle", {
                    x: target.location.x + (Math.random() - 0.5),
                    y: target.location.y + 1 + Math.random(),
                    z: target.location.z + (Math.random() - 0.5)
                });
            }, i * 2);
        }
    }
    
    /**
     * Formatting utilities
     */
    
    formatTitle(text, color) {
        const colors = {
            gold: "§6",
            red: "§c",
            green: "§a",
            blue: "§9",
            yellow: "§e",
            white: "§f"
        };
        return `${colors[color] || "§f"}${text}§r`;
    }
    
    formatCardButton(card, canPlay, index) {
        const costColor = canPlay ? "§a" : "§c";
        const nameColor = this.cardManager.getRarityColor(card.rarity);
        
        return {
            text: `${nameColor}${card.name}§r\n${costColor}Cost: ${card.cost}§r\n§7${card.description}`,
            icon: canPlay ? undefined : "textures/ui/cancel"
        };
    }
    
    formatActionButton(text, color) {
        const colors = {
            green: "§a",
            blue: "§9",
            red: "§c",
            yellow: "§e"
        };
        return `${colors[color] || "§f"}${text}§r`;
    }
    
    buildEnhancedCombatInfo(player, combatSession, playerData) {
        const lines = [
            `§6═══ ROUND ${combatSession.round} ═══`,
            "",
            `§eEnergy: §b${playerData.currentEnergy}§7/§b${playerData.maxEnergy}`,
            `§9Block: §b${playerData.block}`,
            ""
        ];
        
        // Status effects
        if (playerData.statusEffects.size > 0) {
            lines.push("§6Status Effects:");
            for (const [effect, data] of playerData.statusEffects) {
                lines.push(`  §7${effect}: ${data.duration} turns`);
            }
            lines.push("");
        }
        
        // Participants
        lines.push("§6Combatants:");
        for (const participant of combatSession.participants) {
            if (!participant.isValid()) continue;
            
            const health = participant.getComponent("health");
            const healthText = health ? `${health.currentValue}/${health.defaultValue}` : "?/?";
            const isCurrentTurn = combatSession.turnOrder[combatSession.currentTurn] === participant;
            const turnIndicator = isCurrentTurn ? "§e► " : "  ";
            const nameColor = participant === player ? "§a" : "§c";
            
            lines.push(`${turnIndicator}${nameColor}${participant.nameTag || participant.typeId}§r: ${healthText} HP`);
        }
        
        lines.push("");
        lines.push("§7Choose a card to play:");
        
        return lines.join("\n");
    }
    
    /**
     * Utility message methods
     */
    
    showStatusMessage(player, message, color = "white") {
        const colors = {
            red: "§c",
            green: "§a",
            blue: "§9",
            yellow: "§e",
            white: "§f"
        };
        player.sendMessage(`${colors[color]}${message}§r`);
    }
    
    showErrorMessage(player, message, color = "red") {
        this.showStatusMessage(player, `✗ ${message}`, color);
    }
    
    /**
     * Fallback methods
     */
    
    fallbackCombatUI(player, combatSession) {
        // Simple fallback UI if enhanced UI fails
        player.sendMessage("§cUI Error - Using fallback interface");
        player.sendMessage("§7Use chat commands: !attack, !defend, !end");
    }
    
    handleCombatUICancel(player, combatSession) {
        player.sendMessage("§7Combat UI closed. Use !combat to reopen.");
    }
    
    handleEndTurn(player, combatSession) {
        world.combatManager?.endTurn(combatSession);
    }
    
    async showDeckViewer(player, combatSession) {
        // Show current deck during combat
        const playerData = this.playerDataManager.getPlayerData(player);
        if (!playerData) return;
        
        const deckText = playerData.activeDeck.map(cardId => {
            const card = this.cardManager.getCard(cardId);
            return card ? this.cardManager.formatCard(cardId) : cardId;
        }).join("\n");
        
        const form = new MessageFormData()
            .title("§6Current Deck")
            .body(`§7Your deck:\n\n${deckText}`)
            .button1("§aBack to Combat")
            .button2("§7Close");
        
        try {
            const response = await form.show(player);
            if (response.selection === 0) {
                this.showEnhancedCombatUI(player, combatSession);
            }
        } catch (error) {
            console.error("Error showing deck viewer:", error);
        }
    }
    
    async handleSurrender(player, combatSession) {
        const form = new ModalFormData()
            .title("§cSurrender?")
            .toggle("Are you sure you want to surrender this combat?", false);
        
        try {
            const response = await form.show(player);
            if (response.canceled || !response.formValues[0]) {
                this.showEnhancedCombatUI(player, combatSession);
                return;
            }
            
            // Handle surrender
            world.combatManager?.endCombat(combatSession, "surrender");
            
        } catch (error) {
            console.error("Error handling surrender:", error);
        }
    }
    
    // Placeholder methods for special effects
    async applySpellWithFeedback(player, combatSession, card, target) {
        // Implementation for spell effects with feedback
        this.showStatusMessage(player, `Cast ${card.name}!`, "blue");
    }
    
    async applyItemWithFeedback(player, card) {
        // Implementation for item effects with feedback
        this.showStatusMessage(player, `Used ${card.name}!`, "green");
    }
    
    async applySpecialEffectWithFeedback(player, combatSession, effect) {
        // Implementation for special effects with feedback
        this.showStatusMessage(player, `Applied effect: ${effect}`, "yellow");
    }
}
