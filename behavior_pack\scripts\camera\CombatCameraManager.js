/**
 * CombatCameraManager - Handles top-down camera view for combat encounters
 */

import { world, system } from "@minecraft/server";

export class CombatCameraManager {
    constructor() {
        this.activeCameras = new Map(); // player.id -> camera state
        this.cameraTransitions = new Map(); // player.id -> transition data
        
        // Camera configuration
        this.config = {
            // Height above combat area
            combatHeight: 12,
            maxHeight: 20,
            minHeight: 8,
            
            // Camera angles (in degrees)
            pitch: -85, // Looking almost straight down
            yaw: 0,     // North-facing by default
            
            // Transition settings
            transitionDuration: 40, // ticks (2 seconds)
            smoothingFactor: 0.15,
            
            // Combat area calculation
            participantPadding: 3, // blocks around participants
            minAreaSize: 8,        // minimum combat area size
            maxAreaSize: 16,       // maximum combat area size
            
            // Camera lock settings
            lockMovement: true,
            allowZoom: false,
            
            // UI adjustments
            uiOffsetY: 0.2, // Adjust UI positioning for overhead view
        };
    }
    
    /**
     * Start combat camera mode for a player
     */
    startCombatCamera(player, combatSession) {
        if (!player || !player.isValid()) return false;
        
        try {
            // Store original camera state
            const originalState = this.captureOriginalCameraState(player);
            
            // Calculate optimal camera position
            const cameraPosition = this.calculateCombatCameraPosition(combatSession);
            const cameraRotation = this.calculateCombatCameraRotation(combatSession);
            
            // Create camera state
            const cameraState = {
                playerId: player.id,
                combatSessionId: combatSession.id,
                isActive: true,
                originalState: originalState,
                targetPosition: cameraPosition,
                targetRotation: cameraRotation,
                currentPosition: { ...originalState.position },
                currentRotation: { ...originalState.rotation },
                transitionProgress: 0,
                lastUpdate: Date.now()
            };
            
            this.activeCameras.set(player.id, cameraState);
            
            // Start transition
            this.startCameraTransition(player, cameraState);
            
            // Lock player movement during combat
            if (this.config.lockMovement) {
                this.lockPlayerMovement(player);
            }
            
            // Adjust UI for overhead view
            this.adjustUIForCombatCamera(player);
            
            player.sendMessage("§7Camera switching to combat view...");
            return true;
            
        } catch (error) {
            console.error("Failed to start combat camera:", error);
            return false;
        }
    }
    
    /**
     * End combat camera mode for a player
     */
    endCombatCamera(player) {
        if (!player || !player.isValid()) return false;
        
        const cameraState = this.activeCameras.get(player.id);
        if (!cameraState) return false;
        
        try {
            // Start transition back to original camera
            this.startReturnTransition(player, cameraState);
            
            // Unlock player movement
            this.unlockPlayerMovement(player);
            
            // Restore original UI
            this.restoreOriginalUI(player);
            
            player.sendMessage("§7Camera returning to normal view...");
            return true;
            
        } catch (error) {
            console.error("Failed to end combat camera:", error);
            return false;
        }
    }
    
    /**
     * Update camera positions (called every tick)
     */
    updateCameras() {
        for (const [playerId, cameraState] of this.activeCameras) {
            const player = world.getPlayers().find(p => p.id === playerId);
            if (!player || !player.isValid()) {
                this.activeCameras.delete(playerId);
                continue;
            }
            
            this.updateCameraState(player, cameraState);
        }
    }
    
    /**
     * Calculate optimal camera position for combat
     */
    calculateCombatCameraPosition(combatSession) {
        const participants = combatSession.participants.filter(p => p.isValid());
        if (participants.length === 0) {
            return { x: 0, y: 70, z: 0 };
        }
        
        // Calculate bounding box of all participants
        let minX = Infinity, maxX = -Infinity;
        let minZ = Infinity, maxZ = -Infinity;
        let avgY = 0;
        
        for (const participant of participants) {
            const pos = participant.location;
            minX = Math.min(minX, pos.x);
            maxX = Math.max(maxX, pos.x);
            minZ = Math.min(minZ, pos.z);
            maxZ = Math.max(maxZ, pos.z);
            avgY += pos.y;
        }
        
        avgY /= participants.length;
        
        // Add padding around participants
        const padding = this.config.participantPadding;
        minX -= padding;
        maxX += padding;
        minZ -= padding;
        maxZ += padding;
        
        // Calculate center of combat area
        const centerX = (minX + maxX) / 2;
        const centerZ = (minZ + maxZ) / 2;
        
        // Calculate required height based on area size
        const areaWidth = maxX - minX;
        const areaDepth = maxZ - minZ;
        const maxDimension = Math.max(areaWidth, areaDepth);
        
        // Scale height based on area size
        let height = this.config.combatHeight;
        if (maxDimension > this.config.minAreaSize) {
            height = Math.min(
                this.config.maxHeight,
                this.config.combatHeight + (maxDimension - this.config.minAreaSize) * 0.5
            );
        }
        
        return {
            x: centerX,
            y: avgY + height,
            z: centerZ
        };
    }
    
    /**
     * Calculate camera rotation for combat view
     */
    calculateCombatCameraRotation(combatSession) {
        // For now, use fixed top-down rotation
        // Could be enhanced to face the most important participant
        return {
            pitch: this.config.pitch,
            yaw: this.config.yaw
        };
    }
    
    /**
     * Capture original camera state
     */
    captureOriginalCameraState(player) {
        const rotation = player.getRotation();
        const location = player.location;
        
        return {
            position: { ...location },
            rotation: { 
                pitch: rotation.x, 
                yaw: rotation.y 
            },
            viewMode: player.getGameMode(), // Store game mode as proxy for view mode
            timestamp: Date.now()
        };
    }
    
    /**
     * Start camera transition
     */
    startCameraTransition(player, cameraState) {
        cameraState.transitionProgress = 0;
        cameraState.isTransitioning = true;
        
        // Store transition in separate map for smooth updates
        this.cameraTransitions.set(player.id, {
            startTime: Date.now(),
            duration: this.config.transitionDuration,
            type: "to_combat"
        });
    }
    
    /**
     * Start return transition
     */
    startReturnTransition(player, cameraState) {
        cameraState.isReturning = true;
        cameraState.transitionProgress = 0;
        
        // Set target back to original position
        cameraState.targetPosition = { ...cameraState.originalState.position };
        cameraState.targetRotation = { ...cameraState.originalState.rotation };
        
        this.cameraTransitions.set(player.id, {
            startTime: Date.now(),
            duration: this.config.transitionDuration,
            type: "to_normal"
        });
    }
    
    /**
     * Update individual camera state
     */
    updateCameraState(player, cameraState) {
        const transition = this.cameraTransitions.get(player.id);
        
        if (transition) {
            const elapsed = Date.now() - transition.startTime;
            const progress = Math.min(1, elapsed / (transition.duration * 50)); // Convert ticks to ms
            
            // Use easing function for smooth transition
            const easedProgress = this.easeInOutCubic(progress);
            
            // Interpolate position
            cameraState.currentPosition = this.interpolatePosition(
                cameraState.currentPosition,
                cameraState.targetPosition,
                easedProgress
            );
            
            // Interpolate rotation
            cameraState.currentRotation = this.interpolateRotation(
                cameraState.currentRotation,
                cameraState.targetRotation,
                easedProgress
            );
            
            // Apply camera position
            this.applyCameraTransform(player, cameraState);
            
            // Check if transition is complete
            if (progress >= 1) {
                this.cameraTransitions.delete(player.id);
                
                if (transition.type === "to_normal") {
                    // Transition back to normal is complete
                    this.activeCameras.delete(player.id);
                } else {
                    // Transition to combat is complete
                    cameraState.isTransitioning = false;
                }
            }
        } else if (cameraState.isActive && !cameraState.isReturning) {
            // Combat camera is active, apply current transform
            this.applyCameraTransform(player, cameraState);
        }
    }
    
    /**
     * Apply camera transform to player
     */
    applyCameraTransform(player, cameraState) {
        try {
            // Set player rotation (this affects camera angle)
            player.setRotation({
                x: cameraState.currentRotation.pitch,
                y: cameraState.currentRotation.yaw
            });
            
            // Teleport player to camera position
            // Note: In Bedrock, we can't separate camera from player position completely
            // So we teleport the player to the camera position
            player.teleport(cameraState.currentPosition, {
                dimension: player.dimension,
                rotation: {
                    x: cameraState.currentRotation.pitch,
                    y: cameraState.currentRotation.yaw
                },
                facingLocation: undefined,
                checkForBlocks: false,
                keepVelocity: false
            });
            
        } catch (error) {
            console.error("Failed to apply camera transform:", error);
        }
    }
    
    /**
     * Lock player movement during combat
     */
    lockPlayerMovement(player) {
        try {
            // Add effect to prevent movement
            player.addEffect("slowness", 999999, {
                amplifier: 255,
                showParticles: false
            });
            
            // Add tag to indicate locked state
            player.addTag("combat_camera_locked");
            
        } catch (error) {
            console.error("Failed to lock player movement:", error);
        }
    }
    
    /**
     * Unlock player movement
     */
    unlockPlayerMovement(player) {
        try {
            // Remove slowness effect
            player.removeEffect("slowness");
            
            // Remove lock tag
            player.removeTag("combat_camera_locked");
            
        } catch (error) {
            console.error("Failed to unlock player movement:", error);
        }
    }
    
    /**
     * Adjust UI for combat camera view
     */
    adjustUIForCombatCamera(player) {
        // Add tag to indicate combat camera mode for UI
        player.addTag("combat_camera_active");
        
        // Send UI adjustment command
        player.runCommand(`tellraw @s {"text":"UI adjusted for combat view","color":"gray"}`);
    }
    
    /**
     * Restore original UI
     */
    restoreOriginalUI(player) {
        // Remove combat camera tag
        player.removeTag("combat_camera_active");
        
        // Send UI restoration command
        player.runCommand(`tellraw @s {"text":"UI restored to normal","color":"gray"}`);
    }
    
    /**
     * Update camera focus to specific participant
     */
    focusOnParticipant(player, targetParticipant) {
        const cameraState = this.activeCameras.get(player.id);
        if (!cameraState || !targetParticipant.isValid()) return;
        
        // Calculate new camera position focused on target
        const targetPos = targetParticipant.location;
        const newPosition = {
            x: targetPos.x,
            y: targetPos.y + this.config.combatHeight,
            z: targetPos.z
        };
        
        // Smoothly transition to new position
        cameraState.targetPosition = newPosition;
    }
    
    /**
     * Utility functions
     */
    
    interpolatePosition(current, target, progress) {
        return {
            x: current.x + (target.x - current.x) * progress,
            y: current.y + (target.y - current.y) * progress,
            z: current.z + (target.z - current.z) * progress
        };
    }
    
    interpolateRotation(current, target, progress) {
        // Handle yaw wrapping (0-360 degrees)
        let yawDiff = target.yaw - current.yaw;
        if (yawDiff > 180) yawDiff -= 360;
        if (yawDiff < -180) yawDiff += 360;
        
        return {
            pitch: current.pitch + (target.pitch - current.pitch) * progress,
            yaw: current.yaw + yawDiff * progress
        };
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }
    
    /**
     * Check if player has active combat camera
     */
    hasActiveCombatCamera(player) {
        return this.activeCameras.has(player.id);
    }
    
    /**
     * Get camera state for player
     */
    getCameraState(player) {
        return this.activeCameras.get(player.id);
    }
    
    /**
     * Emergency camera reset
     */
    emergencyResetCamera(player) {
        const cameraState = this.activeCameras.get(player.id);
        if (cameraState && cameraState.originalState) {
            // Immediately restore original position
            player.teleport(cameraState.originalState.position, {
                dimension: player.dimension,
                rotation: {
                    x: cameraState.originalState.rotation.pitch,
                    y: cameraState.originalState.rotation.yaw
                }
            });
            
            this.unlockPlayerMovement(player);
            this.restoreOriginalUI(player);
            this.activeCameras.delete(player.id);
            this.cameraTransitions.delete(player.id);
            
            player.sendMessage("§cCamera reset to normal view");
        }
    }
    
    /**
     * Cleanup inactive cameras
     */
    cleanup() {
        const currentTime = Date.now();
        
        for (const [playerId, cameraState] of this.activeCameras) {
            // Remove cameras that are too old (safety cleanup)
            if (currentTime - cameraState.lastUpdate > 300000) { // 5 minutes
                const player = world.getPlayers().find(p => p.id === playerId);
                if (player) {
                    this.emergencyResetCamera(player);
                } else {
                    this.activeCameras.delete(playerId);
                }
            }
        }
    }
}
