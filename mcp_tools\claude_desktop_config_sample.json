{"mcpServers": {"elevenlabs-mcp": {"command": "python", "args": ["-m", "elevenlabs_mcp"], "env": {"ELEVENLABS_API_KEY": "your-elevenlabs-api-key-here"}}, "minimax-mcp-js": {"command": "npx", "args": ["-y", "minimax-mcp-js"], "env": {"MINIMAX_API_HOST": "https://api.minimaxi.chat", "MINIMAX_API_KEY": "your-minimax-api-key-here", "MINIMAX_MCP_BASE_PATH": "C:\\Users\\<USER>\\Desktop\\coding stuff\\mcc_addon\\generated_assets", "MINIMAX_RESOURCE_MODE": "local"}}, "figma-mcp": {"command": "npx", "args": ["figma-mcp"], "env": {"FIGMA_API_KEY": "your-figma-api-key-here"}}}}