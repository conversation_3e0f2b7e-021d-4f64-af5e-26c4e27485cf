/**
 * UltimateManager - <PERSON>les advanced class abilities, ultimates, and passives
 */

export class Ultimate<PERSON>anager {
    constructor(player<PERSON>ata<PERSON>ana<PERSON>, combatManager) {
        this.playerDataManager = playerDataManager;
        this.combatManager = combatManager;
        
        // Track ultimate cooldowns per combat session
        this.ultimateCooldowns = new Map();
        
        this.initializeAbilities();
    }
    
    initializeAbilities() {
        // Define all class abilities
        this.classAbilities = {
            warrior: {
                passive5: {
                    name: "Battle Hardened",
                    description: "Gain 1 block at the start of each turn",
                    level: 5,
                    type: "passive"
                },
                ultimate10: {
                    name: "<PERSON><PERSON><PERSON><PERSON>'s Wrath",
                    description: "For 3 turns: double attack damage, heal for 25% of damage dealt",
                    level: 10,
                    type: "ultimate",
                    energyCost: 6,
                    duration: 3
                },
                passive15: {
                    name: "Unstoppable Force", 
                    description: "Immune to stuns and debuffs",
                    level: 15,
                    type: "passive"
                },
                master20: {
                    name: "Avatar of War",
                    description: "Enhanced <PERSON><PERSON><PERSON><PERSON>'s Wrath: +50% damage to all allies, immunity to damage for 1 turn",
                    level: 20,
                    type: "master",
                    energyCost: 8,
                    duration: 4
                }
            },
            mage: {
                passive5: {
                    name: "Arcane Intellect",
                    description: "Draw an extra card when energy is at maximum",
                    level: 5,
                    type: "passive"
                },
                ultimate10: {
                    name: "Arcane Ascendance",
                    description: "Gain 3 energy, draw 3 cards, next 3 spells cost 0 energy",
                    level: 10,
                    type: "ultimate",
                    energyCost: 5,
                    spellsAffected: 3
                },
                passive15: {
                    name: "Spell Mastery",
                    description: "All spells cost 1 less energy (minimum 1)",
                    level: 15,
                    type: "passive"
                },
                master20: {
                    name: "Archmage Transcendence",
                    description: "Enhanced Arcane Ascendance: +5 energy, draw 5 cards, next 5 spells free",
                    level: 20,
                    type: "master",
                    energyCost: 6,
                    spellsAffected: 5
                }
            },
            rogue: {
                passive5: {
                    name: "Assassinate",
                    description: "10% chance to instantly kill enemies below 25% health",
                    level: 5,
                    type: "passive"
                },
                ultimate10: {
                    name: "Shadow Clone",
                    description: "Create a shadow clone that copies your next 3 attacks with 75% damage",
                    level: 10,
                    type: "ultimate",
                    energyCost: 4,
                    attacksCopied: 3
                },
                passive15: {
                    name: "Shadow Step",
                    description: "25% chance to avoid all damage from attacks",
                    level: 15,
                    type: "passive"
                },
                master20: {
                    name: "Master Assassin",
                    description: "Enhanced Shadow Clone: 3 clones, 100% damage, lasts 5 attacks",
                    level: 20,
                    type: "master",
                    energyCost: 6,
                    attacksCopied: 5
                }
            },
            paladin: {
                passive5: {
                    name: "Divine Grace",
                    description: "Heal 2 HP at the end of each turn",
                    level: 5,
                    type: "passive"
                },
                ultimate10: {
                    name: "Divine Intervention",
                    description: "Fully heal all allies, grant immunity for 1 turn, cleanse debuffs",
                    level: 10,
                    type: "ultimate",
                    energyCost: 6,
                    duration: 1
                },
                passive15: {
                    name: "Aura of Protection",
                    description: "All allies gain 1 block at the start of each turn",
                    level: 15,
                    type: "passive"
                },
                master20: {
                    name: "Divine Avatar",
                    description: "Enhanced Divine Intervention: immunity for 2 turns, +10 max health permanently",
                    level: 20,
                    type: "master",
                    energyCost: 8,
                    duration: 2
                }
            },
            necromancer: {
                passive5: {
                    name: "Death Magic",
                    description: "Gain 1 energy when any creature dies",
                    level: 5,
                    type: "passive"
                },
                ultimate10: {
                    name: "Army of the Dead",
                    description: "Summon 3 skeleton warriors, gain energy equal to enemies killed",
                    level: 10,
                    type: "ultimate",
                    energyCost: 5,
                    skeletons: 3
                },
                passive15: {
                    name: "Soul Harvest",
                    description: "Killing enemies permanently increases max energy by 1 (max +3)",
                    level: 15,
                    type: "passive"
                },
                master20: {
                    name: "Lich Lord",
                    description: "Enhanced Army: 5 skeletons, gain 2 energy per kill, skeletons have 15 HP",
                    level: 20,
                    type: "master",
                    energyCost: 7,
                    skeletons: 5
                }
            }
        };
    }
    
    /**
     * Get available abilities for a player's class and level
     */
    getPlayerAbilities(player) {
        const data = this.playerDataManager.getPlayerData(player);
        if (!data || !data.class) return [];
        
        const classAbilities = this.classAbilities[data.class];
        if (!classAbilities) return [];
        
        const availableAbilities = [];
        for (const [key, ability] of Object.entries(classAbilities)) {
            if (data.classLevel >= ability.level) {
                availableAbilities.push({
                    id: key,
                    ...ability,
                    unlocked: true
                });
            } else {
                availableAbilities.push({
                    id: key,
                    ...ability,
                    unlocked: false
                });
            }
        }
        
        return availableAbilities;
    }
    
    /**
     * Check if player can use ultimate ability
     */
    canUseUltimate(player, abilityId) {
        const data = this.playerDataManager.getPlayerData(player);
        if (!data || !data.class) return false;
        
        const ability = this.classAbilities[data.class]?.[abilityId];
        if (!ability || (ability.type !== "ultimate" && ability.type !== "master")) return false;
        
        // Check level requirement
        if (data.classLevel < ability.level) return false;
        
        // Check energy requirement
        if (data.currentEnergy < ability.energyCost) return false;
        
        // Check cooldown
        const cooldownKey = `${player.id}_${abilityId}`;
        return !this.ultimateCooldowns.has(cooldownKey);
    }
    
    /**
     * Use ultimate ability
     */
    useUltimate(player, abilityId, combatSession) {
        if (!this.canUseUltimate(player, abilityId)) return false;
        
        const data = this.playerDataManager.getPlayerData(player);
        const ability = this.classAbilities[data.class][abilityId];
        
        // Consume energy
        data.currentEnergy -= ability.energyCost;
        
        // Set cooldown
        const cooldownKey = `${player.id}_${abilityId}`;
        this.ultimateCooldowns.set(cooldownKey, true);
        
        // Apply ultimate effect
        this.applyUltimateEffect(player, ability, combatSession);
        
        // Notify combat participants
        if (this.combatManager) {
            this.combatManager.notifyParticipants(combatSession, 
                `§6✦ ${player.name} §euses §6${ability.name}§e! ✦`);
        }
        
        return true;
    }
    
    /**
     * Apply ultimate ability effects
     */
    applyUltimateEffect(player, ability, combatSession) {
        const data = this.playerDataManager.getPlayerData(player);
        
        switch (ability.name) {
            case "Berserker's Wrath":
            case "Avatar of War":
                this.applyBerserkerWrath(player, ability, combatSession);
                break;
            case "Arcane Ascendance":
            case "Archmage Transcendence":
                this.applyArcaneAscendance(player, ability, combatSession);
                break;
            case "Shadow Clone":
            case "Master Assassin":
                this.applyShadowClone(player, ability, combatSession);
                break;
            case "Divine Intervention":
            case "Divine Avatar":
                this.applyDivineIntervention(player, ability, combatSession);
                break;
            case "Army of the Dead":
            case "Lich Lord":
                this.applyArmyOfTheDead(player, ability, combatSession);
                break;
        }
    }
    
    /**
     * Apply Berserker's Wrath effect
     */
    applyBerserkerWrath(player, ability, combatSession) {
        const data = this.playerDataManager.getPlayerData(player);

        // Add berserker status effect
        if (!data.statusEffects) data.statusEffects = new Map();
        data.statusEffects.set('berserker_wrath', {
            name: 'Berserker\'s Wrath',
            duration: ability.duration,
            type: 'buff',
            description: 'Double attack damage, heal for 25% of damage dealt'
        });

        if (ability.name === "Avatar of War") {
            // Apply to all allies
            for (const participant of combatSession.participants) {
                if (participant.typeId === "minecraft:player" && participant !== player) {
                    const allyData = this.playerDataManager.getPlayerData(participant);
                    if (allyData) {
                        if (!allyData.statusEffects) allyData.statusEffects = new Map();
                        allyData.statusEffects.set('avatar_of_war', {
                            name: 'Avatar of War',
                            duration: 1,
                            type: 'buff',
                            description: 'Immunity to damage'
                        });
                    }
                }
            }
        }

        player.sendMessage("§c⚔️ You feel the fury of battle coursing through your veins!");
    }

    /**
     * Apply Arcane Ascendance effect
     */
    applyArcaneAscendance(player, ability, combatSession) {
        const data = this.playerDataManager.getPlayerData(player);

        // Gain energy
        const energyGain = ability.name === "Archmage Transcendence" ? 5 : 3;
        data.currentEnergy = Math.min(data.maxEnergy + energyGain, data.maxEnergy + energyGain);

        // Draw cards
        const cardsToDraw = ability.name === "Archmage Transcendence" ? 5 : 3;
        if (this.combatManager && this.combatManager.cardManager) {
            for (let i = 0; i < cardsToDraw; i++) {
                this.combatManager.cardManager.drawCard(player);
            }
        }

        // Add free spell status
        if (!data.statusEffects) data.statusEffects = new Map();
        data.statusEffects.set('free_spells', {
            name: 'Arcane Ascendance',
            duration: 999, // Until spells used
            type: 'buff',
            description: `Next ${ability.spellsAffected} spells cost 0 energy`,
            spellsRemaining: ability.spellsAffected
        });

        player.sendMessage("§b✦ Arcane power flows through you!");
    }

    /**
     * Apply Shadow Clone effect
     */
    applyShadowClone(player, ability, combatSession) {
        const data = this.playerDataManager.getPlayerData(player);

        // Add shadow clone status
        if (!data.statusEffects) data.statusEffects = new Map();
        const cloneCount = ability.name === "Master Assassin" ? 3 : 1;
        const damagePercent = ability.name === "Master Assassin" ? 100 : 75;

        data.statusEffects.set('shadow_clone', {
            name: 'Shadow Clone',
            duration: 999, // Until attacks used
            type: 'buff',
            description: `${cloneCount} shadow clone(s) copy next ${ability.attacksCopied} attacks`,
            attacksRemaining: ability.attacksCopied,
            cloneCount: cloneCount,
            damagePercent: damagePercent
        });

        player.sendMessage("§8👥 Shadow clones emerge from the darkness!");
    }

    /**
     * Apply Divine Intervention effect
     */
    applyDivineIntervention(player, ability, combatSession) {
        // Heal all allies
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player") {
                const health = participant.getComponent("health");
                if (health) {
                    health.setCurrentValue(health.effectiveMax);

                    // Grant immunity
                    const allyData = this.playerDataManager.getPlayerData(participant);
                    if (allyData) {
                        if (!allyData.statusEffects) allyData.statusEffects = new Map();
                        allyData.statusEffects.set('divine_protection', {
                            name: 'Divine Protection',
                            duration: ability.duration,
                            type: 'buff',
                            description: 'Immune to all damage'
                        });

                        // Cleanse debuffs
                        for (const [key, effect] of allyData.statusEffects.entries()) {
                            if (effect.type === 'debuff') {
                                allyData.statusEffects.delete(key);
                            }
                        }

                        if (ability.name === "Divine Avatar") {
                            // Permanently increase max health
                            allyData.maxHealth += 10;
                            health.setCurrentValue(health.effectiveMax + 10);
                        }
                    }

                    participant.sendMessage("§e✚ Divine light washes over you!");
                }
            }
        }
    }

    /**
     * Apply Army of the Dead effect
     */
    applyArmyOfTheDead(player, ability, combatSession) {
        const data = this.playerDataManager.getPlayerData(player);

        // Add skeleton summoning status
        if (!data.statusEffects) data.statusEffects = new Map();
        const skeletonHP = ability.name === "Lich Lord" ? 15 : 8;

        data.statusEffects.set('skeleton_army', {
            name: 'Skeleton Army',
            duration: 999, // Permanent until combat ends
            type: 'buff',
            description: `${ability.skeletons} skeleton warriors fight alongside you`,
            skeletonCount: ability.skeletons,
            skeletonHP: skeletonHP,
            energyPerKill: ability.name === "Lich Lord" ? 2 : 1
        });

        // Gain energy for enemies already killed this combat
        const enemiesKilled = combatSession.enemiesKilled || 0;
        const energyGain = enemiesKilled * (ability.name === "Lich Lord" ? 2 : 1);
        data.currentEnergy = Math.min(data.currentEnergy + energyGain, data.maxEnergy);

        player.sendMessage("§8☠️ The dead rise to serve you!");
        if (energyGain > 0) {
            player.sendMessage(`§b+${energyGain} energy from fallen enemies!`);
        }
    }

    /**
     * Apply passive abilities during combat events
     */
    applyPassiveAbilities(player, event, combatSession) {
        const data = this.playerDataManager.getPlayerData(player);
        if (!data || !data.class) return;

        const abilities = this.getPlayerAbilities(player).filter(a => a.unlocked && a.type === "passive");

        for (const ability of abilities) {
            switch (ability.name) {
                case "Battle Hardened":
                    if (event === "turn_start") {
                        data.block = (data.block || 0) + 1;
                        player.sendMessage("§7⚔️ Battle Hardened: +1 block");
                    }
                    break;

                case "Arcane Intellect":
                    if (event === "turn_start" && data.currentEnergy === data.maxEnergy) {
                        if (this.combatManager?.cardManager) {
                            this.combatManager.cardManager.drawCard(player);
                            player.sendMessage("§7✦ Arcane Intellect: Draw extra card");
                        }
                    }
                    break;

                case "Assassinate":
                    if (event === "before_attack" && Math.random() < 0.1) {
                        // Check if target is below 25% health
                        const target = combatSession.currentTarget;
                        if (target) {
                            const health = target.getComponent("health");
                            if (health && health.currentValue < health.effectiveMax * 0.25) {
                                health.setCurrentValue(0);
                                player.sendMessage("§8🗡️ Assassinate: Instant kill!");
                                return true; // Indicates instant kill
                            }
                        }
                    }
                    break;

                case "Divine Grace":
                    if (event === "turn_end") {
                        const health = player.getComponent("health");
                        if (health) {
                            const newHealth = Math.min(health.currentValue + 2, health.effectiveMax);
                            health.setCurrentValue(newHealth);
                            player.sendMessage("§7✚ Divine Grace: +2 health");
                        }
                    }
                    break;

                case "Death Magic":
                    if (event === "creature_death") {
                        data.currentEnergy = Math.min(data.currentEnergy + 1, data.maxEnergy);
                        player.sendMessage("§7☠️ Death Magic: +1 energy");
                    }
                    break;

                case "Unstoppable Force":
                    if (event === "apply_debuff") {
                        player.sendMessage("§7⚔️ Unstoppable Force: Immune to debuffs");
                        return false; // Prevent debuff
                    }
                    break;

                case "Spell Mastery":
                    if (event === "spell_cost") {
                        return Math.max(1, (combatSession.originalCost || 1) - 1);
                    }
                    break;

                case "Shadow Step":
                    if (event === "take_damage" && Math.random() < 0.25) {
                        player.sendMessage("§8👤 Shadow Step: Avoided damage!");
                        return 0; // No damage taken
                    }
                    break;

                case "Aura of Protection":
                    if (event === "turn_start") {
                        // Apply to all allies
                        for (const participant of combatSession.participants) {
                            if (participant.typeId === "minecraft:player") {
                                const allyData = this.playerDataManager.getPlayerData(participant);
                                if (allyData) {
                                    allyData.block = (allyData.block || 0) + 1;
                                    if (participant !== player) {
                                        participant.sendMessage("§7✚ Aura of Protection: +1 block");
                                    }
                                }
                            }
                        }
                    }
                    break;

                case "Soul Harvest":
                    if (event === "enemy_killed") {
                        const currentBonus = data.soulHarvestBonus || 0;
                        if (currentBonus < 3) {
                            data.soulHarvestBonus = currentBonus + 1;
                            data.maxEnergy += 1;
                            player.sendMessage("§7☠️ Soul Harvest: +1 max energy (permanent)");
                        }
                    }
                    break;
            }
        }

        return null; // No special return value
    }

    /**
     * Clear ultimate cooldowns for new combat
     */
    clearCombatCooldowns(combatSession) {
        // Clear cooldowns for all participants in this combat
        for (const participant of combatSession.participants) {
            if (participant.typeId === "minecraft:player") {
                const keys = Array.from(this.ultimateCooldowns.keys())
                    .filter(key => key.startsWith(participant.id));
                keys.forEach(key => this.ultimateCooldowns.delete(key));
            }
        }
    }
}
