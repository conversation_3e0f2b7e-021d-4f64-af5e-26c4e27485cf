/**
 * ClassSelectionUI - Handles class selection interface for new players
 */

import { ActionFormData, ModalFormData, MessageFormData } from "@minecraft/server-ui";
import { world, system } from "@minecraft/server";

export class ClassSelectionUI {
    constructor(classManager, playerDataManager) {
        this.classManager = classManager;
        this.playerDataManager = playerDataManager;
        this.classInfo = this.initializeClassInfo();
    }

    /**
     * Initialize detailed class information for UI display
     */
    initializeClassInfo() {
        return {
            warrior: {
                name: "⚔️ Warrior",
                icon: "🛡️",
                description: "Masters of combat and defense",
                playstyle: "Tank/Damage Dealer",
                strengths: ["High damage output", "Strong defensive abilities", "Battle-hardened survivability"],
                abilities: {
                    passive: "Battle Hardened - Gain 1 block at start of each turn",
                    ultimate: "Berserk<PERSON>'s Wrath - Double damage + healing for 3 turns",
                    advanced: "Unstoppable Force - Immune to stuns and debuffs",
                    master: "Avatar of War - Enhanced berserker + team damage boost"
                },
                cardTypes: ["Heavy attacks", "Defensive blocks", "Rage abilities"],
                difficulty: "★★☆☆☆ Beginner Friendly"
            },
            
            mage: {
                name: "🔮 Mage",
                icon: "✨",
                description: "Wielders of arcane magic and energy",
                playstyle: "Spell Caster/Support",
                strengths: ["Powerful spells", "Energy manipulation", "Versatile magic"],
                abilities: {
                    passive: "Arcane Intellect - Draw extra card at max energy",
                    ultimate: "Arcane Ascendance - Gain energy, cards, and free spells",
                    advanced: "Spell Mastery - All spells cost 1 less energy",
                    master: "Archmage Transcendence - Enhanced ascendance with more power"
                },
                cardTypes: ["Elemental spells", "Energy manipulation", "Magical effects"],
                difficulty: "★★★☆☆ Moderate"
            },
            
            rogue: {
                name: "🗡️ Rogue",
                icon: "🌙",
                description: "Masters of stealth and precision",
                playstyle: "Assassin/Critical Strikes",
                strengths: ["High critical damage", "Stealth abilities", "Quick strikes"],
                abilities: {
                    passive: "Assassinate - 10% chance to instantly kill low HP enemies",
                    ultimate: "Shadow Clone - Clone copies your next 3 attacks",
                    advanced: "Shadow Step - 25% chance to avoid all damage",
                    master: "Master Assassin - 3 clones with enhanced damage"
                },
                cardTypes: ["Quick attacks", "Stealth abilities", "Critical strikes"],
                difficulty: "★★★★☆ Advanced"
            },
            
            paladin: {
                name: "🛡️ Paladin",
                icon: "⭐",
                description: "Holy warriors of light and protection",
                playstyle: "Healer/Support Tank",
                strengths: ["Healing abilities", "Team protection", "Divine magic"],
                abilities: {
                    passive: "Divine Grace - Heal 2 HP at end of each turn",
                    ultimate: "Divine Intervention - Heal all + immunity + cleanse",
                    advanced: "Aura of Protection - All allies gain 1 block per turn",
                    master: "Divine Avatar - Enhanced intervention + permanent health"
                },
                cardTypes: ["Healing spells", "Protection buffs", "Divine magic"],
                difficulty: "★★★☆☆ Moderate"
            },
            
            necromancer: {
                name: "💀 Necromancer",
                icon: "🌑",
                description: "Masters of death magic and minions",
                playstyle: "Summoner/Death Magic",
                strengths: ["Minion summoning", "Death magic", "Energy from kills"],
                abilities: {
                    passive: "Death Magic - Gain 1 energy when any creature dies",
                    ultimate: "Army of the Dead - Summon 3 skeleton warriors",
                    advanced: "Soul Harvest - Gain max energy from kills (max +3)",
                    master: "Lich Lord - 5 skeletons + enhanced death magic"
                },
                cardTypes: ["Necromancy spells", "Minion summoning", "Death magic"],
                difficulty: "★★★★★ Expert"
            }
        };
    }

    /**
     * Show initial class selection welcome screen
     */
    async showWelcomeScreen(player) {
        const form = new MessageFormData()
            .title("§6🎴 Welcome to Card Combat! 🎴")
            .body(
                "§eWelcome to the world of strategic card-based combat!\n\n" +
                "§7Before you begin your journey, you must choose a class that will define your playstyle and abilities.\n\n" +
                "§aEach class offers unique:\n" +
                "§7• Passive abilities that activate automatically\n" +
                "§7• Ultimate abilities for powerful combat effects\n" +
                "§7• Specialized cards and strategies\n" +
                "§7• Progressive advancement through 20 levels\n\n" +
                "§6Choose wisely - your class will shape your entire adventure!"
            )
            .button1("§aChoose My Class")
            .button2("§7Learn More");

        try {
            const response = await form.show(player);
            if (response.canceled) {
                // Show again after delay if canceled
                system.runTimeout(() => this.showWelcomeScreen(player), 100);
                return;
            }

            if (response.selection === 0) {
                // Show class selection
                this.showClassSelection(player);
            } else {
                // Show detailed information first
                this.showClassOverview(player);
            }
        } catch (error) {
            console.error("Error showing welcome screen:", error);
            player.sendMessage("§cError showing class selection. Use /class select <class> to choose manually.");
        }
    }

    /**
     * Show class overview before selection
     */
    async showClassOverview(player) {
        const form = new ActionFormData()
            .title("§6📚 Class System Overview")
            .body(
                "§eClass Progression System:\n\n" +
                "§6Level 5: §aPassive Ability\n" +
                "§7Your class's signature passive ability unlocks\n\n" +
                "§6Level 10: §cUltimate Ability + Card\n" +
                "§7Powerful once-per-combat ability unlocks\n\n" +
                "§6Level 15: §dAdvanced Passive\n" +
                "§7Enhanced passive ability unlocks\n\n" +
                "§6Level 20: §5Master Ability + Card\n" +
                "§7Ultimate evolution of your ultimate ability\n\n" +
                "§eReady to choose your path?"
            );

        // Add class preview buttons
        const classes = Object.keys(this.classInfo);
        for (const className of classes) {
            const classData = this.classInfo[className];
            form.button(`${classData.name}\n§7${classData.description}`, undefined);
        }

        form.button("§aI'm Ready to Choose!", undefined);

        try {
            const response = await form.show(player);
            if (response.canceled) {
                this.showWelcomeScreen(player);
                return;
            }

            if (response.selection === classes.length) {
                // Ready to choose
                this.showClassSelection(player);
            } else {
                // Show specific class details
                const selectedClass = classes[response.selection];
                this.showClassDetails(player, selectedClass);
            }
        } catch (error) {
            console.error("Error showing class overview:", error);
        }
    }

    /**
     * Show detailed information for a specific class
     */
    async showClassDetails(player, className) {
        const classData = this.classInfo[className];
        if (!classData) return;

        const form = new MessageFormData()
            .title(`${classData.name} Details`)
            .body(
                `${classData.icon} §6${classData.name}\n\n` +
                `§e${classData.description}\n\n` +
                `§6Playstyle: §f${classData.playstyle}\n` +
                `§6Difficulty: §f${classData.difficulty}\n\n` +
                `§aStrengths:\n${classData.strengths.map(s => `§7• ${s}`).join('\n')}\n\n` +
                `§6Abilities:\n` +
                `§d🌟 Passive (L5): §f${classData.abilities.passive}\n` +
                `§c⚡ Ultimate (L10): §f${classData.abilities.ultimate}\n` +
                `§d🌟 Advanced (L15): §f${classData.abilities.advanced}\n` +
                `§5👑 Master (L20): §f${classData.abilities.master}\n\n` +
                `§6Card Types:\n${classData.cardTypes.map(t => `§7• ${t}`).join('\n')}`
            )
            .button1(`§aSelect ${classData.name}`)
            .button2("§7Back to Overview");

        try {
            const response = await form.show(player);
            if (response.canceled || response.selection === 1) {
                this.showClassOverview(player);
                return;
            }

            if (response.selection === 0) {
                this.confirmClassSelection(player, className);
            }
        } catch (error) {
            console.error("Error showing class details:", error);
        }
    }

    /**
     * Show main class selection interface
     */
    async showClassSelection(player) {
        const form = new ActionFormData()
            .title("§6⚔️ Choose Your Class ⚔️")
            .body("§eSelect your class to begin your journey:\n§7Click on a class to see details and select it.");

        // Add class buttons
        const classes = Object.keys(this.classInfo);
        for (const className of classes) {
            const classData = this.classInfo[className];
            form.button(
                `${classData.name}\n§7${classData.playstyle} • ${classData.difficulty}`,
                undefined
            );
        }

        try {
            const response = await form.show(player);
            if (response.canceled) {
                // Show welcome screen again if canceled
                system.runTimeout(() => this.showWelcomeScreen(player), 100);
                return;
            }

            const selectedClass = classes[response.selection];
            this.showClassDetails(player, selectedClass);
        } catch (error) {
            console.error("Error showing class selection:", error);
        }
    }

    /**
     * Confirm class selection
     */
    async confirmClassSelection(player, className) {
        const classData = this.classInfo[className];
        
        const form = new MessageFormData()
            .title("§6⚠️ Confirm Class Selection")
            .body(
                `§eYou have chosen: ${classData.name}\n\n` +
                `§7This will be your permanent class and cannot be changed easily.\n\n` +
                `§6Are you sure you want to become a ${classData.name.replace(/[^\w\s]/g, '')}?`
            )
            .button1("§aYes, I'm Sure!")
            .button2("§cNo, Let Me Choose Again");

        try {
            const response = await form.show(player);
            if (response.canceled || response.selection === 1) {
                this.showClassSelection(player);
                return;
            }

            if (response.selection === 0) {
                this.finalizeClassSelection(player, className);
            }
        } catch (error) {
            console.error("Error confirming class selection:", error);
        }
    }

    /**
     * Finalize the class selection
     */
    finalizeClassSelection(player, className) {
        try {
            // Use the class manager to set the class
            const success = this.classManager.setPlayerClass(player, className);
            
            if (success) {
                const classData = this.classInfo[className];
                
                // Success messages
                player.sendMessage(`§6🎉 Class Selected: ${classData.name}! 🎉`);
                player.sendMessage(`§eYou are now a ${classData.name.replace(/[^\w\s]/g, '')}!`);
                player.sendMessage(`§7Your journey begins now. Level up to unlock powerful abilities!`);
                
                // Show initial guidance
                system.runTimeout(() => {
                    player.sendMessage("§6📚 Getting Started:");
                    player.sendMessage("§e• Use /class abilities to see your progression");
                    player.sendMessage("§e• Use /combat start to begin card-based combat");
                    player.sendMessage("§e• Use /class stats to view your class information");
                    player.sendMessage("§7Level up through combat to unlock new abilities!");
                }, 60);
                
                // Announce to world
                world.sendMessage(`§6${player.name} has chosen the path of the ${classData.name.replace(/[^\w\s]/g, '')}!`);
                
            } else {
                player.sendMessage("§cError selecting class. Please try again or use /class select <class>.");
                this.showClassSelection(player);
            }
        } catch (error) {
            console.error("Error finalizing class selection:", error);
            player.sendMessage("§cError selecting class. Please try again.");
        }
    }

    /**
     * Check if player needs class selection
     */
    needsClassSelection(player) {
        const currentClass = this.classManager.getPlayerClass(player);
        return !currentClass;
    }

    /**
     * Show class selection for existing players who haven't chosen
     */
    async showForExistingPlayer(player) {
        if (this.needsClassSelection(player)) {
            player.sendMessage("§6🎴 You haven't chosen a class yet!");
            player.sendMessage("§eSelect your class to unlock the full card combat experience.");
            
            system.runTimeout(() => {
                this.showWelcomeScreen(player);
            }, 40);
        }
    }
}
