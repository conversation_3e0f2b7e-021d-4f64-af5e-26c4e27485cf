/**
 * Card Combat System - Main Entry Point
 * Minecraft Bedrock Edition Addon
 * 
 * This file initializes the card-based turn-based combat system
 */

import { world, system } from "@minecraft/server";
import { CombatManager } from "./combat/CombatManager.js";
import { CardManager } from "./cards/CardManager.js";
import { UIManager } from "./ui/UIManager.js";
import { PlayerDataManager } from "./data/PlayerDataManager.js";

// Global managers
let combatManager;
let cardManager;
let uiManager;
let playerDataManager;

/**
 * Initialize the addon when the world loads
 */
world.afterEvents.worldLoad.subscribe(() => {
    console.log("Card Combat System: Initializing...");
    
    try {
        // Initialize managers
        playerDataManager = new PlayerDataManager();
        cardManager = new CardManager();
        uiManager = new UIManager();
        combatManager = new CombatManager(cardManager, uiManager, playerDataManager);
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize player data for existing players
        for (const player of world.getAllPlayers()) {
            playerDataManager.initializePlayer(player);
        }
        
        console.log("Card Combat System: Successfully initialized!");
        world.sendMessage("§aCard Combat System loaded! Combat is now turn-based.");
        
    } catch (error) {
        console.error("Card Combat System: Failed to initialize:", error);
        world.sendMessage("§cCard Combat System failed to load. Check console for errors.");
    }
});

/**
 * Set up event listeners for the combat system
 */
function setupEventListeners() {
    // Player join event
    world.afterEvents.playerJoin.subscribe((event) => {
        playerDataManager.initializePlayer(event.player);
        world.sendMessage(`§e${event.player.name} joined! Welcome to card-based combat!`);
    });
    
    // Player leave event
    world.afterEvents.playerLeave.subscribe((event) => {
        combatManager.handlePlayerLeave(event.player);
    });
    
    // Entity hurt event - intercept for turn-based combat
    world.afterEvents.entityHurt.subscribe((event) => {
        combatManager.handleEntityHurt(event);
    });
    
    // Entity hit event - intercept for turn-based combat
    world.afterEvents.entityHitEntity.subscribe((event) => {
        combatManager.handleEntityHit(event);
    });
    
    // Item use event - for card-related items
    world.afterEvents.itemUse.subscribe((event) => {
        combatManager.handleItemUse(event);
    });
    
    // Chat command event - for debug and admin commands
    world.beforeEvents.chatSend.subscribe((event) => {
        if (event.message.startsWith("!combat")) {
            event.cancel = true;
            handleCombatCommand(event.sender, event.message);
        }
    });
}

/**
 * Handle combat-related commands
 */
function handleCombatCommand(player, message) {
    const args = message.split(" ");
    const command = args[1];
    
    switch (command) {
        case "debug":
            combatManager.toggleDebugMode(player);
            break;
        case "deck":
            uiManager.showDeckBuilder(player);
            break;
        case "collection":
            uiManager.showCardCollection(player);
            break;
        case "reset":
            if (player.hasTag("admin")) {
                playerDataManager.resetPlayer(player);
                player.sendMessage("§aPlayer data reset!");
            } else {
                player.sendMessage("§cYou don't have permission to use this command.");
            }
            break;
        case "help":
            showHelpMessage(player);
            break;
        default:
            player.sendMessage("§cUnknown combat command. Use !combat help for available commands.");
    }
}

/**
 * Show help message for combat commands
 */
function showHelpMessage(player) {
    const helpText = [
        "§6=== Card Combat System Commands ===",
        "§e!combat deck §7- Open deck builder",
        "§e!combat collection §7- View card collection",
        "§e!combat debug §7- Toggle debug mode",
        "§e!combat help §7- Show this help message",
        "§7Admin only:",
        "§e!combat reset §7- Reset player data"
    ];
    
    for (const line of helpText) {
        player.sendMessage(line);
    }
}

// Export managers for use in other modules
export { combatManager, cardManager, uiManager, playerDataManager };
