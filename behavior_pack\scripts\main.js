/**
 * Card Combat System - Main Entry Point
 * Minecraft Bedrock Edition Addon
 * 
 * This file initializes the card-based turn-based combat system
 */

import { world, system } from "@minecraft/server";
import { CombatManager } from "./combat/CombatManager.js";
import { CardManager } from "./cards/CardManager.js";
import { UIManager } from "./ui/UIManager.js";
import { PlayerDataManager } from "./data/PlayerDataManager.js";
import { ProgressionManager } from "./progression/ProgressionManager.js";
import { StatusEffectManager } from "./combat/StatusEffectManager.js";
import { DamageCalculator } from "./combat/DamageCalculator.js";
import { TurnOrderManager } from "./combat/TurnOrderManager.js";
import { CombatAI } from "./combat/CombatAI.js";
import { CardEffects } from "./cards/CardEffects.js";
import { DeckBuilder } from "./cards/DeckBuilder.js";
import { CombatUIController } from "./ui/CombatUIController.js";
import { TestManager } from "./testing/TestManager.js";
import { AudioManager } from "./audio/AudioManager.js";
import { ClassManager } from "./classes/ClassManager.js";
import { ClassCommands } from "./commands/ClassCommands.js";
import { UltimateManager } from "./abilities/UltimateManager.js";

// Global managers
let combatManager;
let cardManager;
let uiManager;
let playerDataManager;
let progressionManager;
let statusEffectManager;
let damageCalculator;
let turnOrderManager;
let combatAI;
let cardEffects;
let deckBuilder;
let combatUIController;
let testManager;
let audioManager;
let classManager;
let classCommands;
let ultimateManager;

/**
 * Initialize the addon when the world loads
 */
world.afterEvents.worldLoad.subscribe(() => {
    console.log("Card Combat System: Initializing...");
    
    try {
        // Initialize audio system first
        audioManager = new AudioManager();

        // Initialize core managers
        playerDataManager = new PlayerDataManager(audioManager);
        cardManager = new CardManager();

        // Initialize combat subsystems
        damageCalculator = new DamageCalculator(playerDataManager);
        statusEffectManager = new StatusEffectManager(playerDataManager, damageCalculator);
        turnOrderManager = new TurnOrderManager(playerDataManager, statusEffectManager);

        // Initialize card subsystems
        cardEffects = new CardEffects(null, playerDataManager); // Will set combatManager later
        deckBuilder = new DeckBuilder(cardManager, playerDataManager);

        // Initialize UI systems
        uiManager = new UIManager();
        combatUIController = new CombatUIController(cardManager, playerDataManager);

        // Initialize AI
        combatAI = new CombatAI(cardManager, null); // Will set combatManager later

        // Initialize progression system
        progressionManager = new ProgressionManager(playerDataManager, cardManager);

        // Initialize class system
        classManager = new ClassManager(playerDataManager, cardManager);

        // Initialize ultimate system
        ultimateManager = new UltimateManager(playerDataManager, null); // Will set combat manager later
        classManager.setUltimateManager(ultimateManager);

        classCommands = new ClassCommands(classManager, playerDataManager, ultimateManager);

        // Initialize main combat manager with all dependencies
        combatManager = new CombatManager(cardManager, uiManager, playerDataManager, audioManager, classManager, ultimateManager);

        // Set circular dependencies
        ultimateManager.combatManager = combatManager;

        // Set circular dependencies
        cardEffects.combatManager = combatManager;
        combatAI.combatManager = combatManager;

        // Initialize testing system
        testManager = new TestManager(combatManager, cardManager, playerDataManager, progressionManager, audioManager);
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize player data for existing players
        for (const player of world.getAllPlayers()) {
            playerDataManager.initializePlayer(player);
        }
        
        console.log("Card Combat System: Successfully initialized!");
        world.sendMessage("§aCard Combat System loaded! Combat is now turn-based.");

        // Start main update loop
        startMainUpdateLoop();

    } catch (error) {
        console.error("Card Combat System: Failed to initialize:", error);
        world.sendMessage("§cCard Combat System failed to load. Check console for errors.");
    }
});

/**
 * Set up event listeners for the combat system - INTERCEPT ALL COMBAT
 */
function setupEventListeners() {
    // Player join event
    world.afterEvents.playerJoin.subscribe((event) => {
        playerDataManager.initializePlayer(event.player);

        // Give starter deck to new players
        giveStarterDeck(event.player);

        world.sendMessage(`§6${event.player.name} joined! §eAll combat is now CARD-BASED!`);
        world.sendMessage(`§7Use /combat help for commands`);

        // Show starter deck notification
        system.runTimeout(() => {
            event.player.sendMessage("§6🎴 STARTER DECK RECEIVED! 🎴");
            event.player.sendMessage("§eYou've been given basic cards to start your journey!");
            event.player.sendMessage("§7Use /combat deck to view and customize your deck");
        }, 60); // 3 second delay
    });

    // Player leave event
    world.afterEvents.playerLeave.subscribe((event) => {
        combatManager.handlePlayerLeave(event.player);
    });

    // Player spawn event - give starter deck to new players
    world.afterEvents.playerSpawn.subscribe((event) => {
        // Check if this is a new player (first spawn)
        if (event.initialSpawn) {
            system.runTimeout(() => {
                giveStarterDeck(event.player);
                event.player.sendMessage("§6🎴 WELCOME TO CARD COMBAT! 🎴");
                event.player.sendMessage("§eYou've received a starter deck!");
                event.player.sendMessage("§7Attack any mob to begin card-based combat!");
            }, 100); // 5 second delay for initial spawn
        }
    });

    // BEFORE EVENTS - Intercept and cancel original combat
    world.beforeEvents.entityHurt.subscribe((event) => {
        // Cancel ALL damage events - we'll handle them in card combat
        if (combatManager.shouldInterceptDamage(event)) {
            event.cancel = true;
            // Trigger card combat instead
            system.runTimeout(() => {
                combatManager.handleEntityHurt(event);
            }, 1);
        }
    });

    // AFTER EVENTS - Backup interception
    world.afterEvents.entityHurt.subscribe((event) => {
        combatManager.handleEntityHurt(event);
    });

    // Entity hit event - intercept for turn-based combat
    world.afterEvents.entityHitEntity.subscribe((event) => {
        combatManager.handleEntityHit(event);
    });

    // Player attack event - intercept player attacks
    world.beforeEvents.playerAttackEntity.subscribe((event) => {
        // Cancel the attack and start card combat instead
        event.cancel = true;
        system.runTimeout(() => {
            combatManager.initiateCombat(event.target, event.player);
        }, 1);
    });

    // Entity spawn event - notify about card combat
    world.afterEvents.entitySpawn.subscribe((event) => {
        if (combatManager.isCombatCapableMob(event.entity)) {
            // Add a tag to indicate this mob uses card combat
            event.entity.addTag("card_combat_enabled");
        }
    });

    // Item use event - for card-related items
    world.afterEvents.itemUse.subscribe((event) => {
        combatManager.handleItemUse(event);
    });

    // Chat command event - for slash commands
    world.beforeEvents.chatSend.subscribe((event) => {
        if (event.message.startsWith("/combat")) {
            event.cancel = true;
            handleCombatCommand(event.sender, event.message);
        }
    });

    // Periodic check for nearby entities to inform about card combat
    system.runInterval(() => {
        informPlayersAboutCardCombat();
    }, 1200); // Every 60 seconds
}

/**
 * Give starter deck to new players
 */
function giveStarterDeck(player) {
    try {
        // Check if player already has cards to avoid duplicates
        const playerData = playerDataManager.getPlayerData(player);

        // Only give starter deck if player has no cards or very few cards
        if (!playerData.collection || Object.keys(playerData.collection).length < 5) {

            // Basic starter deck - balanced for new players
            const starterCards = {
                "basic_strike": 4,      // Basic attack cards
                "basic_block": 3,       // Basic defense cards
                "heal": 2,              // Healing cards
                "power_attack": 2,      // Stronger attack
                "health_potion": 1,     // Emergency healing
                "critical_strike": 1,   // Special attack
                "shield_wall": 1        // Strong defense
            };

            // Add cards to player's collection
            for (const [cardId, quantity] of Object.entries(starterCards)) {
                for (let i = 0; i < quantity; i++) {
                    playerDataManager.addCardToCollection(player, cardId);
                }
            }

            // Set up basic starter deck
            const starterDeck = [
                "basic_strike", "basic_strike", "basic_strike",
                "basic_block", "basic_block",
                "heal", "heal",
                "power_attack", "power_attack",
                "health_potion"
            ];

            // Set the starter deck as active deck
            playerDataManager.setPlayerDeck(player, starterDeck);

            // Give some starting experience and energy
            playerDataManager.addExperience(player, 50); // Starting XP

            player.sendMessage("§a✅ Starter deck configured!");
            player.sendMessage("§7• 4x Basic Strike (attack)");
            player.sendMessage("§7• 3x Basic Block (defense)");
            player.sendMessage("§7• 2x Heal (recovery)");
            player.sendMessage("§7• 2x Power Attack (strong attack)");
            player.sendMessage("§7• 1x Health Potion (emergency heal)");
            player.sendMessage("§7• 1x Critical Strike (special attack)");
            player.sendMessage("§7• 1x Shield Wall (strong defense)");

        } else {
            player.sendMessage("§7You already have cards in your collection!");
        }

    } catch (error) {
        console.error("Error giving starter deck:", error);
        player.sendMessage("§cError setting up starter deck. Please contact an admin.");
    }
}

/**
 * Inform players about card combat system
 */
function informPlayersAboutCardCombat() {
    for (const player of world.getPlayers()) {
        // Check if player is near combat-capable mobs
        const nearbyEntities = player.dimension.getEntities({
            location: player.location,
            maxDistance: 16,
            excludeTypes: ["minecraft:player", "minecraft:item"]
        });

        const combatMobs = nearbyEntities.filter(entity =>
            combatManager.isCombatCapableMob(entity) && !entity.hasTag("card_combat_notified")
        );

        if (combatMobs.length > 0) {
            player.sendMessage(`§6⚔ Card Combat: §e${combatMobs.length} combat-ready entities nearby!`);
            player.sendMessage(`§7Attack them to start card-based combat!`);

            // Mark mobs as notified
            combatMobs.forEach(mob => mob.addTag("card_combat_notified"));
        }
    }
}

/**
 * Handle combat-related commands
 */
function handleCombatCommand(player, message) {
    const args = message.split(" ");
    const command = args[1];
    
    switch (command) {
        case "debug":
            combatManager.toggleDebugMode(player);
            break;
        case "deck":
            uiManager.showDeckBuilder(player);
            break;
        case "collection":
            uiManager.showCardCollection(player);
            break;
        case "reset":
            if (player.hasTag("admin")) {
                playerDataManager.resetPlayer(player);
                player.sendMessage("§aPlayer data reset!");
            } else {
                player.sendMessage("§cYou don't have permission to use this command.");
            }
            break;
        case "test":
            if (player.hasTag("admin")) {
                testManager.runAllTests(player);
            } else {
                player.sendMessage("§cYou don't have permission to use this command.");
            }
            break;
        case "scenario":
            if (player.hasTag("admin") && args[2]) {
                testManager.createTestScenario(player, args[2]);
            } else {
                player.sendMessage("§cUsage: /combat scenario <scenario_name>");
            }
            break;
        case "performance":
            if (player.hasTag("admin")) {
                testManager.runPerformanceTest(player);
            } else {
                player.sendMessage("§cYou don't have permission to use this command.");
            }
            break;
        case "memory":
            if (player.hasTag("admin")) {
                testManager.checkMemoryUsage(player);
            } else {
                player.sendMessage("§cYou don't have permission to use this command.");
            }
            break;
        case "testsounds":
            if (player.hasTag("admin")) {
                audioManager.testAllSounds(player);
            } else {
                player.sendMessage("§cYou don't have permission to use this command.");
            }
            break;
        case "resetcamera":
            if (player.hasTag("admin")) {
                combatManager.emergencyResetAllCameras();
                player.sendMessage("§aAll combat cameras reset!");
            } else {
                combatManager.cameraManager.emergencyResetCamera(player);
                player.sendMessage("§aYour camera has been reset!");
            }
            break;
        case "progress":
            const summary = progressionManager.getProgressionSummary(player);
            if (summary) {
                showProgressionSummary(player, summary);
            }
            break;
        case "help":
            showHelpMessage(player);
            break;
        case "info":
            showCardCombatInfo(player);
            break;
        case "starter":
            giveStarterDeck(player);
            break;
        case "newdeck":
            // Force give starter deck (useful for testing or if something went wrong)
            giveStarterDeck(player);
            player.sendMessage("§6Starter deck given! Check your collection.");
            break;
        default:
            player.sendMessage("§cUnknown combat command. Use /combat help for available commands.");
    }
}

/**
 * Show help message for combat commands
 */
function showHelpMessage(player) {
    const helpText = [
        "§6=== Card Combat System Commands ===",
        "§e/combat info §7- Card combat system info",
        "§e/combat starter §7- Get starter deck (new players)",
        "§e/combat deck §7- Open deck builder",
        "§e/combat collection §7- View card collection",
        "§e/combat progress §7- View progression summary",
        "§e/combat debug §7- Toggle debug mode",
        "§e/combat resetcamera §7- Reset your camera view",
        "§e/combat help §7- Show this help message",
        "§7Admin only:",
        "§e/combat reset §7- Reset player data",
        "§e/combat test §7- Run system tests",
        "§e/combat scenario <name> §7- Create test scenario",
        "§e/combat performance §7- Run performance test",
        "§e/combat memory §7- Check memory usage",
        "§e/combat testsounds §7- Test all audio effects",
        "§e/combat resetcamera §7- Reset all player cameras (admin)"
    ];

    for (const line of helpText) {
        player.sendMessage(line);
    }
}

/**
 * Show card combat system information
 */
function showCardCombatInfo(player) {
    player.sendMessage("§6=== CARD COMBAT SYSTEM ===");
    player.sendMessage("§e⚔ ALL COMBAT IS NOW CARD-BASED! ⚔");
    player.sendMessage("");
    player.sendMessage("§6🎴 STARTER DECK:");
    player.sendMessage("§7• New players get a free starter deck!");
    player.sendMessage("§7• Use /combat starter if you need one");
    player.sendMessage("");
    player.sendMessage("§6⚔ HOW IT WORKS:");
    player.sendMessage("§7• Attack any mob to start card combat");
    player.sendMessage("§7• All damage is turn-based using cards");
    player.sendMessage("§7• Mobs have their own card decks");
    player.sendMessage("§7• Use strategy instead of button mashing!");
    player.sendMessage("");
    player.sendMessage("§6Combat-Ready Mobs:");
    player.sendMessage("§7• Zombies, Skeletons, Spiders");
    player.sendMessage("§7• Creepers, Endermen, Witches");
    player.sendMessage("§7• Pillagers, Vindicators, Evokers");
    player.sendMessage("§7• Blazes, Ghasts, Piglins");
    player.sendMessage("§7• Hoglins, Wither Skeletons");
    player.sendMessage("§7• Card Duelists (special NPCs)");
    player.sendMessage("");
    player.sendMessage("§eTip: Build better decks to win harder fights!");
}

/**
 * Show progression summary
 */
function showProgressionSummary(player, summary) {
    const progressBar = createProgressBar(summary.progressPercent);

    player.sendMessage("§6=== Progression Summary ===");
    player.sendMessage(`§7Level: §e${summary.level} §7(${summary.title})`);
    player.sendMessage(`§7Experience: §b${summary.experience}`);
    if (summary.experienceToNext > 0) {
        player.sendMessage(`§7To Next Level: §b${summary.experienceToNext} XP`);
        player.sendMessage(`§7Progress: ${progressBar} §7${Math.round(summary.progressPercent)}%`);
    }
    player.sendMessage(`§7Wins: §a${summary.wins} §7(Streak: §e${summary.winStreak}§7)`);
    player.sendMessage(`§7Achievements: §d${summary.achievements}§7/§d${summary.totalAchievements}`);
}

/**
 * Create progress bar
 */
function createProgressBar(percent, length = 20) {
    const filled = Math.floor((percent / 100) * length);
    const empty = length - filled;
    return "§a" + "█".repeat(filled) + "§7" + "░".repeat(empty);
}

/**
 * Start main update loop
 */
function startMainUpdateLoop() {
    // Main update loop - runs every tick
    system.runInterval(() => {
        try {
            // Update combat system
            if (combatManager) {
                // Update camera system
                combatManager.updateCameras();
            }
        } catch (error) {
            console.error("Error in main update loop:", error);
        }
    }, 1); // Run every tick for smooth camera updates

    // Slower update loop for cleanup and maintenance
    system.runInterval(() => {
        try {
            if (combatManager) {
                // Cleanup cameras periodically
                combatManager.cleanupCameras();
            }
        } catch (error) {
            console.error("Error in maintenance loop:", error);
        }
    }, 1200); // Run every minute (1200 ticks)
}

// Export managers for use in other modules
export {
    combatManager,
    cardManager,
    uiManager,
    playerDataManager,
    progressionManager,
    statusEffectManager,
    damageCalculator,
    turnOrderManager,
    combatAI,
    cardEffects,
    deckBuilder,
    combatUIController,
    testManager,
    audioManager
};
