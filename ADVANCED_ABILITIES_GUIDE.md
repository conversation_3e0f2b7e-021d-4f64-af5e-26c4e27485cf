# Advanced Class Abilities & Ultimates Guide

## Overview
The Advanced Abilities system extends the Card Combat Class System with powerful passive abilities and ultimate attacks that unlock as players progress through their chosen class.

## Ability Types

### 1. Passive Abilities (Level 5)
Always-active abilities that trigger automatically during combat.

**Warrior - Battle Hardened**
- Effect: Gain 1 block at the start of each turn
- Provides consistent defensive value

**Mage - Arcane Intellect**
- Effect: Draw an extra card when energy is at maximum
- Rewards efficient energy management

**Rogue - Assassinate**
- Effect: 10% chance to instantly kill enemies below 25% health
- High-risk, high-reward finishing ability

**Paladin - Divine Grace**
- Effect: Heal 2 HP at the end of each turn
- Provides steady sustain throughout combat

**Necromancer - Death Magic**
- Effect: Gain 1 energy when any creature dies
- Scales with combat intensity

### 2. Ultimate Abilities (Level 10)
Powerful once-per-combat abilities with high energy costs.

**Warrior - <PERSON><PERSON><PERSON><PERSON>'s Wrath (6 energy)**
- Effect: For 3 turns: double attack damage, heal for 25% of damage dealt
- Duration: 3 turns
- Ultimate card unlocked: "<PERSON><PERSON><PERSON><PERSON>'s Wrath"

**Mage - Arcane Ascendance (5 energy)**
- Effect: Gain 3 energy, draw 3 cards, next 3 spells cost 0 energy
- Immediate: +3 energy, +3 cards
- Buff: Next 3 spells are free

**Rogue - Shadow Clone (4 energy)**
- Effect: Create a shadow clone that copies your next 3 attacks with 75% damage
- Duration: Until 3 attacks are copied
- Damage: 75% of original attack

**Paladin - Divine Intervention (6 energy)**
- Effect: Fully heal all allies, grant immunity for 1 turn, cleanse debuffs
- Healing: Full health restoration
- Protection: 1 turn immunity
- Cleanse: Remove all debuffs

**Necromancer - Army of the Dead (5 energy)**
- Effect: Summon 3 skeleton warriors, gain energy equal to enemies killed
- Skeletons: 3 warriors with 8 HP each
- Energy: +1 per enemy killed this combat

### 3. Advanced Passives (Level 15)
Enhanced passive abilities that provide significant advantages.

**Warrior - Unstoppable Force**
- Effect: Immune to stuns and debuffs
- Complete debuff immunity

**Mage - Spell Mastery**
- Effect: All spells cost 1 less energy (minimum 1)
- Applies to all spell-type cards

**Rogue - Shadow Step**
- Effect: 25% chance to avoid all damage from attacks
- Complete damage negation on proc

**Paladin - Aura of Protection**
- Effect: All allies gain 1 block at the start of each turn
- Affects entire team

**Necromancer - Soul Harvest**
- Effect: Killing enemies permanently increases max energy by 1 (max +3)
- Permanent progression bonus
- Maximum: +3 energy

### 4. Master Abilities (Level 20)
Enhanced versions of ultimate abilities with greater power.

**Warrior - Avatar of War (8 energy)**
- Effect: Enhanced Berserker's Wrath + 50% damage to all allies + immunity for 1 turn
- Duration: 4 turns
- Team buff: +50% damage to allies
- Protection: 1 turn immunity

**Mage - Archmage Transcendence (6 energy)**
- Effect: Enhanced Arcane Ascendance with +5 energy, draw 5 cards, next 5 spells free
- Immediate: +5 energy, +5 cards
- Buff: Next 5 spells are free

**Rogue - Master Assassin (6 energy)**
- Effect: Enhanced Shadow Clone with 3 clones, 100% damage, lasts 5 attacks
- Clones: 3 shadow clones
- Damage: 100% of original
- Duration: 5 attacks

**Paladin - Divine Avatar (8 energy)**
- Effect: Enhanced Divine Intervention + immunity for 2 turns + +10 max health permanently
- Protection: 2 turns immunity
- Permanent: +10 max health

**Necromancer - Lich Lord (7 energy)**
- Effect: Enhanced Army with 5 skeletons, gain 2 energy per kill, skeletons have 15 HP
- Skeletons: 5 warriors with 15 HP each
- Energy: +2 per enemy killed

## Ultimate Cards System

### Card Mechanics
- Ultimate abilities are also available as special cards
- Cards have the same energy costs as direct ultimate usage
- Cards can only be used once per combat (same as direct ultimates)
- Ultimate cards are automatically added to decks when abilities unlock

### Level Requirements
- Level 10: Basic ultimate card unlocked
- Level 20: Master ultimate card unlocked (replaces basic version)

### Card Integration
- Ultimate cards appear in hand like normal cards
- Playing an ultimate card triggers the same effect as using the ability directly
- Cards respect the once-per-combat limitation

## Combat Integration

### Passive Ability Triggers
- **Turn Start**: Battle Hardened, Arcane Intellect, Aura of Protection
- **Turn End**: Divine Grace
- **Before Attack**: Assassinate
- **Take Damage**: Shadow Step
- **Creature Death**: Death Magic
- **Enemy Killed**: Soul Harvest
- **Apply Debuff**: Unstoppable Force (prevents)
- **Spell Cost**: Spell Mastery (reduces)

### Ultimate Usage
- Available once per combat session
- Cooldowns reset when new combat begins
- Can be activated via `/class ultimate` command or by playing ultimate cards
- Energy cost must be met
- Level requirement must be satisfied

## Commands

### `/class abilities`
Shows all class abilities with unlock status and descriptions.

### `/class ultimate`
- In combat: Lists available ultimates and their status
- Out of combat: Shows ultimate information and usage instructions

## Progression Summary

| Level | Unlock | Description |
|-------|--------|-------------|
| 5 | Passive Ability | Always-active class bonus |
| 10 | Ultimate Ability + Card | Powerful once-per-combat ability |
| 15 | Advanced Passive | Enhanced passive effect |
| 20 | Master Ability + Card | Enhanced ultimate ability |

## Technical Implementation

### Status Effects
- Abilities use the status effects system for tracking durations
- Status effects are stored in player data
- Effects are processed during combat events

### Energy Management
- Ultimate abilities consume energy from player's current pool
- Energy costs are balanced against ability power
- Master abilities have higher costs but greater effects

### Combat Session Integration
- Abilities integrate seamlessly with existing combat system
- Passive abilities trigger automatically during combat events
- Ultimate cooldowns are managed per combat session

This advanced abilities system provides meaningful progression and specialization for each class, encouraging players to reach higher levels and master their chosen playstyle.
