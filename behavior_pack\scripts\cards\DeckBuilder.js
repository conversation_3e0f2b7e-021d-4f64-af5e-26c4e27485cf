/**
 * DeckBuilder - Advanced deck building and management system
 */

export class DeckBuilder {
    constructor(cardManager, playerDataManager) {
        this.cardManager = cardManager;
        this.playerDataManager = playerDataManager;
        
        // Deck constraints
        this.minDeckSize = 10;
        this.maxDeckSize = 30;
        this.maxCopiesPerCard = 3;
        this.maxLegendaryCards = 2;
        
        // Deck archetypes
        this.archetypes = {
            AGGRO: "aggro",
            CONTROL: "control",
            MIDRANGE: "midrange",
            COMBO: "combo"
        };
    }
    
    /**
     * Create a deck based on archetype
     */
    createArchetypeDeck(archetype, playerCollection) {
        switch (archetype) {
            case this.archetypes.AGGRO:
                return this.createAggroDeck(playerCollection);
            case this.archetypes.CONTROL:
                return this.createControlDeck(playerCollection);
            case this.archetypes.MIDRANGE:
                return this.createMidrangeDeck(playerCollection);
            case this.archetypes.COMBO:
                return this.createComboDeck(playerCollection);
            default:
                return this.createBalancedDeck(playerCollection);
        }
    }
    
    /**
     * Create an aggressive deck focused on quick damage
     */
    createAggroDeck(playerCollection) {
        const deck = [];
        const availableCards = this.getAvailableCards(playerCollection);
        
        // Prioritize low-cost attack cards
        const attackCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.ATTACK && card.cost <= 2
        );
        
        // Add attack cards (60% of deck)
        this.addCardsToDeck(deck, attackCards, Math.floor(this.minDeckSize * 0.6));
        
        // Add some cheap spells (25% of deck)
        const cheapSpells = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.SPELL && card.cost <= 3
        );
        this.addCardsToDeck(deck, cheapSpells, Math.floor(this.minDeckSize * 0.25));
        
        // Add minimal defense (15% of deck)
        const defenseCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.DEFENSE && card.cost <= 2
        );
        this.addCardsToDeck(deck, defenseCards, Math.floor(this.minDeckSize * 0.15));
        
        // Fill remaining slots with best available cards
        this.fillDeckToMinimum(deck, availableCards);
        
        return deck;
    }
    
    /**
     * Create a control deck focused on defense and late game
     */
    createControlDeck(playerCollection) {
        const deck = [];
        const availableCards = this.getAvailableCards(playerCollection);
        
        // Prioritize defense cards (40% of deck)
        const defenseCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.DEFENSE
        );
        this.addCardsToDeck(deck, defenseCards, Math.floor(this.minDeckSize * 0.4));
        
        // Add healing and utility spells (35% of deck)
        const utilitySpells = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.SPELL && 
            (card.healing || card.effects.includes("draw_card"))
        );
        this.addCardsToDeck(deck, utilitySpells, Math.floor(this.minDeckSize * 0.35));
        
        // Add high-cost powerful attacks (25% of deck)
        const powerfulAttacks = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.ATTACK && card.cost >= 3
        );
        this.addCardsToDeck(deck, powerfulAttacks, Math.floor(this.minDeckSize * 0.25));
        
        this.fillDeckToMinimum(deck, availableCards);
        return deck;
    }
    
    /**
     * Create a balanced midrange deck
     */
    createMidrangeDeck(playerCollection) {
        const deck = [];
        const availableCards = this.getAvailableCards(playerCollection);
        
        // Balanced distribution
        const attackCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.ATTACK
        );
        this.addCardsToDeck(deck, attackCards, Math.floor(this.minDeckSize * 0.4));
        
        const defenseCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.DEFENSE
        );
        this.addCardsToDeck(deck, defenseCards, Math.floor(this.minDeckSize * 0.3));
        
        const spellCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.SPELL
        );
        this.addCardsToDeck(deck, spellCards, Math.floor(this.minDeckSize * 0.2));
        
        const itemCards = availableCards.filter(card => 
            card.type === this.cardManager.cardTypes.ITEM
        );
        this.addCardsToDeck(deck, itemCards, Math.floor(this.minDeckSize * 0.1));
        
        this.fillDeckToMinimum(deck, availableCards);
        return deck;
    }
    
    /**
     * Create a combo deck focused on synergies
     */
    createComboDeck(playerCollection) {
        const deck = [];
        const availableCards = this.getAvailableCards(playerCollection);
        
        // Focus on cards with special effects
        const comboCards = availableCards.filter(card => 
            card.effects && card.effects.length > 0
        );
        this.addCardsToDeck(deck, comboCards, Math.floor(this.minDeckSize * 0.5));
        
        // Add card draw
        const drawCards = availableCards.filter(card => 
            card.effects && card.effects.includes("draw_card")
        );
        this.addCardsToDeck(deck, drawCards, Math.floor(this.minDeckSize * 0.3));
        
        // Fill with utility
        this.fillDeckToMinimum(deck, availableCards);
        return deck;
    }
    
    /**
     * Create a balanced starter deck
     */
    createBalancedDeck(playerCollection) {
        return this.createMidrangeDeck(playerCollection);
    }
    
    /**
     * Get available cards from player collection
     */
    getAvailableCards(playerCollection) {
        const availableCards = [];
        
        for (const [cardId, count] of playerCollection) {
            const card = this.cardManager.getCard(cardId);
            if (card && count > 0) {
                // Add card data with available count
                availableCards.push({
                    ...card,
                    availableCount: count
                });
            }
        }
        
        return availableCards;
    }
    
    /**
     * Add cards to deck with constraints
     */
    addCardsToDeck(deck, cardPool, targetCount) {
        // Sort cards by priority (rarity, cost, etc.)
        const sortedCards = cardPool.sort((a, b) => {
            // Prioritize by rarity and cost
            const rarityWeight = this.getRarityWeight(a.rarity) - this.getRarityWeight(b.rarity);
            if (rarityWeight !== 0) return rarityWeight;
            
            return a.cost - b.cost;
        });
        
        let added = 0;
        for (const card of sortedCards) {
            if (added >= targetCount) break;
            
            // Calculate how many copies to add
            const maxCopies = this.getMaxCopiesForCard(card);
            const currentCopies = deck.filter(id => id === card.id).length;
            const availableCopies = card.availableCount - currentCopies;
            const copiesToAdd = Math.min(
                maxCopies - currentCopies,
                availableCopies,
                targetCount - added
            );
            
            for (let i = 0; i < copiesToAdd; i++) {
                deck.push(card.id);
                added++;
            }
        }
    }
    
    /**
     * Fill deck to minimum size
     */
    fillDeckToMinimum(deck, availableCards) {
        while (deck.length < this.minDeckSize) {
            // Find a card we can still add
            let added = false;
            for (const card of availableCards) {
                const currentCopies = deck.filter(id => id === card.id).length;
                const maxCopies = this.getMaxCopiesForCard(card);
                
                if (currentCopies < maxCopies && currentCopies < card.availableCount) {
                    deck.push(card.id);
                    added = true;
                    break;
                }
            }
            
            if (!added) break; // Can't add any more cards
        }
    }
    
    /**
     * Get maximum copies allowed for a card
     */
    getMaxCopiesForCard(card) {
        if (card.rarity === this.cardManager.cardRarities.LEGENDARY) {
            return Math.min(this.maxLegendaryCards, this.maxCopiesPerCard);
        }
        return this.maxCopiesPerCard;
    }
    
    /**
     * Get rarity weight for sorting
     */
    getRarityWeight(rarity) {
        const weights = {
            [this.cardManager.cardRarities.COMMON]: 1,
            [this.cardManager.cardRarities.UNCOMMON]: 2,
            [this.cardManager.cardRarities.RARE]: 3,
            [this.cardManager.cardRarities.LEGENDARY]: 4
        };
        return weights[rarity] || 1;
    }
    
    /**
     * Validate deck composition
     */
    validateDeck(deck, playerCollection) {
        const errors = [];
        const warnings = [];
        
        // Check deck size
        if (deck.length < this.minDeckSize) {
            errors.push(`Deck too small: ${deck.length}/${this.minDeckSize} minimum`);
        }
        if (deck.length > this.maxDeckSize) {
            errors.push(`Deck too large: ${deck.length}/${this.maxDeckSize} maximum`);
        }
        
        // Count cards
        const cardCounts = new Map();
        for (const cardId of deck) {
            cardCounts.set(cardId, (cardCounts.get(cardId) || 0) + 1);
        }
        
        // Check card limits and ownership
        let legendaryCount = 0;
        for (const [cardId, count] of cardCounts) {
            const card = this.cardManager.getCard(cardId);
            if (!card) {
                errors.push(`Unknown card: ${cardId}`);
                continue;
            }
            
            // Check ownership
            const owned = playerCollection.get(cardId) || 0;
            if (count > owned) {
                errors.push(`Not enough copies of ${card.name}: need ${count}, have ${owned}`);
            }
            
            // Check copy limits
            const maxCopies = this.getMaxCopiesForCard(card);
            if (count > maxCopies) {
                errors.push(`Too many copies of ${card.name}: ${count}/${maxCopies} maximum`);
            }
            
            // Count legendaries
            if (card.rarity === this.cardManager.cardRarities.LEGENDARY) {
                legendaryCount += count;
            }
        }
        
        // Check legendary limit
        if (legendaryCount > this.maxLegendaryCards) {
            errors.push(`Too many legendary cards: ${legendaryCount}/${this.maxLegendaryCards} maximum`);
        }
        
        // Analyze deck composition
        const analysis = this.analyzeDeck(deck);
        
        // Add warnings based on analysis
        if (analysis.averageCost > 3.5) {
            warnings.push("Deck has high average cost - may be slow to start");
        }
        if (analysis.averageCost < 1.5) {
            warnings.push("Deck has low average cost - may lack late game power");
        }
        if (analysis.attackPercentage < 0.3) {
            warnings.push("Deck has few attack cards - may struggle to deal damage");
        }
        if (analysis.defensePercentage < 0.2) {
            warnings.push("Deck has few defense cards - may be vulnerable");
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            analysis
        };
    }
    
    /**
     * Analyze deck composition
     */
    analyzeDeck(deck) {
        const cardCounts = new Map();
        let totalCost = 0;
        const typeDistribution = {
            attack: 0,
            defense: 0,
            spell: 0,
            item: 0
        };
        const rarityDistribution = {
            common: 0,
            uncommon: 0,
            rare: 0,
            legendary: 0
        };
        
        for (const cardId of deck) {
            const card = this.cardManager.getCard(cardId);
            if (!card) continue;
            
            cardCounts.set(cardId, (cardCounts.get(cardId) || 0) + 1);
            totalCost += card.cost;
            typeDistribution[card.type]++;
            rarityDistribution[card.rarity]++;
        }
        
        const deckSize = deck.length;
        
        return {
            size: deckSize,
            uniqueCards: cardCounts.size,
            averageCost: totalCost / deckSize,
            attackPercentage: typeDistribution.attack / deckSize,
            defensePercentage: typeDistribution.defense / deckSize,
            spellPercentage: typeDistribution.spell / deckSize,
            itemPercentage: typeDistribution.item / deckSize,
            commonPercentage: rarityDistribution.common / deckSize,
            uncommonPercentage: rarityDistribution.uncommon / deckSize,
            rarePercentage: rarityDistribution.rare / deckSize,
            legendaryPercentage: rarityDistribution.legendary / deckSize,
            typeDistribution,
            rarityDistribution,
            cardCounts
        };
    }
    
    /**
     * Suggest deck improvements
     */
    suggestImprovements(deck, playerCollection) {
        const analysis = this.analyzeDeck(deck);
        const suggestions = [];
        
        // Cost curve suggestions
        if (analysis.averageCost > 3.5) {
            suggestions.push({
                type: "cost",
                message: "Consider adding more low-cost cards for early game",
                priority: "high"
            });
        }
        
        // Type balance suggestions
        if (analysis.attackPercentage < 0.3) {
            suggestions.push({
                type: "balance",
                message: "Add more attack cards to increase damage output",
                priority: "medium"
            });
        }
        
        if (analysis.defensePercentage < 0.2) {
            suggestions.push({
                type: "balance",
                message: "Add more defense cards for survivability",
                priority: "medium"
            });
        }
        
        // Synergy suggestions
        const synergyCards = this.findSynergyCards(deck, playerCollection);
        if (synergyCards.length > 0) {
            suggestions.push({
                type: "synergy",
                message: `Consider adding: ${synergyCards.slice(0, 3).map(c => c.name).join(", ")}`,
                priority: "low"
            });
        }
        
        return suggestions;
    }
    
    /**
     * Find cards that synergize with current deck
     */
    findSynergyCards(deck, playerCollection) {
        const deckCards = deck.map(id => this.cardManager.getCard(id)).filter(Boolean);
        const availableCards = this.getAvailableCards(playerCollection);
        const synergyCards = [];
        
        // Simple synergy detection based on effects and types
        const deckEffects = new Set();
        const deckTypes = new Set();
        
        for (const card of deckCards) {
            deckTypes.add(card.type);
            if (card.effects) {
                card.effects.forEach(effect => deckEffects.add(effect));
            }
        }
        
        for (const card of availableCards) {
            if (deck.includes(card.id)) continue;
            
            let synergyScore = 0;
            
            // Type synergy
            if (deckTypes.has(card.type)) {
                synergyScore += 1;
            }
            
            // Effect synergy
            if (card.effects) {
                for (const effect of card.effects) {
                    if (deckEffects.has(effect)) {
                        synergyScore += 2;
                    }
                }
            }
            
            if (synergyScore > 0) {
                synergyCards.push({ ...card, synergyScore });
            }
        }
        
        return synergyCards.sort((a, b) => b.synergyScore - a.synergyScore);
    }
    
    /**
     * Generate deck code for sharing
     */
    generateDeckCode(deck) {
        // Simple encoding: card counts as base64
        const cardCounts = new Map();
        for (const cardId of deck) {
            cardCounts.set(cardId, (cardCounts.get(cardId) || 0) + 1);
        }
        
        const deckData = Array.from(cardCounts.entries());
        const encoded = btoa(JSON.stringify(deckData));
        return encoded;
    }
    
    /**
     * Parse deck code
     */
    parseDeckCode(deckCode) {
        try {
            const decoded = atob(deckCode);
            const deckData = JSON.parse(decoded);
            const deck = [];
            
            for (const [cardId, count] of deckData) {
                for (let i = 0; i < count; i++) {
                    deck.push(cardId);
                }
            }
            
            return deck;
        } catch (error) {
            console.error("Failed to parse deck code:", error);
            return null;
        }
    }
}
