{"format_version": "1.10.0", "animation_controllers": {"controller.animation.card_duelist.movement": {"initial_state": "idle", "states": {"idle": {"animations": ["idle"], "transitions": [{"walking": "query.modified_move_speed > 0.1"}, {"combat": "query.has_tag('in_combat')"}]}, "walking": {"animations": ["walk"], "transitions": [{"idle": "query.modified_move_speed <= 0.1"}, {"combat": "query.has_tag('in_combat')"}]}, "combat": {"animations": ["combat_stance"], "transitions": [{"idle": "!query.has_tag('in_combat')"}]}}}}}