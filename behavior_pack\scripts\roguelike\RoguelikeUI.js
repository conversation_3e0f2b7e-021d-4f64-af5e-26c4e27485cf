/**
 * RoguelikeUI.js
 * User interface components for the roguelike dungeon system
 */

import { ActionFormData, ModalFormData, MessageFormData } from "@minecraft/server-ui";

export class RoguelikeUI {
    constructor(roguelikeManager, metaProgression, runRewards, runEvents) {
        this.roguelikeManager = roguelikeManager;
        this.metaProgression = metaProgression;
        this.runRewards = runRewards;
        this.runEvents = runEvents;
        
        console.log("🖥️ RoguelikeUI system initialized");
    }
    
    /**
     * Show main dungeon menu
     */
    async showDungeonMenu(player) {
        const form = new ActionFormData()
            .title("🏰 Dungeon Runs")
            .body("Choose your adventure in the depths below!");
        
        const hasActiveRun = this.roguelikeManager.hasActiveRun(player);
        
        if (hasActiveRun) {
            form.button("📊 Current Run Status", "textures/ui/book_edit_default");
            form.button("🚪 Abandon Current Run", "textures/ui/cancel");
        } else {
            form.button("⚔️ Start New Run", "textures/ui/sword_iron");
        }
        
        form.button("🏆 Meta Progression", "textures/ui/trophy");
        form.button("📈 Statistics", "textures/ui/book_writable");
        form.button("🎯 Achievements", "textures/ui/star");
        form.button("❌ Close", "textures/ui/cancel");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            const selection = response.selection;
            let buttonIndex = 0;
            
            if (hasActiveRun) {
                if (selection === buttonIndex++) {
                    this.showRunStatus(player);
                } else if (selection === buttonIndex++) {
                    this.showAbandonConfirmation(player);
                }
            } else {
                if (selection === buttonIndex++) {
                    this.showRunTypeSelection(player);
                }
            }
            
            // Common buttons
            if (selection === buttonIndex++) {
                this.showMetaProgression(player);
            } else if (selection === buttonIndex++) {
                this.showStatistics(player);
            } else if (selection === buttonIndex++) {
                this.showAchievements(player);
            }
            
        } catch (error) {
            player.sendMessage("§cError showing dungeon menu. Please try again.");
        }
    }
    
    /**
     * Show run type selection
     */
    async showRunTypeSelection(player) {
        const form = new ActionFormData()
            .title("⚔️ Select Run Type")
            .body("Choose your difficulty and challenge level:");
        
        const runTypes = this.roguelikeManager.runTypes;
        const metaData = this.metaProgression.getPlayerMetaData(player);
        
        for (const [runType, config] of Object.entries(runTypes)) {
            let buttonText = `${config.name}\n§7${config.description}`;
            let icon = "textures/ui/sword_iron";
            
            // Check if unlocked
            if (config.unlockRequirement && !metaData.unlockedFeatures.includes(config.unlockRequirement)) {
                buttonText += "\n§c🔒 LOCKED";
                icon = "textures/ui/lock";
            } else {
                buttonText += `\n§eFloors: ${config.floors === -1 ? '∞' : config.floors}`;
                buttonText += `\n§6Reward Multiplier: ${config.rewardMultiplier}x`;
            }
            
            form.button(buttonText, icon);
        }
        
        form.button("🔙 Back", "textures/ui/arrow_left");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            const runTypeKeys = Object.keys(runTypes);
            if (response.selection < runTypeKeys.length) {
                const selectedRunType = runTypeKeys[response.selection];
                this.confirmRunStart(player, selectedRunType);
            } else {
                this.showDungeonMenu(player);
            }
            
        } catch (error) {
            player.sendMessage("§cError showing run selection. Please try again.");
        }
    }
    
    /**
     * Confirm run start
     */
    async confirmRunStart(player, runType) {
        const config = this.roguelikeManager.runTypes[runType];
        
        const form = new MessageFormData()
            .title(`🏰 Start ${config.name}?`)
            .body(`Are you ready to begin this dungeon run?\n\n` +
                  `§eFloors: ${config.floors === -1 ? 'Infinite' : config.floors}\n` +
                  `§eDifficulty: ${config.difficultyMultiplier}x\n` +
                  `§eRewards: ${config.rewardMultiplier}x\n\n` +
                  `§7Once started, you can only abandon or complete the run.`)
            .button1("✅ Start Run")
            .button2("❌ Cancel");
        
        try {
            const response = await form.show(player);
            if (response.canceled || response.selection === 1) {
                this.showRunTypeSelection(player);
                return;
            }
            
            // Start the run
            const success = this.roguelikeManager.startRun(player, runType);
            if (!success) {
                this.showDungeonMenu(player);
            }
            
        } catch (error) {
            player.sendMessage("§cError starting run. Please try again.");
        }
    }
    
    /**
     * Show current run status
     */
    async showRunStatus(player) {
        const runData = this.roguelikeManager.getPlayerRun(player);
        if (!runData) {
            player.sendMessage("§cNo active run found.");
            return;
        }
        
        const statusText = this.formatRunStatus(runData);
        
        const form = new ActionFormData()
            .title("📊 Current Run Status")
            .body(statusText);
        
        form.button("🎒 View Inventory", "textures/ui/inventory_icon");
        form.button("🏺 View Artifacts", "textures/ui/brewing_stand");
        form.button("📜 View Cards", "textures/ui/book_edit_default");
        form.button("🔙 Back", "textures/ui/arrow_left");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            switch (response.selection) {
                case 0:
                    this.showRunInventory(player);
                    break;
                case 1:
                    this.showRunArtifacts(player);
                    break;
                case 2:
                    this.showRunCards(player);
                    break;
                case 3:
                    this.showDungeonMenu(player);
                    break;
            }
            
        } catch (error) {
            player.sendMessage("§cError showing run status. Please try again.");
        }
    }
    
    /**
     * Show run inventory
     */
    async showRunInventory(player) {
        const runData = this.roguelikeManager.getPlayerRun(player);
        if (!runData) return;
        
        const inventoryText = this.formatRunInventory(runData);
        
        const form = new MessageFormData()
            .title("🎒 Run Inventory")
            .body(inventoryText)
            .button1("🔙 Back")
            .button2("❌ Close");
        
        try {
            const response = await form.show(player);
            if (response.selection === 0) {
                this.showRunStatus(player);
            }
        } catch (error) {
            player.sendMessage("§cError showing inventory.");
        }
    }
    
    /**
     * Show meta progression
     */
    async showMetaProgression(player) {
        const metaData = this.metaProgression.getPlayerMetaData(player);
        const { available, locked } = this.metaProgression.getAvailableUnlocks(player);
        
        let bodyText = `§6Meta Points: ${metaData.metaPoints}\n\n`;
        bodyText += `§aUnlocked Features: ${metaData.unlockedFeatures.length}\n`;
        bodyText += `§eAchievements: ${metaData.achievements.length}\n\n`;
        
        if (available.length > 0) {
            bodyText += "§6Available Unlocks:\n";
            available.slice(0, 3).forEach(unlock => {
                bodyText += `§e• ${unlock.name}\n`;
            });
            if (available.length > 3) {
                bodyText += `§7... and ${available.length - 3} more\n`;
            }
        }
        
        const form = new ActionFormData()
            .title("🏆 Meta Progression")
            .body(bodyText);
        
        form.button("🔓 View Unlocks", "textures/ui/key");
        form.button("📊 View Statistics", "textures/ui/book_writable");
        form.button("🔙 Back", "textures/ui/arrow_left");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            switch (response.selection) {
                case 0:
                    this.showUnlocks(player);
                    break;
                case 1:
                    this.showStatistics(player);
                    break;
                case 2:
                    this.showDungeonMenu(player);
                    break;
            }
            
        } catch (error) {
            player.sendMessage("§cError showing meta progression.");
        }
    }
    
    /**
     * Show achievements
     */
    async showAchievements(player) {
        const { earned, available } = this.metaProgression.getPlayerAchievements(player);
        
        let bodyText = `§6Achievements Earned: ${earned.length}\n\n`;
        
        if (earned.length > 0) {
            bodyText += "§a✅ Completed:\n";
            earned.slice(0, 5).forEach(achievement => {
                bodyText += `§e• ${achievement.name} (${achievement.points} pts)\n`;
            });
            if (earned.length > 5) {
                bodyText += `§7... and ${earned.length - 5} more\n`;
            }
        }
        
        if (available.length > 0) {
            bodyText += "\n§7🎯 Available:\n";
            available.slice(0, 3).forEach(achievement => {
                bodyText += `§7• ${achievement.name} (${achievement.points} pts)\n`;
            });
        }
        
        const form = new MessageFormData()
            .title("🎯 Achievements")
            .body(bodyText)
            .button1("🔙 Back")
            .button2("❌ Close");
        
        try {
            const response = await form.show(player);
            if (response.selection === 0) {
                this.showDungeonMenu(player);
            }
        } catch (error) {
            player.sendMessage("§cError showing achievements.");
        }
    }
    
    /**
     * Show abandon confirmation
     */
    async showAbandonConfirmation(player) {
        const form = new MessageFormData()
            .title("🚪 Abandon Run?")
            .body("Are you sure you want to abandon your current dungeon run?\n\n" +
                  "§cYou will lose all progress and temporary rewards from this run.\n" +
                  "§7You will keep any meta progression points already earned.")
            .button1("✅ Yes, Abandon")
            .button2("❌ No, Continue");
        
        try {
            const response = await form.show(player);
            if (response.canceled || response.selection === 1) {
                this.showDungeonMenu(player);
                return;
            }
            
            this.roguelikeManager.abandonRun(player);
            this.showDungeonMenu(player);
            
        } catch (error) {
            player.sendMessage("§cError abandoning run.");
        }
    }
    
    /**
     * Format run status for display
     */
    formatRunStatus(runData) {
        const floorText = runData.maxFloor === -1 ? '∞' : runData.maxFloor;
        
        return `§6${runData.config.name}\n\n` +
               `§eFloor: ${runData.currentFloor}/${floorText}\n` +
               `§cHealth: ${runData.runHealth}/${runData.maxRunHealth}\n` +
               `§bEnergy: ${runData.runEnergy}/${runData.maxRunEnergy}\n` +
               `§eGold: ${runData.runGold || 0}\n\n` +
               `§6Progress:\n` +
               `§7• Enemies Defeated: ${runData.enemiesDefeated || 0}\n` +
               `§7• Cards Collected: ${runData.cardsCollected || 0}\n` +
               `§7• Events Completed: ${runData.eventsCompleted || 0}\n` +
               `§7• Artifacts: ${(runData.activeArtifacts || []).length}`;
    }
    
    /**
     * Format run inventory for display
     */
    formatRunInventory(runData) {
        let text = `§6Gold: ${runData.runGold || 0}\n\n`;
        
        if (runData.temporaryCards && runData.temporaryCards.length > 0) {
            text += `§bTemporary Cards (${runData.temporaryCards.length}):\n`;
            runData.temporaryCards.slice(0, 8).forEach(card => {
                text += `§7• ${card.name} (${card.rarity})\n`;
            });
            if (runData.temporaryCards.length > 8) {
                text += `§7... and ${runData.temporaryCards.length - 8} more\n`;
            }
        }
        
        if (runData.activeArtifacts && runData.activeArtifacts.length > 0) {
            text += `\n§dArtifacts (${runData.activeArtifacts.length}):\n`;
            runData.activeArtifacts.forEach(artifact => {
                text += `§7• ${artifact.name}\n`;
            });
        }
        
        return text || "§7Your inventory is empty.";
    }
    
    /**
     * Show event choice interface
     */
    async showEventChoice(player, event) {
        if (event.isShop) {
            return this.showShopInterface(player, event);
        }
        
        const form = new ActionFormData()
            .title(`✨ ${event.name}`)
            .body(event.description);
        
        event.outcomes.forEach(outcome => {
            let buttonText = outcome.name;
            if (outcome.cost) {
                buttonText += `\n§c(Cost: ${this.formatCost(outcome.cost)})`;
            }
            buttonText += `\n§7${outcome.description}`;
            
            form.button(buttonText, "textures/ui/book_edit_default");
        });
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            const runData = this.roguelikeManager.getPlayerRun(player);
            this.runEvents.handleEventChoice(player, response.selection + 1, runData);
            
        } catch (error) {
            player.sendMessage("§cError showing event choice.");
        }
    }
    
    /**
     * Show shop interface
     */
    async showShopInterface(player, event) {
        const runData = this.roguelikeManager.getPlayerRun(player);
        
        const form = new ActionFormData()
            .title(`🏪 ${event.name}`)
            .body(`${event.description}\n\n§eYour Gold: ${runData.runGold || 0}`);
        
        event.inventory.forEach(item => {
            let buttonText = this.formatShopItem(item);
            buttonText += `\n§6Price: ${item.price} Gold`;
            
            if ((runData.runGold || 0) < item.price) {
                buttonText += "\n§c(Can't afford)";
            }
            
            form.button(buttonText, "textures/ui/coin");
        });
        
        form.button("🚪 Leave Shop", "textures/ui/arrow_left");
        
        try {
            const response = await form.show(player);
            if (response.canceled) return;
            
            if (response.selection < event.inventory.length) {
                this.runEvents.handleShopPurchase(player, response.selection + 1, runData);
                // Show shop again after purchase
                setTimeout(() => this.showShopInterface(player, event), 1000);
            } else {
                // Leave shop
                event.completed = true;
            }
            
        } catch (error) {
            player.sendMessage("§cError showing shop interface.");
        }
    }
    
    /**
     * Format cost for display
     */
    formatCost(cost) {
        switch (cost.type) {
            case "gold":
                return `${cost.value} Gold`;
            case "health":
                return `${cost.value} Health`;
            case "remove_card":
                return "Remove a card";
            default:
                return "Unknown cost";
        }
    }
    
    /**
     * Format shop item for display
     */
    formatShopItem(item) {
        switch (item.type) {
            case "card":
                return `${item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)} Card`;
            case "artifact":
                return `${item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)} Artifact`;
            default:
                return item.description;
        }
    }
    
    /**
     * Show unlocks interface
     */
    async showUnlocks(player) {
        const { available, locked } = this.metaProgression.getAvailableUnlocks(player);
        
        let bodyText = "";
        
        if (available.length > 0) {
            bodyText += "§a✅ Unlocked Features:\n";
            available.forEach(unlock => {
                bodyText += `§e• ${unlock.name}\n`;
            });
        }
        
        if (locked.length > 0) {
            bodyText += "\n§c🔒 Locked Features:\n";
            locked.slice(0, 5).forEach(unlock => {
                bodyText += `§7• ${unlock.name}\n`;
            });
        }
        
        const form = new MessageFormData()
            .title("🔓 Unlocks")
            .body(bodyText || "§7No unlocks available.")
            .button1("🔙 Back")
            .button2("❌ Close");
        
        try {
            const response = await form.show(player);
            if (response.selection === 0) {
                this.showMetaProgression(player);
            }
        } catch (error) {
            player.sendMessage("§cError showing unlocks.");
        }
    }
    
    /**
     * Show statistics interface
     */
    async showStatistics(player) {
        const statusText = this.metaProgression.displayMetaStatus(player);
        
        const form = new MessageFormData()
            .title("📊 Statistics")
            .body(statusText)
            .button1("🔙 Back")
            .button2("❌ Close");
        
        try {
            const response = await form.show(player);
            if (response.selection === 0) {
                this.showDungeonMenu(player);
            }
        } catch (error) {
            player.sendMessage("§cError showing statistics.");
        }
    }
}
