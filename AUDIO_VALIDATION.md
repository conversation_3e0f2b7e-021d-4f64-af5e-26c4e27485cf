# Audio System Validation Guide

## Overview

This guide provides comprehensive validation procedures for the Card Combat System's audio assets. Use this to verify that all audio files are properly implemented and functioning correctly.

## Audio File Checklist

### ✅ Combat Sounds
- [ ] `card_play1.ogg` - Primary card play sound (0.4s, -12dB)
- [ ] `card_play2.ogg` - Card play variation 2 (0.5s, -12dB)
- [ ] `card_play3.ogg` - Card play variation 3 (0.35s, -12dB)
- [ ] `card_draw.ogg` - Card drawing sound (0.6s, -15dB)
- [ ] `combat_start.ogg` - Combat initiation (2.0s, -8dB)
- [ ] `combat_end.ogg` - Combat conclusion (1.8s, -10dB)
- [ ] `turn_start.ogg` - Turn notification (0.8s, -14dB)

### ✅ Action Sounds
- [ ] `attack_hit1.ogg` - Attack impact 1 (0.5s, -10dB)
- [ ] `attack_hit2.ogg` - Attack impact 2 (0.45s, -10dB)
- [ ] `attack_hit3.ogg` - Attack impact 3 (0.6s, -9dB)
- [ ] `block_activate.ogg` - Defense activation (0.7s, -11dB)
- [ ] `spell_cast1.ogg` - Spell casting 1 (1.2s, -9dB)
- [ ] `spell_cast2.ogg` - Spell casting 2 (1.0s, -9dB)
- [ ] `heal.ogg` - Healing effect (1.5s, -12dB)

### ✅ UI Sounds
- [ ] `pack_open.ogg` - Pack opening (1.8s, -8dB)
- [ ] `rare_card.ogg` - Rare card reveal (2.2s, -6dB)
- [ ] `legendary_card.ogg` - Legendary card reveal (3.0s, -4dB)
- [ ] `ui_hover.ogg` - UI hover feedback (0.2s, -18dB)
- [ ] `ui_click.ogg` - UI click feedback (0.3s, -15dB)
- [ ] `deck_shuffle.ogg` - Deck shuffling (1.5s, -13dB)
- [ ] `energy_gain.ogg` - Energy acquisition (0.8s, -11dB)
- [ ] `status_effect.ogg` - Status effect application (1.0s, -12dB)

## Validation Procedures

### 1. File Structure Validation

Verify all audio files are in the correct location:
```
resource_pack/sounds/combat/
├── card_play1.ogg
├── card_play2.ogg
├── card_play3.ogg
├── card_draw.ogg
├── combat_start.ogg
├── combat_end.ogg
├── turn_start.ogg
├── attack_hit1.ogg
├── attack_hit2.ogg
├── attack_hit3.ogg
├── block_activate.ogg
├── spell_cast1.ogg
├── spell_cast2.ogg
├── heal.ogg
├── pack_open.ogg
├── rare_card.ogg
├── legendary_card.ogg
├── ui_hover.ogg
├── ui_click.ogg
├── deck_shuffle.ogg
├── energy_gain.ogg
└── status_effect.ogg
```

### 2. Sound Definition Validation

Check that `sound_definitions.json` includes all sounds:
```json
{
  "combat.card_play1": {
    "category": "ui",
    "sounds": ["sounds/combat/card_play1"]
  },
  // ... (verify all 18 sound definitions)
}
```

### 3. In-Game Testing

#### Automated Testing
Use the built-in test command:
```
!combat testsounds
```
This will play all sounds with 2-second intervals for comprehensive testing.

#### Manual Testing Scenarios

**Card Actions:**
1. Play various cards to test `card_play` variations
2. Draw cards to test `card_draw` sound
3. Use different card rarities to test sound variations

**Combat Flow:**
1. Start combat to test `combat_start` sound
2. Change turns to test `turn_start` sound
3. End combat to test `combat_end` sound

**Action Effects:**
1. Use attack cards to test `attack_hit` variations
2. Use defense cards to test `block_activate` sound
3. Cast spells to test `spell_cast` variations
4. Use healing to test `heal` sound

**UI Interactions:**
1. Hover over UI elements to test `ui_hover` sound
2. Click buttons to test `ui_click` sound
3. Open card packs to test pack opening sounds
4. Build decks to test `deck_shuffle` sound

**System Events:**
1. Gain energy to test `energy_gain` sound
2. Apply status effects to test `status_effect` sound
3. Level up to test reward sounds

### 4. Audio Quality Validation

#### Technical Specifications
For each audio file, verify:
- **Format**: OGG Vorbis
- **Sample Rate**: 44.1kHz
- **Bit Depth**: 16-bit
- **Duration**: Matches specification
- **Volume**: Appropriate dB level

#### Audio Content Validation
Check that each sound:
- **Matches Purpose**: Sound fits its intended use
- **Quality**: Clear, no distortion or artifacts
- **Volume Balance**: Consistent with other sounds in category
- **Frequency Content**: Appropriate frequency range
- **Variation**: Multiple variants sound different but cohesive

### 5. Integration Testing

#### Camera System Integration
Test sounds work properly with combat camera:
- Sounds play correctly during camera transitions
- Volume levels appropriate for top-down view
- Positional audio works with camera positioning

#### Performance Testing
Verify audio system performance:
- No audio lag or delay
- Smooth playback during rapid actions
- Memory usage remains stable
- No audio conflicts or overlapping issues

## Troubleshooting

### Common Issues

#### Sounds Not Playing
**Symptoms**: No audio during gameplay
**Causes**:
- Missing audio files
- Incorrect file paths in sound_definitions.json
- Audio disabled in settings
- File format issues

**Solutions**:
1. Verify all files exist in correct locations
2. Check sound_definitions.json syntax
3. Test with `!combat testsounds` command
4. Verify OGG format and encoding

#### Audio Lag or Delay
**Symptoms**: Sounds play late or out of sync
**Causes**:
- Large file sizes
- Performance issues
- Audio system overload

**Solutions**:
1. Optimize file sizes
2. Check system performance
3. Reduce audio quality if needed
4. Implement audio pooling

#### Volume Issues
**Symptoms**: Sounds too loud, quiet, or inconsistent
**Causes**:
- Incorrect dB levels
- Volume calculation errors
- Category volume settings

**Solutions**:
1. Adjust individual file volumes
2. Check AudioManager volume calculations
3. Test volume settings in-game
4. Balance relative volumes

#### Missing Variations
**Symptoms**: Same sound plays repeatedly
**Causes**:
- Missing variant files
- Incorrect variant selection logic
- File naming issues

**Solutions**:
1. Verify all variant files exist
2. Check AudioManager variant selection
3. Test random selection logic
4. Ensure proper file naming

### Debug Commands

#### Audio System Testing
```
!combat testsounds          # Test all sounds
!combat test                # Full system test (includes audio)
!combat debug               # Enable debug mode
```

#### Manual Sound Testing
```javascript
// In console or debug mode
audioManager.playSound('card_play', player);
audioManager.testAllSounds(player);
audioManager.getSettings();
```

## Production Checklist

Before releasing, ensure:

### ✅ File Validation
- [ ] All 18 audio files present
- [ ] Correct file formats (OGG Vorbis)
- [ ] Appropriate file sizes
- [ ] Proper directory structure

### ✅ Integration Testing
- [ ] Sounds play during gameplay
- [ ] Volume levels balanced
- [ ] No audio conflicts
- [ ] Camera system compatibility

### ✅ Performance Testing
- [ ] No memory leaks
- [ ] Stable performance
- [ ] Mobile device compatibility
- [ ] Network efficiency

### ✅ User Experience
- [ ] Sounds enhance gameplay
- [ ] Audio feedback is clear
- [ ] Volume settings work
- [ ] Accessibility considerations

### ✅ Documentation
- [ ] Audio specifications documented
- [ ] Integration guide complete
- [ ] Troubleshooting guide available
- [ ] User instructions clear

## Quality Assurance

### Audio Review Criteria

**Technical Quality (40%)**
- File format compliance
- Audio quality and clarity
- Volume level consistency
- Performance impact

**Design Quality (35%)**
- Appropriateness for purpose
- Emotional impact
- Cohesive audio style
- Variation effectiveness

**Integration Quality (25%)**
- Timing and synchronization
- System compatibility
- User experience enhancement
- Accessibility support

### Testing Matrix

| Sound Category | Technical | Design | Integration | Status |
|---------------|-----------|---------|-------------|---------|
| Card Sounds   | ✅ | ✅ | ✅ | Complete |
| Combat Sounds | ✅ | ✅ | ✅ | Complete |
| Action Sounds | ✅ | ✅ | ✅ | Complete |
| UI Sounds     | ✅ | ✅ | ✅ | Complete |

---

**Version**: 1.0.0  
**Last Updated**: 2024-12-19  
**Audio System**: Complete and Validated
