import * as core from "zod/v4/core";
import * as schemas from "./schemas.js";
export interface ZodMiniISODateTime extends schemas.ZodMiniStringFormat<"datetime"> {
    _zod: core.$ZodISODateTimeInternals;
}
export declare const ZodMiniISODateTime: core.$constructor<ZodMiniISODateTime>;
export declare function datetime(params?: string | core.$ZodISODateTimeParams): ZodMiniISODateTime;
export interface ZodMiniISODate extends schemas.ZodMiniStringFormat<"date"> {
    _zod: core.$ZodISODateInternals;
}
export declare const ZodMiniISODate: core.$constructor<ZodMiniISODate>;
export declare function date(params?: string | core.$ZodISODateParams): ZodMiniISODate;
export interface ZodMiniISOTime extends schemas.ZodMiniStringFormat<"time"> {
    _zod: core.$ZodISOTimeInternals;
}
export declare const ZodMiniISOTime: core.$constructor<ZodMiniISOTime>;
export declare function time(params?: string | core.$ZodISOTimeParams): ZodMiniISOTime;
export interface ZodMiniISODuration extends schemas.ZodMiniStringFormat<"duration"> {
    _zod: core.$ZodISODurationInternals;
}
export declare const ZodMiniISODuration: core.$constructor<ZodMiniISODuration>;
export declare function duration(params?: string | core.$ZodISODurationParams): ZodMiniISODuration;
