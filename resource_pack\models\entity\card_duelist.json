{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.card_duelist", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 2, "visible_bounds_height": 2, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "body", "parent": "root", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}]}, {"name": "head", "parent": "body", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "hat", "parent": "head", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [32, 0], "inflate": 0.5}]}, {"name": "rightArm", "parent": "body", "pivot": [-5, 22, 0], "cubes": [{"origin": [-8, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "leftArm", "parent": "body", "pivot": [5, 22, 0], "cubes": [{"origin": [4, 12, -2], "size": [4, 12, 4], "uv": [32, 48]}]}, {"name": "rightLeg", "parent": "root", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-3.9, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}, {"name": "leftLeg", "parent": "root", "pivot": [1.9, 12, 0], "cubes": [{"origin": [-0.1, 0, -2], "size": [4, 12, 4], "uv": [16, 48]}]}, {"name": "cardHand", "parent": "leftArm", "pivot": [6, 15, 0], "cubes": [{"origin": [5, 14, -1], "size": [2, 3, 0.1], "uv": [0, 32]}]}, {"name": "staff", "parent": "rightArm", "pivot": [-6, 15, 0], "cubes": [{"origin": [-6.5, 8, -0.5], "size": [1, 16, 1], "uv": [56, 0]}, {"origin": [-7, 22, -1], "size": [2, 2, 2], "uv": [56, 16]}]}]}]}