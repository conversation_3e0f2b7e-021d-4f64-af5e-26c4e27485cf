{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "card_combat:card_duelist", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"card_combat:novice_duelist": {"minecraft:health": {"value": 15, "max": 15}, "minecraft:attack": {"damage": 2}, "minecraft:loot": {"table": "loot_tables/entities/card_duelist_novice.json"}}, "card_combat:expert_duelist": {"minecraft:health": {"value": 25, "max": 25}, "minecraft:attack": {"damage": 4}, "minecraft:loot": {"table": "loot_tables/entities/card_duelist_expert.json"}}, "card_combat:master_duelist": {"minecraft:health": {"value": 40, "max": 40}, "minecraft:attack": {"damage": 6}, "minecraft:loot": {"table": "loot_tables/entities/card_duelist_master.json"}}, "card_combat:in_combat": {"minecraft:movement": {"value": 0.0}, "minecraft:behavior.nearest_attackable_target": {"priority": 1, "must_see": false, "reselect_targets": false, "within_radius": 0.0, "entity_types": []}}, "card_combat:combat_stance": {"minecraft:scale": {"value": 1.1}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["card_duelist", "npc", "mob"]}, "minecraft:collision_box": {"width": 0.6, "height": 1.8}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:nameable": {}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 1.25}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 16.0, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "player"}, "max_dist": 20}]}, "minecraft:behavior.melee_attack": {"priority": 3, "speed_multiplier": 1.0, "reach_multiplier": 1.0}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 8.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:attack": {"damage": 3}, "minecraft:equipment": {"table": "loot_tables/entities/card_duelist_equipment.json"}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? <PERSON><PERSON>(5,15) : 0"}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_tag", "subject": "self", "operator": "!=", "value": "in_combat"}]}, "event": "card_combat:challenge_player"}, "interact_text": "action.interact.challenge_duel"}]}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 60, "trigger": "card_combat:spawn_novice"}, {"weight": 30, "trigger": "card_combat:spawn_expert"}, {"weight": 10, "trigger": "card_combat:spawn_master"}]}, "card_combat:spawn_novice": {"add": {"component_groups": ["card_combat:novice_duelist"]}, "run_command": {"command": ["tag @s add novice_duelist", "tag @s add card_duelist"]}}, "card_combat:spawn_expert": {"add": {"component_groups": ["card_combat:expert_duelist"]}, "run_command": {"command": ["tag @s add expert_duelist", "tag @s add card_duelist"]}}, "card_combat:spawn_master": {"add": {"component_groups": ["card_combat:master_duelist"]}, "run_command": {"command": ["tag @s add master_duelist", "tag @s add card_duelist"]}}, "card_combat:challenge_player": {"run_command": {"command": ["tellraw @a[r=10] {\"text\":\"Card Duelist challenges you to a duel!\",\"color\":\"gold\"}", "tag @s add challenging"]}}, "card_combat:start_combat": {"add": {"component_groups": ["card_combat:in_combat", "card_combat:combat_stance"]}, "run_command": {"command": ["tag @s add in_combat", "tellraw @a[r=10] {\"text\":\"Combat begins!\",\"color\":\"red\"}"]}}, "card_combat:end_combat": {"remove": {"component_groups": ["card_combat:in_combat", "card_combat:combat_stance"]}, "run_command": {"command": ["tag @s remove in_combat", "tag @s remove challenging"]}}, "card_combat:ai_turn": {"run_command": {"command": ["tellraw @a[r=10] {\"text\":\"Card Duelist plays a card!\",\"color\":\"blue\"}"]}}, "card_combat:victory": {"run_command": {"command": ["tellraw @a[r=10] {\"text\":\"Card Duelist wins the duel!\",\"color\":\"red\"}", "give @a[r=5] emerald 1"]}}, "card_combat:defeat": {"run_command": {"command": ["tellraw @a[r=10] {\"text\":\"Card Duelist is defeated!\",\"color\":\"green\"}", "give @a[r=5] card_combat:card_pack 1"]}}}}}