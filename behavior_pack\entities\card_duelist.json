{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "card_combat:card_duelist", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"card_combat:novice_duelist": {"minecraft:health": {"value": 15, "max": 15}, "minecraft:loot": {"table": "loot_tables/entities/card_duelist_novice.json"}, "minecraft:variant": {"value": 0}}, "card_combat:expert_duelist": {"minecraft:health": {"value": 25, "max": 25}, "minecraft:loot": {"table": "loot_tables/entities/card_duelist_expert.json"}, "minecraft:variant": {"value": 1}}, "card_combat:master_duelist": {"minecraft:health": {"value": 40, "max": 40}, "minecraft:loot": {"table": "loot_tables/entities/card_duelist_master.json"}, "minecraft:variant": {"value": 2}}, "card_combat:in_combat": {"minecraft:movement": {"value": 0.0}}, "card_combat:combat_stance": {"minecraft:scale": {"value": 1.1}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["card_duelist", "npc", "mob", "card_combat_enabled"]}, "minecraft:collision_box": {"width": 0.6, "height": 1.8}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:health": {"value": 20, "max": 20}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:nameable": {}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 1.25}, "minecraft:behavior.follow_player": {"priority": 2, "speed_multiplier": 1.0, "start_distance": 10, "stop_distance": 2}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 8.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:equipment": {"table": "loot_tables/entities/card_duelist_equipment.json"}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? <PERSON><PERSON>(5,15) : 0"}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_tag", "subject": "self", "operator": "!=", "value": "in_combat"}]}, "event": "card_combat:challenge_player"}, "interact_text": "action.interact.challenge_duel"}]}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 60, "trigger": "card_combat:spawn_novice"}, {"weight": 30, "trigger": "card_combat:spawn_expert"}, {"weight": 10, "trigger": "card_combat:spawn_master"}]}, "card_combat:spawn_novice": {"add": {"component_groups": ["card_combat:novice_duelist"]}}, "card_combat:spawn_expert": {"add": {"component_groups": ["card_combat:expert_duelist"]}}, "card_combat:spawn_master": {"add": {"component_groups": ["card_combat:master_duelist"]}}, "card_combat:challenge_player": {}, "card_combat:start_combat": {"add": {"component_groups": ["card_combat:in_combat", "card_combat:combat_stance"]}}, "card_combat:end_combat": {"remove": {"component_groups": ["card_combat:in_combat", "card_combat:combat_stance"]}}, "card_combat:ai_turn": {}, "card_combat:victory": {}, "card_combat:defeat": {}}}}